腾讯云 SES 控制台前端项目
======================

## 开发工具

本项目依赖 tea-cli 进行工程化开发，请先安装。

```
npm i @tencent/tea-cli -g --registry=http://r.tnpm.oa.com --proxy=http://127.0.0.1:12639
```

## 开发

使用 tea dev 命令启动本地开发服务器：

```
tea dev
```

然后需要使用 Whistle 代理本地开发，代理规则如下：

```
/\/qcloud\/tea\/app\/([\w-]+)(\.(zh|en|jp|ko|dev))?(\.\w{10})?\.(js|css)(\.map)?/ http://127.0.0.1:8322/$1.$5$6
/\/qcloud\/tea\/app\/(.+\/)?__webpack_hmr$/ http://127.0.0.1:8322/__webpack_hmr
/\/qcloud\/tea\/app\/(.+\.)?(\w+)\.(hot-update\.(js|json))$/ http://127.0.0.1:8322/$1$2.$3
```

完整的原理和流程可以参考 http://tapd.oa.com/tcp_access/markdown_wikis/view/#1020399462010220759

注意：Whistle 需要配置证书，以及勾选https选项中的Capture TUNNEL CONNECTs

## 组件

使用 Tea 组件进行开发，文档地址：http://tea.tencent.com/component

## 接口

跟控制台交互的部分，使用 tea-app 进行，文档地址：http://tea.tencent.com/app

## 构建

```
tea build
```

## 国际化
使用t函数或者标签来标记需要国际化的字符，文档地址：http://tapd.woa.com/tcp_access/markdown_wikis/show/#1220399462000785723

```
tea scan // 扫描找出待翻译词条
tea dict push -l en // 提交英文翻译词条，生成tapd单
tea dict pull // 拉取翻译好的词条
npm run dev-en // 本地开发使用英文词条
```
## 提交版本

提交版本前请先构建。

```
tea commit -m '修改说明'
```

提交后到 Buffet 系统发布即可。

## 目录规范

- `src/app.ts` 入口文件，主要进行业务路由定义
- `src/routes` 存放业务路由实现
- `src/configs` 存放 CSS 和菜单配置
- `src/components` 存放公共业务组件
- `src/utils` 存放公共工具方法