declare module '*.module.less' {
  const classes: { [key: string]: string };
  export default classes;
}
declare module '*.svg' {
  const content: any;
  export default content;
}

declare module '*.png' {
  const content: any;
  export default content;
}

declare module '*.md';

declare interface Window {
  QCBUY_HOST: string; // 购买页域名
  QCMAIN_HOST: string; // 主站域名
  QCCONSOLE_HOST: string; // 控制台域名
  QCLOUD_ROOT_HOST: string; // 控制台域名
}
