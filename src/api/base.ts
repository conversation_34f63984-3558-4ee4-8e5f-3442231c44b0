import { app } from '@tea/app';
import { isIntl } from '@src/utils/CommonUtils';
import {
  SELECTED_REGION_STORAGE_KEY,
  REGIONS_IN_CHINA,
} from '@src/constants/defaultConstant';
import { RequestV3Options } from '@tencent/tea-app/lib/bridge';
// import { fakeCall } from "@src/api/fake";

const REGION_ID = {
  HK: 5,
  SG: 9,
  GZ: 1,
};

const REGION_ID_NAME_MAP = {
  [REGION_ID.HK]: 'ap-hongkong',
  [REGION_ID.SG]: 'ap-singapore',
  [REGION_ID.GZ]: 'ap-guangzhou',
};

const getRegionId = () => {
  if (isIntl) return REGION_ID.SG;
  const selectedRegion = localStorage.getItem(SELECTED_REGION_STORAGE_KEY);
  if (!selectedRegion || !REGIONS_IN_CHINA[selectedRegion]) return REGION_ID.HK;
  return REGIONS_IN_CHINA[selectedRegion].regionId;
};

export const getRegionName = () => {
  const regionId = getRegionId();
  return REGION_ID_NAME_MAP[regionId];
};

const DEFAULT_CAPI_BODY = {
  // regionId: getCurrentUserArea() === AREA_TYPE.MAINLAND ? REGION_ID.HK : REGION_ID.SG,
  regionId: getRegionId(),
  serviceType: 'ses',
  // data: {Offset: 0, Limit: 20}
};

const DEFAULT_DATA = {
  Version: '2020-10-02',
};

const DEFAULT_CAPI_OPTIONS = {
  tipErr: false, // 接口错误时的顶部自动弹窗，目前时有时无，先隐藏了改为手动提示
};

const requestApiV3 = async (
  cmd: string,
  data: any,
  options: any = {},
  capiOptions: RequestV3Options = {},
) => {
  // console.log('selectedRegionId--', getRegionId());
  const requestBody = {
    ...DEFAULT_CAPI_BODY,
    ...options,
    cmd,
    data: { ...DEFAULT_DATA, ...data },
  };

  const mergeCapiOptions = { ...DEFAULT_CAPI_OPTIONS, ...capiOptions };

  let response: any = {};
  // if (API_CONFIG.fake) {
  //   response = await fakeCall(requestBody);
  // } else {
  response = await app.capi.requestV3(requestBody, mergeCapiOptions);
  if (
    !response ||
    response.code !== 0 ||
    !response.data ||
    !response.data.Response
  )
    throw response;
  // }
  return response.data.Response;
};

export { requestApiV3 };
