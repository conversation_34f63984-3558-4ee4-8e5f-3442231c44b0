export const SES_CMD = {
  LIST_DOMAIN: 'ListEmailIdentities',
  ADD_DOMAIN: 'CreateEmailIdentity',
  GET_DOMAIN: 'GetEmailIdentity',
  VERIFY_DOMAIN: 'UpdateEmailIdentity',
  DELETE_DOMAIN: 'DeleteEmailIdentity',
  UPDATE_SMTP_PASSWORD: 'UpdateEmailSmtpPassWord',

  ADD_ADDRESS: 'CreateEmailAddress',
  LIST_ADDRESS: 'ListEmailAddress',
  DELETE_ADDRESS: 'DeleteEmailAddress',

  ADD_EMAIL_TEMPLATE: 'CreateEmailTemplate',
  LIST_EMAIL_TEMPLATE: 'ListEmailTemplates',
  GET_EMAIL_TEMPLATE: 'GetEmailTemplate',
  UPDATE_EMAIL_TEMPLATE: 'UpdateEmailTemplate',
  DELETE_EMAIL_TEMPLATE: 'DeleteEmailTemplate',

  UPDATE_WEBHOOK: 'UpdateWebhookUrl',
  // 发信域名对应回调地址列表
  LIST_ADDRESS_TRACK_URL: 'ListAddressTrackUrl',
  // 删除
  DELETE_ADDRESS_TRACK_URL: 'DeleteAddressTrackUrl',
  // 新增
  CREATE_ADDRESS_TRACK_URL: 'CreateAddressTrackUrl',
  // 更新
  UPDATE_ADDRESS_TRACK_URL: 'UpdateAddressTrackUrl',

  SEND_EMAIL: 'SendEmail',
  EMAIL_SIMULATOR: 'EmailSimulator',

  // 暂时没有发送任务，下面3个暂时不用
  ADD_TASK: 'CreateBulkTask',
  LIST_TASK: 'ListBulkTask',
  TASK_SEND: 'SendBulkTask',

  OPEN_ACCOUNT: 'OpenAccount',
  QUERY_ACCOUNT: 'QueryAccount',
  ACCOUNT_PACKAGE: 'ListAccountPackage',

  GET_STATISTICS_REPORT: 'GetStatisticsReport',
  LIST_BLACKLIST: 'ListBlackEmailAddress',
  DELETE_BLACKLIST: 'DeleteBlackList',
  GET_GLOBAL_BLACKLIST_ADDRESS: 'GetGlobalBlacklistAddress',
  CREATE_WHITELIST_FROM_BLACKLIST: 'CreateWhitelistFromBlacklist',
  // 黑名单-拒收
  LIST_REJECTED_RECIPIENTS: 'ListRejectedRecipients',

  // 批量发送
  LIST_RECEIVERS: 'ListReceivers',
  CREATE_RECEIVER: 'CreateReceiver',
  CREATE_RECEIVER_DETAIL: 'CreateReceiverDetail',
  DELETE_RECEIVER: 'DeleteReceiver',
  LIST_SEND_TASKS: 'ListSendTasks',
  BATCH_SEND_EMAIL: 'BatchSendEmail',
  DELETE_SEND_TASK: 'DeleteSendTask',
  // 通过cos上传收件人文件
  UPLOAD_RECEIVERS_DETAIL_BY_COS: 'UploadReceiversDetailByCOS',
  // 收件人导出下载
  DOWNLOAD_RECIPIENT_DETAIL: 'DownloadRecipientDetail',
  // 批量收件人带模板参数
  CREATE_RECEIVER_DETAIL_DATA: 'CreateReceiverDetailWithData',
  // 更新某个收件人
  UPDATE_RECEIVER_DETAIL: 'UpdateReceiverDetail',
  // 批量收件人带模板参数，直接传文本
  UPLOAD_RECEIVERS_DETAIL: 'UploadReceiversDetail',
  // 查询某个收件人列表详情
  LIST_RECEIVER_DETAILS: 'ListReceiverDetails',
  // 删除某个收件人列表详情
  DELETE_RECEIVER_DETAIL: 'DeleteReceiverDetail',
  // 查询邮件数据统计
  GET_DELIVERY_STATISTIC: 'GetDeliveryStatistic',
  GET_DELIVERY_STATISTIC_BY_MONTH: 'GetDeliveryStatisticByMonth',
  // 队列实时状态
  GET_REALTIME_STATISTIC: 'GetRealTimeStatistic',
  // 查询邮件记录
  LIST_EMAIL_SENDING_RECORD: 'ListEmailSendingRecord',
  // 统计指定投递事件发生数量
  GET_EMAIL_EVENT_STATISTIC: 'GetEmailEventStatistic',
  // 创建导出投递事件记录任务
  CREATE_EMAIL_EVENT_DOWNLOAD_TESK: 'CreateEmailEventDownloadTask',
  // 生成下载链接
  DES_EMAIL_EVENT_DOWNLOAD_TESK: 'DescribeEmailEventDownloadTask',
  // 下载发信数据
  DOWNLOAD_DELIVERY_STATISTIC: 'DownloadDeliveryStatistic',
  // 下载跟踪数据
  DOWNLOAD_TRACE_STATISTIC: 'DownloadTraceStatistic',

  // 获取用户选择的回调事件
  GET_SELECTED_EVENT: 'GetSelectedEvent',
  // 更新用户选择回调的事件
  UPDATE_SELECTED_EVENT: 'UpdateSelectedEvent',
  GET_STATISTICS_LINK: 'GetStatisticsLink',
  UPDATE_STATISTICS_LINK: 'UpdateStatisticsLink',
  // 独立IP
  QUERY_DOMAIN_SEND_IP: 'QueryDomainSendIp',
  QUERY_USER_SEND_IP: 'QueryUserSendIp',
  DO_FEE_MANAGER: 'DoFeeManager',
  UPDATE_DOMAIN_SEND_IP: 'UpdateDomainSendIp',
  GET_CONFIGURABLE_IP_DOMAIN_MAPPING: 'GetConfigurableIpDomainMapping',
  ALLOCATE_DEDICATED_IP: 'AllocateDedicatedIp',
  GET_CONFIGURABLE_IP_COUNT: 'GetConfigurableIpCount',
  CREATE_DOMAIN_SEND_IP: 'CreateDomainSendIp',
  GET_DOMAIN_SEND_IP: 'GetDomainSendIp',
  DELETE_DOMAIN_SEND_IP: 'DeleteDomainSendIp',
  // 自定义黑名单
  LIST_CUSTOM_BLACKLIST: 'ListCustomBlacklist',
  UPDATE_CUSTOM_BLACKLIST: 'UpdateCustomBlackList',
  DELETE_CUSTOM_BLACKLIST: 'DeleteCustomBlackList',
  UPLOAD_CUSTOM_BLACKLIST: 'UploadCustomBlacklist',
  CREATE_CUSTOM_BLACKLIST: 'CreateCustomBlacklist',
  UPLOAD_CUSTOM_BLACKLIST_BY_COS: 'UploadCustomBlacklistByCOS',
  GET_ACCOUNT_CUSTOM_BLACKLIST_STATUS: 'GetAccountCustomBlacklistStatus',
  UPDATE_ACCOUNT_CUSTOM_BLACKLIST_STATUS: 'UpdateAccountCustomBlacklistStatus',

  // 获取COS的上传凭证
  GET_COS_CONF: 'GetCOSConf',

  // 安全合规相关接口
  GET_ACCOUNT_COUNTRY: 'GetAccountCountry',
  GET_SECURITY_SURVEY_ANSWERS: 'GetSecuritySurveyAnswers',
  SAVE_SECURITY_SURVEY_ANSWERS: 'SaveSecuritySurveyAnswers',

  // 退订相关接口
  LIST_UNSUBSCRIBE_ADDRESSES: 'ListUnsubscribeAddresses',
  DELETE_UNSUBSCRIBE_ADDRESS: 'DeleteUnsubscribeAddress',
};

export const OTHER_CMD = {
  DescribeRealNameAuthInfo: 'DescribeRealNameAuthInfo',
};
