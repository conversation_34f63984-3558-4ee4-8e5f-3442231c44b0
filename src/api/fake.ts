import { SES_CMD } from '@src/api/constants';
import { sleep } from '@src/utils/CommonUtils';

export const fakeCall = async (body) => {
  const { cmd, data } = body;
  const result: any = { code: 123 };
  await sleep(1500);
  switch (cmd) {
    case SES_CMD.LIST_DOMAIN: {
      console.log('fakeCall SES_CMD.DOMAIN_LIST', cmd, data);
      result.data = { hello: 123 };
      break;
    }
    default: {
      console.log('unknown fakeCall', cmd, data);
    }
  }
  return result;
};
