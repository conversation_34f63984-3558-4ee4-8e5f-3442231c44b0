import React, {  } from 'react';
import { Form } from '@tencent/tea-component';
import './style.less';

// 修改suffix展示，能让FormItem的suffix展示在更下面，防止显示表单验证错误时右边的图标位置错误
export const SesFormItem = (props) => {
  const { suffix, className, ...others } = props;

  return <>
    <Form.Item className={`${className} form-item-with-suffix`} {...others} />
    {suffix && <div className='form-item-suffix table-row'>
      <div className='table-cell'></div>
      <div className='table-cell suffix-text'>{suffix}</div>
    </div>}
  </>;
};
