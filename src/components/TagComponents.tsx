import React, { useEffect, useState } from 'react';
// @ts-ignore
import loadable from '@loadable/component';
import { app } from '@tea/app';

const fallback = <div></div>;
const opt = { fallback };

export const TagSelectPanel = loadable<{
  allowDuplicatedKey?: boolean;
  fixedKey?: boolean;
  asFilter?: boolean;
  defaultTagValue?: string;
  disableUnset?: boolean;
  tagKeyOnly?: boolean;
  rowPrefix?: any;
  max?: number;
  style?: any;
  value?: Array<any>;
  onChange?: (...args: any[]) => any;
  errors?: Array<string>;
}>(async () => {
  const tagSDK = await app.sdk.use('tag-sdk');
  const { TagSelectPanel } = tagSDK.components;
  return TagSelectPanel;
}, opt);

export const InfoTip = loadable(async () => {
  const tagSDK = await app.sdk.use('tag-sdk');
  const { InfoTip } = tagSDK.components;
  return InfoTip;
}, opt);

export const TagDisplayIcon = loadable(async () => {
  const tagSDK = await app.sdk.use('tag-sdk');
  const { TagDisplayIcon } = tagSDK.components;
  return TagDisplayIcon;
}, opt);

export const useLoadTagSDK = () => {
  const [tagSDK, setTagSDK] = useState<any>(null);
  useEffect(() => {
    const loadTagSDK = async () => {
      const tagSDK = await app.sdk.use('tag-sdk');
      setTagSDK(tagSDK);
    };
    loadTagSDK();
  }, []);
  return {
    tagFilterable: tagSDK?.tableAddons?.tagFilterable,
    tagConstants: tagSDK?.constants,
  };
};
