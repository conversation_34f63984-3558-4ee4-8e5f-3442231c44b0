import { requestApiV3 } from '@src/api';
import { handleRequestError } from '@src/utils/RequestUtils';

interface TagResourcesParams {
  Version?: string;
  ResourceList: string[];
  Tags: {
    TagKey: string;
    TagValue: string;
  }[];
}

interface UnTagResourcesParams {
  Version?: string;
  ResourceList: string[];
  TagKeys: string[];
}

/**
 * 调用标签服务的 TagResources 接口
 * @param params 标签资源参数
 */
export function tagResources(params: TagResourcesParams) {
  const cmd = 'TagResources';
  const postData = {
    Version: '2018-08-13',
    ...params,
  };

  return requestApiV3(cmd, postData, {
    serviceType: 'tag',
  })
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      console.log('tagResourcesRequestError', e);
      throw handleRequestError(e);
    });
}

export function unTagResources(params: UnTagResourcesParams) {
  const cmd = 'UnTagResources';
  const postData = {
    Version: '2018-08-13',
    ...params,
  };

  return requestApiV3(cmd, postData, {
    serviceType: 'tag',
  })
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      console.log('tagResourcesRequestError', e);
      throw handleRequestError(e);
    });
}
