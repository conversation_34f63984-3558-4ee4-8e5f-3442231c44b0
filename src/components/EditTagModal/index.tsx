import React, { useCallback, useEffect } from 'react';
import { Button, Form, Modal, Text, message } from '@tencent/tea-component';
import { useForm, useField } from 'react-final-form-hooks';
import { getValidateStatus } from '@src/utils/FormUtils';
import { t } from '@tea/app/i18n';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import { TagSelectPanel, InfoTip } from '@src/components/TagComponents';
import { tagResources, unTagResources } from './actions';
import { getRegionName } from '@src/api/base';
import styles from './style.module.less';
import { app } from '@tea/app/index';

interface DialogValue {
  resourceType: 'senderdomain' | 'independentip';
  resourceIds: string[];
  currentTags?: { tagKey: string; tagValue: string }[];
}

interface DialogProps {
  dialogRef: DialogRef<DialogValue>;
  onConfirm?: () => void;
}

const DEFAULT_INIT_VALUES = {
  tags: [],
};
const resourceInfoMap = {
  senderdomain: {
    label: t('发信域名'),
    prefix: 'senderdomain',
  },
  independentip: {
    label: t('独立IP'),
    prefix: 'independentip',
  },
};

const validateTags = (values: any) => {
  const errors: Record<string, string> = {};
  // if (!values?.tags || !values?.tags.length) {
  //   errors.tags = t('请至少添加一个标签');
  // }
  if (values?.tags.length > 10) {
    errors.tags = t('最多可添加10个标签');
  }
  if (values?.tags.some((tag) => !tag.tagKey || !tag.tagValue)) {
    errors.tags = t('标签名和标签值不能为空');
  }
  return errors;
};

export const EditTagModal: React.FC<DialogProps> = ({
  dialogRef,
  onConfirm,
}) => {
  const [visible, setShowState, value] = useDialog<DialogValue>(dialogRef);

  const { form, handleSubmit, validating, submitting, values } = useForm({
    onSubmit: async (formValues) => {
      if (!value) return;

      try {
        // 构建资源列表
        const { ownerUin } = app.user;
        const resourceList = value.resourceIds.map((id) => {
          const resourcePrefix = resourceInfoMap[value.resourceType].prefix;
          const region = getRegionName();
          return `qcs::ses:${region}:uin/${ownerUin}:${resourcePrefix}/${id}`;
        });

        const oldTags = value.currentTags ?? [];
        const newTags = formValues.tags;

        const addTags = newTags.filter(
          (tag) => !oldTags.some((oldTag) => oldTag.tagKey === tag.tagKey),
        );
        const removeTags = oldTags.filter(
          (oldTag) => !newTags.some((tag) => tag.tagKey === oldTag.tagKey),
        );
        // 如果没有当前标签，且提交的标签为空，则直接关闭弹窗
        if (!oldTags.length && !newTags.length) {
          setShowState(false);
          return;
        }

        // 如果有当前标签，并且提交的标签为空，则是解绑标签
        if (removeTags.length) {
          await unTagResources({
            ResourceList: resourceList,
            TagKeys: removeTags.map((tag) => tag.tagKey),
          });
        }
        if (newTags.length) {
          // 调用标签接口
          await tagResources({
            ResourceList: resourceList,
            Tags: newTags.map((tag: any) => ({
              TagKey: tag.tagKey,
              TagValue: tag.tagValue,
            })),
          });
        }

        message.success({ content: t('标签编辑成功') });
        setShowState(false);
        onConfirm?.();
      } catch (error) {
        message.error({ content: t('标签编辑失败，请稍后重试') });
      }
    },
    validate: validateTags,
    initialValuesEqual: () => true,
    initialValues: DEFAULT_INIT_VALUES,
  });

  const tagsField = useField('tags', form);

  // 当弹窗打开时，设置初始标签值
  useEffect(() => {
    if (visible && value?.currentTags) {
      form.change('tags', value.currentTags);
    } else if (visible) {
      form.change('tags', []);
    }
  }, [visible, form, value?.currentTags]);

  const getResourceTypeText = () => {
    if (!value) return '';
    return resourceInfoMap[value.resourceType]?.label;
  };

  return (
    <Modal
      visible={visible}
      caption={t('编辑标签')}
      onClose={() => setShowState(false)}
      size="m"
    >
      <form onSubmit={handleSubmit}>
        <Modal.Body>
          <Form>
            <Form.Item label={getResourceTypeText()}>
              <Form.Text>{value.resourceIds?.join('')}</Form.Text>
            </Form.Item>
            <Form.Item
              label={
                <>
                  {t('标签')}
                  {InfoTip && <InfoTip />} {/* 提示 */}
                </>
              }
              status={getValidateStatus(tagsField.meta, validating)}
              message={
                getValidateStatus(tagsField.meta, validating) === 'error' &&
                tagsField.meta.error
              }
            >
              <TagSelectPanel
                serviceType="ses"
                resourcePrefix={value.resourceType}
                max={10}
                value={tagsField.input.value}
                onChange={(tags: { tagKey: string; tagValue: string }[]) => {
                  tagsField.input.onChange(tags);
                }}
                className={styles.tagPanel}
              />
            </Form.Item>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            disabled={validating}
          >
            {t('确定')}
          </Button>
          <Button
            type="weak"
            htmlType="button"
            onClick={() => setShowState(false)}
          >
            {t('取消')}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default EditTagModal;
