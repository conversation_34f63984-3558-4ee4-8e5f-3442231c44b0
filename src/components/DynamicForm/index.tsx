import React from 'react';
import { Button, Form, FormItemProps, FormProps } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import arrayMutators from 'final-form-arrays';
import { FieldArray } from 'react-final-form-arrays';
import { getValidateStatus } from '@src/utils/FormUtils';
import style from './style.module.less';
import { AddCircleIcon, MinusCircleIcon } from '@tencent/tea-icons-react';
import { t } from '@tencent/tea-app/lib/i18n';
const _DYNAMIC_FORM = '_DYNAMIC_FORM';

type FieldList = {
  name: string;
  itemProps: FormItemProps;
  fieldProps;
  component: React.FunctionComponent;
  validate: (value: any) => string | undefined;
}[];

type DynamicFormProps = {
  showButtons?: Boolean;
  defaultValues?: unknown[];
  onSubmit: (values: unknown[]) => void;
  formProps: FormProps;
  fieldList: FieldList;
  footerRender: any;
};

const DynamicForm = ({
  defaultValues,
  onSubmit,
  formProps,
  fieldList,
  footerRender,
  showButtons = true,
}: DynamicFormProps) => {
  const handleSubmit = (values) => {
    return onSubmit(values[_DYNAMIC_FORM]);
  };
  return (
    <FinalForm
      onSubmit={handleSubmit}
      mutators={{ ...arrayMutators }}
      initialValues={{
        [_DYNAMIC_FORM]: defaultValues ? defaultValues : [undefined],
      }}
      render={({
        handleSubmit,
        form: {
          mutators: { push },
        },
        pristine,
        submitting,
        validating,
        form,
        values,
      }) => {
        return (
          <form className={style['dynamic-form']} onSubmit={handleSubmit}>
            <FieldArray name={_DYNAMIC_FORM}>
              {({ fields }) =>
                fields.map((name, index) => (
                  <Form key={name} {...formProps}>
                    {fieldList.map((field) => {
                      const {
                        name: fieldName,
                        itemProps,
                        fieldProps,
                        component: Component,
                        validate,
                      } = field;
                      const _itemProps = { ...itemProps };
                      // 为了动态地生成或修改组件的属性，使得组件的行为可以根据传入的itemProps对象中的函数逻辑进行定制。
                      Object.keys(itemProps).forEach((key) => {
                        if (typeof itemProps[key] === 'function') {
                          _itemProps[key] = itemProps[key]({
                            index,
                            values,
                            form,
                          });
                        }
                      });
                      return (
                        <Field
                          name={`${name}.${fieldName}`}
                          key={fieldName}
                          validate={validate}
                        >
                          {({ input, meta }) => {
                            const restProps = Object.assign(
                              {},
                              input,
                              fieldProps,
                            );
                            return (
                              <Form.Item
                                status={getValidateStatus(meta, validating)}
                                message={
                                  getValidateStatus(meta, validating) ===
                                    'error' && meta.error
                                }
                                {..._itemProps}
                              >
                                <Component {...restProps} />
                              </Form.Item>
                            );
                          }}
                        </Field>
                      );
                    })}
                    {showButtons && (
                      <div className={style['icon-box']}>
                        {fields.length > 1 && index !== 0 && (
                          <MinusCircleIcon
                            size={20}
                            onClick={() => fields.remove(index)}
                          />
                        )}
                        {index === fields.length - 1 && (
                          <AddCircleIcon
                            size={20}
                            onClick={() => push(_DYNAMIC_FORM, undefined)}
                          />
                        )}
                      </div>
                    )}
                  </Form>
                ))
              }
            </FieldArray>
            {footerRender ? (
              footerRender({
                submitting,
                pristine,
                validating,
                handleSubmit,
                form,
              })
            ) : (
              <>
                <Button
                  type="primary"
                  style={{ marginRight: 10 }}
                  htmlType="submit"
                  onClick={handleSubmit}
                  disabled={submitting || pristine}
                >
                  {t('确定')}
                </Button>
                <Button
                  type="weak"
                  onClick={form.reset}
                  disabled={submitting || pristine}
                >
                  {t('重置')}
                </Button>
              </>
            )}
          </form>
        );
      }}
    />
  );
};
export default DynamicForm;
