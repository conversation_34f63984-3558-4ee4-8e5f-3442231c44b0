/* 目前只在国内使用，忽略翻译 */
/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
import React, { useState } from 'react';
import { RegionSupport } from '@tencent/tea-app';
import { Text } from '@tea/component';
import {
  REGIONS_IN_CHINA,
  SELECTED_REGION_STORAGE_KEY,
} from '@src/constants/defaultConstant';
import { isMainLand } from '@src/utils/CommonUtils';

const RegionSelect: React.FC = () => {
  const selectedRegion = localStorage.getItem(SELECTED_REGION_STORAGE_KEY);
  const initRegion = REGIONS_IN_CHINA[selectedRegion]
    ? selectedRegion
    : REGIONS_IN_CHINA.hk.regionCode;
  const [region, setRegion] = useState(initRegion);

  return (
    <RegionSupport>
      {({ RegionSelect, RegionPanel, RegionOption }) => {
        const { Column, Group } = RegionPanel;
        return (
          <RegionSelect
            value={region}
            onChange={(val) => {
              setRegion(val);
              localStorage.setItem(SELECTED_REGION_STORAGE_KEY, val);
              window.location.reload();
            }}
          >
            <RegionPanel>
              <Column>
                <Group name="中国">
                  {Object.keys(REGIONS_IN_CHINA).map((key) => (
                    <RegionOption key={`region-${key}`} value={key}>
                      {REGIONS_IN_CHINA[key].name}
                    </RegionOption>
                  ))}
                </Group>
              </Column>
            </RegionPanel>
          </RegionSelect>
        );
      }}
    </RegionSupport>
  );
};

const FinalRegionSelect: React.FC = () => {
  // useEffect(() => {
  //   if (isMainLand) {
  //     app.user.checkWhitelist('SES_REGION_WHITELIST').then((res) => {
  //       // 在白名单内
  //       if (res) {
  //         setShowRegion(true);
  //       }
  //     });
  //   }
  // }, []);

  // 国内站增加地区选择
  if (isMainLand) {
    return (
      <>
        <Text verticalAlign="middle" style={{ marginRight: 10 }}>
          {
            // 目前只在国内使用，忽略翻译
            // eslint-disable-next-line @tencent/tea-i18n/no-bare-zh-in-js
            '地域'
          }
        </Text>
        {/* 地域选择组件 */}
        <RegionSelect />
      </>
    );
  }
  return null;
};

export default FinalRegionSelect;
