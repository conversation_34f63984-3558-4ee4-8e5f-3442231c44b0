import React from 'react';
import { app } from '@tea/app';
import { t } from '@tea/app/i18n';
import { getRegionName } from '@src/api/base';

/**
 * 编辑域名标签的对话框
 * @param props
 */
export const editResourceTag = (props: {
  onSuccess?: () => void;
  resourceIds: string[];
  resourcePrefix: string;
}) => {
  const { resourceIds, resourcePrefix, onSuccess } = props;

  const params = {
    region: getRegionName(),
    serviceType: 'ses', // 使用SES服务类型
    resourcePrefix, // 资源前缀为domain
    resourceIds, // 域名作为资源ID
    needCommit: true,
    callback: (event: 'complete' | 'taskBoxClose') => {
      console.log(event);
      onSuccess?.();
    },
  };

  const main = async () => {
    try {
      const { batchEditTag } = await app.sdk.use('tag-sdk');
      batchEditTag(params);
    } catch (err) {
      console.error(t('打开标签编辑器失败'), err);
    }
  };
  main();
};

export default editResourceTag;
