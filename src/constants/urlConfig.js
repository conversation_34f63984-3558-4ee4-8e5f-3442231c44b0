import { AREA_TYPE, getCurrentUserArea } from '@src/utils/CommonUtils';

export const LINK_URL_NAME = {
  apiDoc: 'apiDoc',
  payDoc: 'payDoc',
  warnUpDoc: 'warnUpDoc',
  reputationDoc: 'reputationDoc',
  verifyDoc: 'verifyDoc',
  specificationDoc: 'specificationDoc',
};

export const LINK_URLS = {
  MAINLAND: {
    apiDoc: `https://${window.QCMAIN_HOST}/document/product/1288/51062`,
    payDoc: `https://${window.QCMAIN_HOST}/document/product/1288/47930`,
    warnUpDoc: `https://${window.QCMAIN_HOST}/document/product/1288/63469`,
    reputationDoc: `https://${window.QCMAIN_HOST}/document/product/1288/77252`,
    verifyDoc: `https://${window.QCMAIN_HOST}/document/product/1288/60652`,
    specificationDoc: `https://${window.QCMAIN_HOST}/document/product/1288/52777#.E9.82.AE.E4.BB.B6.E6.A8.A1.E6.9D.BF.E5.86.85.E5.AE.B9.E8.A7.84.E8.8C.83.E6.98.AF.E4.BB.80.E4.B9.88.EF.BC.9F`,
  },
  INTL: {
    apiDoc: `https://${window.QCMAIN_HOST}/document/product/1084/39387`,
    payDoc: `https://${window.QCMAIN_HOST}/document/product/1084/39335`,
    warnUpDoc: `https://${window.QCMAIN_HOST}/document/product/1084/43285`,
    reputationDoc: `https://${window.QCMAIN_HOST}/document/product/1084/48864`,
    verifyDoc: `https://${window.QCMAIN_HOST}/document/product/1084/42371`,
    specificationDoc: `https://${window.QCMAIN_HOST}/document/product/1288/52777#.E9.82.AE.E4.BB.B6.E6.A8.A1.E6.9D.BF.E5.86.85.E5.AE.B9.E8.A7.84.E8.8C.83.E6.98.AF.E4.BB.80.E4.B9.88.EF.BC.9F`,
  },
};

export const getLinkUrl = (urlName) => {
  const area = getCurrentUserArea();
  if (area === AREA_TYPE.MAINLAND) {
    return LINK_URLS.MAINLAND[urlName];
  }
  return LINK_URLS.INTL[urlName];
};
