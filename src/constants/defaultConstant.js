import { t } from '@tea/app/i18n';

export const TO_DOMAIN_LIST = [
  { value: 'qq' },
  { value: 'foxmail' },
  { value: '163' },
  { value: '126' },
  { value: '139' },
  { value: '189' },
  { value: 'gmail' },
  { value: 'hotmail' },
  { value: 'outlook' },
  { value: 'live' },
  { value: 'icloud' },
  { value: 'yandex' },
  { value: 'mail.ru' },
  { value: 'web.de' },
  { value: 'gmx' },
  { value: 'yahoo' },
  { value: 'other', text: t('其它') },
];

export const EVENT_TYPE = [
  { id: 0, value: t('开始发送') },
  { id: 1, value: t('已送达') },
  { id: 2, value: t('校验未通过') },
  { id: 3, value: t('退信') },
  { id: 4, value: t('打开') },
  { id: 5, value: t('点击') },
  { id: 6, value: t('垃圾投诉') },
  { id: 7, value: t('取消订阅') },
  { id: 8, value: t('延期发送') },
  { id: 9, value: t('发送错误') },
];

export const STATISTIC_PATH = [
  // 4: 用户打开; 5: 用户点击
  {
    pathname: '/ses/open-click',
    openValue: 4,
    clickValue: 5,
    pageText: t('打开/点击'),
  },
  // { pathName: '/ses/open-click', value: 5 },
  { pathname: '/ses/spam-report', value: 6, pageText: t('垃圾举报') },
  { pathname: '/ses/hard-bounce', value: 3, pageText: t('退信') },
  { pathname: '/ses/unsubscription', value: 7, pageText: t('退订') },
];

// 正则匹配中文、数字、大小写字母
export const INPUT_RULE = /^[\u4E00-\u9FA5A-Za-z0-9_]{1,}$/;
// 正则匹配邮箱
export const emailRules =
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

//只包含大小写字母（a-z、A-Z）、数字（0-9）和下划线
export const variableRules = /^[a-zA-Z0-9_]+$/;

export const REGIONS_IN_CHINA = {
  // 目前只在国内使用，忽略翻译
  /* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
  gz: { name: '广州', regionId: 1, regionCode: 'gz' },
  hk: { name: '中国香港', regionId: 5, regionCode: 'hk' },
  /* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
};

// localStorage中地域选择对应的key
export const SELECTED_REGION_STORAGE_KEY = 'SES_SELECTED_REGION';
