import React from 'react';

// 导入组件
import { SesIndex } from '@src/routes/ses-index';
import { TemplatePage } from '@src/routes/template';
import { DomainPage } from '@src/routes/domain';
import { AddressPage } from '@src/routes/address';
import { SingleSendPage } from '@src/routes/single-send';
import { AccountOpenPage } from '@src/routes/account-open';
import { StatsPage, MailTrackingPage } from '@src/routes/stats';
import { AccountPackagePage } from '@src/routes/account-package';
import { SettingPage } from '@src/routes/setting';
import { BatchSendPage } from './routes/batch-send';
import { EmailSimulatorPage } from './routes/simulator';
import { BlockListPage } from './routes/block-list';
import { EmailStatusPage } from './routes/email-status';
import { QueueStatusPage } from './routes/queue-status';
import { EmailEventPage } from './routes/email-event';
import MenusSdkController from './routes/global-components/menus-sdk-controller/MenusSdkController';
import StatisticsSettingPage from './routes/stats/StatisticsSettingPage';
import { useUserAuthCheck } from './routes/global-components/useUserAuthCheck';
import DedicatedIPContainer from './routes/dedicated-ip/DedicatedIPContainer';
import { Router } from 'react-router-dom';
import { useHistory } from '@tencent/tea-app';
import ContactContainer from './routes/contact/ContactContainer';

const Container: React.FC<{}> = (props) => {
  const history = useHistory();
  useUserAuthCheck();
  return (
    <Router history={history}>
      <MenusSdkController>
        <>{props.children}</>
      </MenusSdkController>
    </Router>
  );
};

// 路由表，一个路由对应一个组件
export const routes = {
  'ses/index': {
    render: () => (
      <Container>
        <SesIndex />
      </Container>
    ),
  },
  // "ses/index": SingleSendPage,
  'ses/domain': {
    render: () => (
      <Container>
        <DomainPage />
      </Container>
    ),
  },
  'ses/address': {
    render: () => (
      <Container>
        <AddressPage />
      </Container>
    ),
  },
  'ses/template': {
    render: () => (
      <Container>
        <TemplatePage />
      </Container>
    ),
  },
  'ses/setting': {
    render: () => (
      <Container>
        <SettingPage />
      </Container>
    ),
  },
  'ses/dedicated-ip': {
    render: () => (
      <Container>
        <DedicatedIPContainer />
      </Container>
    ),
  },
  'ses/send': {
    render: () => (
      <Container>
        <SingleSendPage />
      </Container>
    ),
  },
  'ses/open': {
    render: () => (
      <Container>
        <AccountOpenPage />
      </Container>
    ),
  },
  'ses/stats': {
    render: () => (
      <Container>
        <StatsPage />
      </Container>
    ),
  },
  'ses/mail-tracking': {
    render: () => (
      <Container>
        <MailTrackingPage />
      </Container>
    ),
  },
  'ses/statistics-setting': {
    render: () => (
      <Container>
        <StatisticsSettingPage />
      </Container>
    ),
  },
  'ses/package': {
    render: () => (
      <Container>
        <AccountPackagePage />
      </Container>
    ),
  },
  'ses/contact': {
    render: () => (
      <Container>
        <ContactContainer />
      </Container>
    ),
  },
  'ses/batch-send': {
    render: () => (
      <Container>
        <BatchSendPage />
      </Container>
    ),
  },
  'ses/simulator': {
    render: () => (
      <Container>
        <EmailSimulatorPage />
      </Container>
    ),
  },
  // 投递回应
  // 队列实时状态
  'ses/queue-status': {
    render: () => (
      <Container>
        <QueueStatusPage />
      </Container>
    ),
  },
  // 邮件状态
  'ses/email-status': {
    render: () => (
      <Container>
        <EmailStatusPage />
      </Container>
    ),
  },
  // 打开/点击
  'ses/open-click': {
    render: () => (
      <Container>
        <EmailEventPage />
      </Container>
    ),
  },
  // 垃圾举报
  'ses/spam-report': {
    render: () => (
      <Container>
        <EmailEventPage />
      </Container>
    ),
  },
  // 硬退信
  'ses/hard-bounce': {
    render: () => (
      <Container>
        <EmailEventPage />
      </Container>
    ),
  },
  // 退订
  'ses/unsubscription': {
    render: () => (
      <Container>
        <EmailEventPage />
      </Container>
    ),
  },
  // 拦截
  'ses/block-list': {
    render: () => (
      <Container>
        <BlockListPage />
      </Container>
    ),
  },
};
