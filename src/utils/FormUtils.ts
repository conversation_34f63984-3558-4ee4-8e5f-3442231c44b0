import { t } from '@tea/app/i18n';
import _ from 'lodash';

export const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const getValidateStatus = (meta, validating) => {
  if (meta.active && validating) {
    return 'validating';
  }
  if (!meta.touched) {
    return null;
  }
  return meta.error ? 'error' : 'success';
};

export function validateEmail(email: string): string | undefined {
  if (!email) {
    return t('收件邮箱地址为必填条件');
  }
  if (!emailReg.test(email)) {
    return t('邮箱地址格式错误');
  }
  return undefined;
}

export function validateDestination(dest: string): boolean | string {
  if (!dest) return true;
  try {
    const destArr = dest.split('\n');
    const unvalidIndex = _.findIndex(
      destArr,
      (v: string) => _.trim(v) && !emailReg.test(_.trim(v)),
    );
    if (unvalidIndex !== -1)
      return t('第{{unvalidIndex}}行格式错误，请重新输入。', {
        unvalidIndex: unvalidIndex + 1,
      });
  } catch (error) {
    return true;
  }
}
