import { app, i18n } from '@tea/app';
import { t } from '@tencent/tea-app/lib/i18n';
import COS from 'cos-js-sdk-v5';

export const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

/** 当前用户所在站点 `1`-国内站 `2`-国际站 */
export const AREA_TYPE = {
  MAINLAND: 1,
  INTL: 2,
};

/** 获取当前用户所在站点 `1`-国内站 `2`-国际站 */
export const getCurrentUserArea = () => {
  const { area } = app.user;
  return area;
};

export const LANGUAGE_TYPE = {
  ZH: 'zh',
  EN: 'en',
  JP: 'jp',
  KO: 'ko',
};

/**
 * 当前用户的国际化语言，已知语言：
 *  - `zh` 中文
 *  - `en` 英文
 *  - `jp` 日语
 *  - `ko` 韩语
 */
export const getCurrentLanguage = () => {
  const { lng } = i18n;
  return lng;
};

export const isMainLand = getCurrentUserArea() === AREA_TYPE.MAINLAND;
export const isIntl = getCurrentUserArea() === AREA_TYPE.INTL;
export function isPlainObject(value) {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

export const readFileAsync: (file: any) => Promise<string> = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event: any) => {
      resolve(event.currentTarget.result);
    };

    reader.onerror = () => {
      reject(new Error(t('文件读取失败，请重试。')));
    };
    reader.readAsText(file);
  });
};
export function downloadFile(url: string, fileName?) {
  // 创建一个隐藏的a标签
  const link = document.createElement('a');
  link.style.display = 'none';
  document.body.appendChild(link);
  // 设置a标签的href属性和download属性
  link.href = url;
  fileName && (link.download = fileName);
  // 触发点击事件
  link.click();
  // 移除a标签
  document.body.removeChild(link);
}

export const createCOSInstance = (credentials: any): COS => {
  return new COS({
    Domain: '{Bucket}.cos.accelerate.myqcloud.com',
    SecretId: credentials.TmpSecretID,
    SecretKey: credentials.TmpSecretKey,
    SecurityToken: credentials.SessionToken,
    StartTime: credentials.StartTime,
    ExpiredTime: credentials.ExpiredTime,
  });
};
