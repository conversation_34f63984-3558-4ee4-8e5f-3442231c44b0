import { message } from '@tea/component';
import { app } from '@tea/app';

// 操作级CAM 唤起拦截对话框
const modal = async (msg = '') => {
  const cam = await app.sdk.use('cam-sdk');
  // 通过 cam 调用 API
  cam.showBreakModal({ message: msg });
};

export const handleRequestError = (json) => {
  console.log('handleRequestError', json);
  if (json.code === 'AuthFailure.UnauthorizedOperation') {
    modal(json?.msg);
    return json;
  }
  let msg = json?.data?.message;
  if (!msg) msg = json?.msg;
  if (!msg) msg = JSON.stringify(json);
  message.error({ content: msg });
  // if (json.base && json.base.ret === CODE.NOT_AUTHORIZED) {
  //   showTip(<div>Not Login, please login again.
  //   </div>, showAlert)
  //     .then(() => {
  //       actions.logout();
  //     });
  // } else {
  //   showTip(<div>{json ? JSON.stringify(json) : "NETWORK ERROR"}</div>, showAlert);
  // }
  return json;
};
