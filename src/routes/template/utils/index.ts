import CryptoJS from 'crypto-js';

// 把base64开头的一段去掉，变成纯文件内容的base64
export const dataURItoBase64Content = (dataURI) => {
  if (!dataURI || typeof dataURI !== 'string') return dataURI;
  const base64contentStr = dataURI.split(',')[1];
  return base64contentStr;
};

export const base64Convertor = {
  toBase64: (text) => {
    let encoded = text;
    try {
      const utf8s = CryptoJS.enc.Utf8.parse(text);
      encoded = CryptoJS.enc.Base64.stringify(utf8s);
    } catch (e) {
      console.log('base64Convertor.toBase64 error:', e, text);
      throw e;
    }
    return encoded;
  },
  toText: (base64) => {
    let decoded = base64;
    let errorStr = '';
    try {
      const words = CryptoJS.enc.Base64.parse(base64);
      decoded = CryptoJS.enc.Utf8.stringify(words);
    } catch (e) {
      errorStr = e.toString();
      console.log('base64Convertor.toText error:', e, { base64 });
    }
    return { content: decoded, errorStr };
  },
};
