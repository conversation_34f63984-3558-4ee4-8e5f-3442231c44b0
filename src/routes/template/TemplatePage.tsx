import React, { useCallback, useEffect, useState } from 'react';
import { t, Trans } from '@tea/app/i18n';
import {
  Alert,
  Button,
  Card,
  ExternalLink,
  Justify,
  Layout,
  List,
  Table,
  Text,
} from '@tencent/tea-component';
import { Bubble, Icon, Modal } from '@tea/component';
import NewTemplateModal from '@src/routes/template/components/NewTemplateModal/NewTemplateModal';
import {
  addTemplate,
  deleteTemplate,
  fetchTemplateList,
} from '@src/routes/template/actions';
import { base64Convertor } from '@src/routes/template/utils';
import { DateTimeDisplay } from '@src/components/DateTimeDisplay';
import { TEMPLATE_STATUS } from '@src/routes/template/constants';
import { autotip } from '@tea/component/table/addons';
import './style.less';
import RegionSelect from '@src/components/RegionSelect/Page';
import { LINK_URL_NAME, getLinkUrl } from '@src/constants/urlConfig';

const { Body, Content } = Layout;
const { pageable } = Table.addons;

export function TemplatePage() {
  const [list, setList] = useState([]);
  const [newModalVisible, setCreateModalVisible] = useState(false);
  const [listLoading, setListLoading] = useState(true);

  const [total, setTotal] = useState();
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  const fetchList = useCallback(() => {
    setListLoading(true);
    // setDomainList([]);
    const data = {
      Limit: pageData.pageSize,
      Offset: (pageData.pageIndex - 1) * pageData.pageSize,
    };
    console.log('will fetch Template', data, pageData);
    fetchTemplateList(data)
      .then((res: any) => {
        console.log('fetchTemplateList res=', res);
        const { TemplatesMetadata = [], TotalCount } = res || {};
        const newList = TemplatesMetadata.map((one: any) => {
          const {
            TemplateID,
            TemplateName,
            TemplateStatus,
            CreatedTimestamp,
            ReviewReason,
          } = one;
          return {
            TemplateID,
            TemplateName,
            TemplateStatus,
            CreatedTimestamp,
            ReviewReason,
          };
        });
        setList(newList);
        setTotal(TotalCount);
      })
      .finally(() => {
        setListLoading(false);
      });
  }, [pageData]);

  useEffect(() => {
    fetchList();
  }, [pageData]);

  const onCreateSubmit = (values) => {
    console.log('onCreateSubmit', values);
    const { file, text, ...others } = values;
    // const base64 = dataURItoBase64Content(file && file.base64);
    const base64html = file?.base64;
    const base64text = text && base64Convertor.toBase64(text);
    const data = { base64: base64html, text: base64text, ...others };
    return addTemplate(data)
      .then((res) => {
        console.log('addTemplate Over', res);
        setCreateModalVisible(false);
        fetchList();
        Modal.alert({
          message: t('创建模板'),
          description: t(
            '您的模板申请已提交成功，预计在一个工作日内完成审核（周末、节假日顺延），感谢您的耐心等待。',
          ),
          type: 'infoblue',
          buttonText: t('知道了'),
        });
      })
      .catch(() => {})
      .finally(() => {});
  };

  const [current, setCurrent] = useState();
  const onDetailClick = async (item) => {
    // const { TemplateID } = item;
    setCurrent(item);
    // setCurrentId(TemplateID);
    setCreateModalVisible(true);
  };

  const onDeleteConfirm = async (item) => {
    const yes = await Modal.confirm({
      message: t('确认删除当前所选模板？'),
      description: t('删除后，不能再用该模板发送邮件。'),
      okText: t('删除'),
      cancelText: t('取消'),
    });
    if (yes) {
      return deleteTemplate(item.TemplateID)
        .then(() => {
          fetchList();
        })
        .finally(() => {});
    }
    // setAnswer(yes ? "已删除" : "未删除");
  };

  // // 右上角搜索框
  // const [searchBox, setSearchBox] = useState("");
  // const onSearchBoxChange = (value) => {
  //   setSearchBox(value);
  // }
  // const filterList = useMemo(() => {
  //   if (searchBox) {
  //     return list.filter(one => {
  //       const { TemplateID = '', TemplateName = '' } = one || {};
  //       return `${TemplateID}`.includes(searchBox) || TemplateName.includes(searchBox);
  //     });
  //   } else {
  //     return list;
  //   }
  // }, [list, searchBox]);
  const filterList = list;

  // 翻页
  const onPagingChange = (data) => {
    setPageData(data);
  };

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('发信模板')} subtitle={<RegionSelect />} />
            <Content.Body>
              <Alert hideIcon>
                <List type="number">
                  <List.Item>
                    {t(
                      '模板提交后，预计1个工作日内完成审核（周末、节假日顺延）；',
                    )}
                  </List.Item>
                  <List.Item>
                    <Trans>
                      为了保证模板通过率，模板内容必须体现实际业务且需要遵循
                      <ExternalLink
                        href={getLinkUrl(LINK_URL_NAME.specificationDoc)}
                      >
                        邮件模板内容规范
                      </ExternalLink>
                      。
                    </Trans>
                  </List.Item>
                </List>
              </Alert>
              <Table.ActionPanel>
                <Justify
                  left={
                    <>
                      <Button
                        type="primary"
                        onClick={() => {
                          setCurrent(null);
                          setCreateModalVisible(true);
                        }}
                      >
                        {t('新建')}
                      </Button>
                      {/* <Button>开机</Button>*/}
                    </>
                  }
                  right={<>{/* <SearchBox onChange={onSearchBoxChange} />*/}</>}
                />
              </Table.ActionPanel>
              <Card>
                <Table
                  verticalTop
                  records={filterList}
                  recordKey="TemplateID"
                  columns={[
                    {
                      key: 'TemplateID',
                      header: t('模板id'),
                    },
                    {
                      key: 'TemplateName',
                      header: t('模板名称'),
                    },
                    {
                      key: 'CreatedTimestamp',
                      header: t('创建时间'),
                      render: (
                        record: any,
                        rowKey: string,
                        recordIndex: number,
                        column: any,
                      ) => {
                        const item = record[column.key];
                        return <DateTimeDisplay timestamp={item} />;
                      },
                    },
                    {
                      key: 'TemplateStatus',
                      header: () => (
                        <>
                          {t('当前状态')}
                          <Bubble
                            content={t(
                              '模板状态，审核通过才可以使用此模板发送邮件',
                            )}
                          >
                            <Icon type="info" />
                          </Bubble>
                        </>
                      ),
                      // width: 100,
                      render: (item) => {
                        if (
                          item &&
                          item.TemplateStatus === TEMPLATE_STATUS.PASS
                        ) {
                          return (
                            <span style={{ color: '#0abf5b' }}>
                              {t('审核通过')}
                            </span>
                          );
                        }
                        if (
                          item &&
                          item.TemplateStatus === TEMPLATE_STATUS.VERIFYING
                        ) {
                          return (
                            <Text
                              theme="warning"
                              style={{ verticalAlign: 'middle' }}
                            >
                              {t('审核中')}
                              <Bubble
                                content={t(
                                  '模板提交后，预计1个工作日内完成审核（周末、节假日顺延），感谢您的耐心等待。',
                                )}
                              >
                                <Icon
                                  type="warning"
                                  style={{ margin: '-2px 0 0 2px' }}
                                />
                              </Bubble>
                            </Text>
                          );
                        }
                        if (
                          item &&
                          item.TemplateStatus === TEMPLATE_STATUS.FORBIDDEN
                        ) {
                          return (
                            <span className="template-status template-status__forbidden">
                              {t('审核未通过')}
                              {item?.ReviewReason && (
                                <Icon
                                  className="status-help-icon"
                                  type="error"
                                  tooltip={item.ReviewReason}
                                />
                              )}
                            </span>
                          );
                        }
                        return (
                          <span style={{ color: '#e54545' }}>
                            {t('未知状态') + item?.status}
                          </span>
                        );
                      },
                    },
                    {
                      key: 'action',
                      header: t('操作'),
                      // align: "right",
                      width: 120,
                      render: (item) => (
                        <>
                          <Button
                            type="link"
                            onClick={() => {
                              onDetailClick(item);
                            }}
                          >
                            {t('详情')}
                          </Button>
                          <Button
                            type="link"
                            onClick={() => {
                              onDeleteConfirm(item);
                            }}
                          >
                            {t('删除')}
                          </Button>
                        </>
                      ),
                    },
                  ]}
                  addons={[
                    pageable({
                      recordCount: total,
                      pageIndex: pageData.pageIndex,
                      pageSize: pageData.pageSize,
                      pageSizeOptions: [10, 20, 30, 50, 100],
                      onPagingChange: (query) => {
                        console.log('onPagingChange', query);
                        // {pageIndex: 2, pageSize: 10}
                        onPagingChange(query);
                      },
                    }),
                    autotip({
                      isLoading: listLoading,
                    }),
                    // scrollable({ maxHeight: 480, minHeight: 480 }),
                  ]}
                />
              </Card>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      {newModalVisible && (
        <NewTemplateModal
          visible={newModalVisible}
          onCancel={() => {
            setCreateModalVisible(false);
          }}
          values={current}
          // values={modalValues}
          onSubmit={onCreateSubmit}
        />
      )}
    </>
  );
}
