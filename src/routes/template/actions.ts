import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';
import { HTML_FLAGS } from '@src/routes/template/components/NewTemplateModal';
import { message } from '@tea/component';
import { t } from '@tea/app/i18n';

export function fetchTemplateList(data: any = {}) {
  const cmd = SES_CMD.LIST_EMAIL_TEMPLATE;
  let { Limit = 100 } = data || {};
  const { Offset = 0 } = data || {};
  return requestApiV3(cmd, {
    Limit,
    Offset,
  })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function addTemplate(data: any) {
  console.log('addTemplate start data=', data);
  const { id, name, base64, htmlFlag, text } = data;
  let cmd = SES_CMD.ADD_EMAIL_TEMPLATE;
  if (id) {
    cmd = SES_CMD.UPDATE_EMAIL_TEMPLATE;
  }
  const postData = {
    TemplateID: id,
    TemplateName: name,
    TemplateContent: {
      Html: htmlFlag === HTML_FLAGS.html ? base64 : '',
      Text: text,
    },
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      if (e && e.code === -400) {
        message.error({
          content: t(
            '提交失败，可能由于模板文件过大，请使用API调用的方式上传。',
          ),
        });
        throw e;
      } else {
        throw handleRequestError(e);
      }
    });
}

export function getTemplateInfo(templateId: string) {
  const cmd = SES_CMD.GET_EMAIL_TEMPLATE;
  return requestApiV3(cmd, { TemplateID: templateId })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function deleteTemplate(id: string) {
  const cmd = SES_CMD.DELETE_EMAIL_TEMPLATE;
  return requestApiV3(cmd, { TemplateID: id })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}
