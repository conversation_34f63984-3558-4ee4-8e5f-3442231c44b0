import React, { useState } from 'react';
import { Upload, Text, Button, Form } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import {
  base64Convertor,
  dataURItoBase64Content,
} from '@src/routes/template/utils';
import { Input } from '@tea/component';
import './style.less';

// size
const getSizeDisplay = (size: number) => {
  if (size >= 1024) {
    return `${Math.floor(size / 1024)}K`;
  }
  return `${size}B`;
};

interface Value {
  file?: File;
  base64?: string;
  content?: string;
}

interface Props {
  value: Value;
  onChange: any;
  propStatus: string;
}

export const TemplateFileUpload = (props: Props) => {
  const { onChange, value, propStatus, ...restProps } = props;
  const { file, content } = value || {};
  // console.log('TemplateFileUpload props=', props, file, (file?.hasOwnProperty('size')));
  // const file = value; // {name/size}
  const setValue = (value) => {
    if (onChange) onChange(value);
  };
  // const [file, setFile] = useState(value);
  const [status, setStatus] = useState(null);
  // const [percent, setPercent] = useState(null);

  // function handleStart(file, { xhr }) {
  //   setFile(file);
  //   onChange && onChange(file);
  //   setStatus("validating");
  //   xhrRef.current = xhr;
  //   xhr.abort();
  // }
  //
  // function handleProgress({ percent }) {
  //   setPercent(percent);
  // }
  //
  // function handleSuccess(result) {
  //   console.log("TemplateFileUpload handleSuccess", result);
  //   setStatus("success");
  // }

  // function handleError() {
  //   setStatus("error");
  //   Modal.alert({
  //     type: "error",
  //     message: "错误",
  //     description: "请求服务器失败",
  //   });
  // }

  const toBase64 = (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });

  async function beforeUpload(file, fileList, isAccepted) {
    if (!isAccepted) {
      setValue('');
      setStatus('error');
      throw false;
    }
    setStatus(null);
    let base64: any = await toBase64(file);
    base64 = dataURItoBase64Content(base64);
    const { content, errorStr } = base64Convertor.toText(base64);
    // console.log('errorStr--', errorStr);
    if (errorStr) {
      setValue('');
      setStatus('error');
      throw false;
    }
    setValue({ file, base64, content });
    throw false; // 让它不要走网络上传
    // return isAccepted;
  }

  // function handleAbort() {
  //   if (xhrRef.current) {
  //     xhrRef.current.abort();
  //   }
  //   setFile(null);
  //   setStatus(null);
  //   setPercent(null);
  // }

  return (
    <Form.Control
      status={status || propStatus}
      message={`${t('请上传HTML文件。HTML文件仅支持UTF-8编码，否则会乱码。')}
      ${t('文件大小不超过{{size}}。', { size: '400KB' })}`}
    >
      <Upload
        // action="https://run.mocky.io/v3/68ed7204-0487-4135-857d-0e4366b2cfad"
        // action={(file: File) => {
        //   return false;
        // }}
        // accept="text/plain,text/html"
        accept="text/html"
        maxSize={409600}
        // onStart={handleStart}
        // onProgress={handleProgress}
        // onSuccess={handleSuccess}
        // onError={handleError}
        beforeUpload={beforeUpload}
        {...restProps}
      >
        {({ open, isDragging }) => (
          <Upload.Dragger
            className="upload-dragger"
            filename={file?.name}
            // percent={percent}
            description={
              <>
                {file?.size && (
                  <>
                    <p>
                      {t('文件大小')}: {getSizeDisplay(file.size)}
                    </p>
                    {/* <p>上传日期：-</p>*/}
                  </>
                )}
                {content && (
                  <>
                    <Input
                      className="content-display-textarea"
                      multiline
                      readonly={true}
                      value={content}
                    />
                  </>
                )}
              </>
            }
            button={
              status === 'validating' ? (
                <Button
                  type="link"
                  // onClick={handleAbort}
                  htmlType="button"
                >
                  {t('取消上传')}
                </Button>
              ) : (
                <>
                  <Button
                    type="link"
                    htmlType="button"
                    onClick={() => {
                      open();
                      return false;
                    }}
                  >
                    {t('重新上传')}
                  </Button>
                  {/* <Button*/}
                  {/*  type="link"*/}
                  {/*  style={{ marginLeft: 8 }}*/}
                  {/*  onClick={() => setFile(null)}*/}
                  {/* >*/}
                  {/*  {t('删除')}*/}
                  {/* </Button>*/}
                </>
              )
            }
          >
            {isDragging ? (
              t('释放鼠标')
            ) : (
              <>
                <a onClick={open}>{t('点击上传')}</a>
                <Text theme="weak">/{t('拖拽到此区域')}</Text>
              </>
            )}
          </Upload.Dragger>
        )}
      </Upload>
    </Form.Control>
  );
};
