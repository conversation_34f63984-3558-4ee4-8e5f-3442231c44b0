import React, { useCallback, useEffect, useState } from 'react';
import { Button, Form, Input, Modal, Segment, StatusTip } from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import get from 'lodash/get';
import { getValidateStatus } from '@src/utils/FormUtils';
import { t } from '@tea/app/i18n';
import { TemplateFileUpload } from '@src/routes/template/components/NewTemplateModal/TemplateFileUpload';
import './style.less';
import { getTemplateInfo } from '@src/routes/template/actions';
import { HtmlPreviewModal } from '@src/routes/template/components/HtmlPreview';
import { base64Convertor } from '@src/routes/template/utils';
import { SesFormItem } from '@src/components/SesFormItem';

interface Props {
  values?: any;
  // options: any;
  visible: boolean;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

export const HTML_FLAGS = {
  text: 'text',
  html: 'html',
};

const DEFAULT_INIT_VALUES = {
  name: '',
  htmlFlag: HTML_FLAGS.html,
};

const NewTemplateModal: React.FC<Props> = (props) => {
  const { visible, onCancel, onSubmit, values: propsValues } = props;

  const { form, handleSubmit, validating, submitting, values } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: DEFAULT_INIT_VALUES,
    validate: ({ name, file, text, htmlFlag }: any) => {
      let nameError = undefined;
      if (!name) nameError = t('请输入模板名称');
      else if (name && name.length > 40) {
        nameError = t('模板名称过长');
      }
      return {
        name: nameError,
        file: htmlFlag === HTML_FLAGS.html && !file ? t('请上传文件') : undefined,
        text: htmlFlag === HTML_FLAGS.text && !text ? t('请输入邮件正文') : undefined,
      };
    },
  });

  const nameField = useField('name', form);
  const htmlFlagField = useField('htmlFlag', form);
  // const titleField = useField("title", form);
  const fileField = useField('file', form);
  const textField = useField('text', form);

  const [fetching, setFetching] = useState(false);
  const [loading, setLoading] = useState(true);

  const { TemplateID, TemplateName } = propsValues || {};
  // 打开modal的时候重置值，用于编辑
  useEffect(() => {
    if (visible) {
      if (TemplateID) {
        console.log('modal visible, values:', propsValues);
        setFetching(true);
        getTemplateInfo(TemplateID)
          .then((res) => {
            console.log('getTemplateInfo res!', res);
            const { TemplateContent = {} } = res;
            const { Html, Text } = TemplateContent;
            let htmlFlag = HTML_FLAGS.text;
            let file = null;
            if (Html) {
              htmlFlag = HTML_FLAGS.html;
              const { content } = base64Convertor.toText(Html);
              file = {
                base64: Html,
                content,
                file: {
                  // id: '1234567',
                  name: t('已上传的模板'),
                },
              };
            }
            const resetValues = {
              id: TemplateID,
              name: TemplateName,
              text: Text ? base64Convertor.toText(Text).content : '',
              htmlFlag,
              file,
            };
            form.reset(resetValues);
          })
          .finally(() => {
            setFetching(false);
            setLoading(false);
          });
      } else {
        setLoading(false);
      }
    } else {
      // 关窗口的时候reset
      setTimeout(() => {
        form.reset(DEFAULT_INIT_VALUES);
      });
    }
  }, [visible, TemplateID]);

  const content = get(values, 'file.content');
  const previewHtmlTemplate = useCallback(() => {
    const modal = Modal.show({
      caption: t('邮件模板预览'),
      size: 'auto',
      children: (
        <HtmlPreviewModal
          html={content}
          // onClose={() => modal.destroy()}
        />
      ),
      onClose: () => modal.destroy(),
    });
  }, [content]);

  const { htmlFlag } = values;

  return (
    <Modal
      className="edit-template-modal"
      visible={visible}
      size={500}
      caption={TemplateID ? t('邮件模板详情') : t('新建邮件模板')}
      onClose={onCancel}
    >
      {loading && <StatusTip status="loading" />}
      {!loading && (
        <form onSubmit={handleSubmit}>
          <Modal.Body>
            <Form
            // layout="vertical"
            >
              <Form.Item
                label={t('模板名称')}
                required={true}
                // label="昵称"
                status={getValidateStatus(nameField.meta, validating)}
                message={getValidateStatus(nameField.meta, validating) === 'error' && nameField.meta.error}
              >
                <Input
                  {...nameField.input}
                  // placeholder={t("")}
                  autoComplete="off"
                />
              </Form.Item>
              <Form.Item
                label={t('模板类型')}
                required={true}
                status={getValidateStatus(htmlFlagField.meta, validating)}
                message={getValidateStatus(htmlFlagField.meta, validating) === 'error' && htmlFlagField.meta.error}
              >
                <Segment
                  {...htmlFlagField.input}
                  options={[
                    { text: t('HTML富文本'), value: HTML_FLAGS.html },
                    { text: t('纯文本'), value: HTML_FLAGS.text },
                  ]}
                />
              </Form.Item>
              {/* <Form.Item*/}
              {/*  // label="昵称"*/}
              {/*  status={getValidateStatus(titleField.meta, validating)}*/}
              {/*  message={getValidateStatus(titleField.meta, validating) === "error" && titleField.meta.error}*/}
              {/* >*/}
              {/*  <Input*/}
              {/*    {...titleField.input}*/}
              {/*    // placeholder={t("")}*/}
              {/*    autoComplete="off"*/}
              {/*  />*/}
              {/* </Form.Item>*/}
              <Form.Item
                label={htmlFlag === HTML_FLAGS.text ? t('邮件正文') : t('邮件摘要')}
                required={htmlFlag === HTML_FLAGS.text}
                // label="昵称"
                status={getValidateStatus(textField.meta, validating)}
                message={getValidateStatus(textField.meta, validating) === 'error' && textField.meta.error}
              >
                <Input
                  multiline
                  {...textField.input}
                  // placeholder={t("")}
                  autoComplete="off"
                />
              </Form.Item>
              {htmlFlag === 'html' && (
                <SesFormItem
                  label={t('邮件正文')}
                  required={true}
                  // label="昵称"
                  // status={getValidateStatus(fileField.meta, validating)}
                  // message={getValidateStatus(fileField.meta, validating) === 'error' && fileField.meta.error}
                  // suffix={t('请上传html文件')}
                  // 临时解决方案。更好的解决需要 TODO: 找到大括号转义方式
                >
                  <TemplateFileUpload {...fileField.input} propStatus={getValidateStatus(fileField.meta, validating)} />
                </SesFormItem>
              )}
              <Form.Item
                label=""
                suffix={t(
                  '邮件内容中的变量使用{{变量名}}表示，如：尊敬的{{name}}。变量名仅支持大小写字母（a-z、A-Z）、数字（0-9）和下划线。',
                  { interpolation: { prefix: '{{{' } },
                )} // 修改默认变量前缀，防止{{}}内容被当成变量转换
              ></Form.Item>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button type="primary" htmlType="submit" loading={submitting} disabled={validating || fetching}>
              {t('提交')}
            </Button>
            {htmlFlag === HTML_FLAGS.html && (
              <Button
                htmlType="button"
                disabled={validating || fetching || submitting || !content}
                onClick={previewHtmlTemplate}
              >
                {t('预览')}
              </Button>
            )}
            <Button type="weak" htmlType="button" onClick={onCancel}>
              {t('取消')}
            </Button>
          </Modal.Footer>
        </form>
      )}
    </Modal>
  );
};

export default NewTemplateModal;
