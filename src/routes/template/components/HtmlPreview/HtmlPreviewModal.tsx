import React from 'react';
import { Modal } from '@tencent/tea-component';
import './style.less';

const generateTemplatePreview = (html) => {
  const decodedHtml = html;
  return () => (<div dangerouslySetInnerHTML={{ __html: decodedHtml }} />);
};

export function HtmlPreviewModal({ html }) {
  const Template = html ? generateTemplatePreview(html) : null;
  return (
    <>
      <Modal.Body className='html-preview-modal-body'>
        {Template ? <Template /> : null}
      </Modal.Body>
      {/* <Modal.Footer>*/}
      {/*  <Button onClick={onClose}>关闭</Button>*/}
      {/* </Modal.Footer>*/}
    </>
  );
}
