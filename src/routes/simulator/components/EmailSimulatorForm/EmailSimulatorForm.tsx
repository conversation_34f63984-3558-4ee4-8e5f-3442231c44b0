import React, { useEffect } from 'react';
import { Button, Form, Select, Input } from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import './style.less';
import { getValidateStatus } from '@src/utils/FormUtils';

interface Props {
  values?: any;
  // options: any;
  onSubmit: (values: any) => void;
  className?: string;
  // onCancel: () => void;
}

const sceneList = [
  { value: '0', text: t('成功发送') },
  { value: '1', text: t('退订') },
  { value: '2', text: t('软弹回') },
  { value: '3', text: t('硬弹回') },
  { value: '4', text: t('全局黑名单') },
];

const receiverList = {
  0: '<EMAIL>',
  1: '<EMAIL>',
  2: '<EMAIL>',
  3: '<EMAIL>',
  4: '<EMAIL>',
};

const EmailSimulatorForm: React.FC<Props> = (props) => {
  const { onSubmit, className } = props;

  useEffect(() => {}, []);

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    initialValuesEqual: () => true,
    initialValues: { Scene: '0', Destination: receiverList[0] },
    validate: ({ Destination }: any) => ({
      Destination: !Destination ? true : undefined,
    }),
  });

  const sceneField = useField('Scene', form);
  const destinationField = useField('Destination', form);

  return (
    <form onSubmit={handleSubmit} className={`send-email-form ${className}`}>
      <Form layout="fixed">
        <Form.Item
          label={t('场景模拟')}
          required={true}
          status={getValidateStatus(sceneField.meta, validating)}
          message={
            getValidateStatus(sceneField.meta, validating) === 'error' &&
            sceneField.meta.error
          }
        >
          <Select
            {...sceneField.input}
            options={sceneList}
            onChange={(value) => {
              form.change('Scene', value);
              form.change('Destination', receiverList[value]);
            }}
            placeholder={t('请选择...')}
          />
        </Form.Item>
        <Form.Item label={t('发信地址')}>
          <Input />
        </Form.Item>
        <Form.Item
          label={t('接受者地址')}
          required={true}
          status={getValidateStatus(destinationField.meta, validating)}
          message={
            getValidateStatus(destinationField.meta, validating) === 'error' &&
            destinationField.meta.error
          }
        >
          <Input
            disabled={true}
            {...destinationField.input}
            autoComplete="off"
          />
        </Form.Item>
      </Form>
      <Form.Action>
        {/* <div className="send-submit-action">*/}
        {/*  <div className="send-submit-btns">*/}
        <Button
          className="send-submit-btn"
          type="primary"
          htmlType="submit"
          loading={submitting}
          disabled={validating}
        >
          {t('发送')}
        </Button>
        {/* </div>*/}
        {/* </div>*/}
      </Form.Action>
    </form>
  );
};

export default EmailSimulatorForm;
