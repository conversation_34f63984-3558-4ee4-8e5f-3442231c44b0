import React, { useCallback, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Layout } from '@tencent/tea-component';
import { Card, message, Form, TextArea } from '@tea/component';
import './style.less';
import EmailSimulatorForm from './components/EmailSimulatorForm/EmailSimulatorForm';
import { emailSimulator } from './actions';
import RegionSelect from '@src/components/RegionSelect/Page';

const { Body, Content } = Layout;

export const EmailSimulatorPage = () => {
  const [result, setResult] = useState<any>();
  const onCreateSubmit = useCallback((values) => {
    console.log('onCreateSubmit', values);
    return emailSimulator(values)
      .then((res) => {
        setResult(res);
        console.log('sendEmail Over', res);
        message.success({ content: t('模拟成功') });
      })
      .catch(() => {})
      .finally(() => {});
  }, []);

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('邮件模拟器')} subtitle={<RegionSelect />} />
            <Content.Body>
              {/* 内容区域一般使用 Card 组件显示内容 */}
              <Card className="send-form-page-card">
                <Card.Body>
                  <EmailSimulatorForm className="send-form" onSubmit={onCreateSubmit} />
                </Card.Body>
              </Card>

              <Card className="send-form-page-card">
                <Card.Body>
                  <Form>
                    <Form.Item label={t('代码及描述')}>
                      <TextArea disabled={true} value={JSON.stringify(result)} />
                    </Form.Item>
                  </Form>
                </Card.Body>
              </Card>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
};
