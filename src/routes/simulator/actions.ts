import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';

export function emailSimulator(data: any) {
  console.log('EmailSimulator start data=', data);
  const cmd = SES_CMD.EMAIL_SIMULATOR;
  // const { FromEmailAddress, Subject, Destination, TemplateID, TemplateData, Unsubscribe } = data;
  const { Destination } = data;
  // const TemplateData = JSON.stringify({});
  const postData = {
    // FromEmailAddress,
    // Subject,
    Destination,
    // Unsubscribe,
    // Template: {
    //   TemplateID,
    //   TemplateData: TemplateData || '{}',
    // },
    // ReplyToAddresses,
    // Simple,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      console.log('emailSimulator catch', e);
      // throw e; // 不自动弹提示，错误的时候上面会出通用提示
      throw handleRequestError(e);
    });
}
