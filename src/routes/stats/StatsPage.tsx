import React from 'react';
import { t } from '@tea/app/i18n';
import { Layout } from '@tencent/tea-component';
// import { TabPanel, Tabs, Card } from '@tea/component';
// import { StatusTable } from '@src/routes/stats/components/StatusTable';
import NewStatusTable from '@src/routes/stats/components/NewStatusTable';
import RegionSelect from '@src/components/RegionSelect/Page';

// import { BlackListTable } from '@src/routes/stats/components/BlackListTable';
import './style.less';

const { Body, Content } = Layout;

export function StatsPage() {
  // const tabs = [
  //   { id: 'basic', label: t('发送总览') },
  //   { id: 'blacklist', label: t('黑名单列表') },
  // ];
  const dataLabelMap = {
    SentCount: t('发送数量'),
    DroppedCount: t('拒绝发送数量'),
    ValidCount: t('计费邮件数量'),
    InvalidRcptCount: t('无效地址数量'),
    SpamCount: t('垃圾邮件退信数量'),
    OtherBounceCount: t('其他原因退信数量'),
    DeliveredCount: t('送达数量'),
  };

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('发信数据')} subtitle={<RegionSelect />} />
            <Content.Body>
              <NewStatusTable
                dataLabelMap={dataLabelMap}
                needDeliveredRate={true}
                showDownload
              />
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
}
