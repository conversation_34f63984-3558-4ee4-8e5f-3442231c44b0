import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';
import _ from 'lodash';

export function searchQuery(data: any) {
  console.log('searchQuery start data=', data);
  const cmd = SES_CMD.GET_STATISTICS_REPORT;
  const { StartDate, EndDate, Domain, ReceivingMailboxType } = data;
  const postData = {
    StartDate,
    EndDate,
    Domain,
    ReceivingMailboxType,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function getDeliveryStatistic(data: any) {
  console.log('searchQuery start data=', data);
  const cmd = SES_CMD.GET_DELIVERY_STATISTIC;
  return requestApiV3(cmd, data)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function getDeliveryStatisticByMonth(data: any) {
  console.log('searchQuery start data=', data);
  const cmd = SES_CMD.GET_DELIVERY_STATISTIC_BY_MONTH;
  return requestApiV3(cmd, data)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function searchBlackList(data: any) {
  console.log('searchBlackList start data=', data);
  const cmd = SES_CMD.LIST_BLACKLIST;
  const {
    StartDate,
    EndDate,
    EmailAddress,
    Offset = 0,
    Limit = 10000,
    TaskID,
  } = data;
  const postData = {
    StartDate,
    EndDate,
    EmailAddress,
    Offset,
    Limit,
    TaskID,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function deleteBlackList(data: any) {
  console.log('deleteBlackList start data=', data);
  const cmd = SES_CMD.DELETE_BLACKLIST;
  const postData = {
    EmailAddressList: data,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function downloadDeliveryStatistic(data: any) {
  const cmd = SES_CMD.DOWNLOAD_DELIVERY_STATISTIC;
  return requestApiV3(cmd, data)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function downloadTraceStatistic(data: any) {
  const cmd = SES_CMD.DOWNLOAD_TRACE_STATISTIC;
  return requestApiV3(
    cmd,
    _.pickBy(data, (v) => !_.isNil(v) && v !== ''),
  )
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function getStatisticsLink(data: any) {
  const cmd = SES_CMD.GET_STATISTICS_LINK;
  return requestApiV3(cmd, data)
    .then((res: any) => {
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function updateStatisticsLink(data: any) {
  const cmd = SES_CMD.UPDATE_STATISTICS_LINK;
  return requestApiV3(cmd, data)
    .then((res: any) => {
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}
