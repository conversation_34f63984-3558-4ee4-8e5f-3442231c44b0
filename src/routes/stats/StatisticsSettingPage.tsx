import React, { useMemo } from 'react';
import { t } from '@tencent/tea-app/lib/i18n';
import { Layout } from '@tencent/tea-component';
import _ from 'lodash';
import DomainSettingTable from './components/DomainSettingTable';
import AccountSettingCard from './components/AccountSettingCard';
import RegionSelect from '@src/components/RegionSelect/Page';
import { useAsyncRetry } from 'react-use';
import { getStatisticsLink } from './actions';

export interface FlagType {
  OpenFlag: 0 | 1;
  ClickFlag: 0 | 1;
}

const { Body, Content } = Layout;

const StatisticsSettingPage = () => {
  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetry(async () => {
    const res = await getStatisticsLink({});
    return res ?? {};
  }, []);

  const initFlags = useMemo(() => {
    return { ..._.pick(state, ['OpenFlag', 'ClickFlag']) };
  }, [state]);

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('统计设置')} subtitle={<RegionSelect />} />
            <Content.Body>
              <AccountSettingCard
                info={initFlags}
                loading={loading}
                retry={retry}
              />
              <DomainSettingTable
                list={state?.DomainStatisticsLinks ?? []}
                loading={loading}
                retry={retry}
                initFlags={initFlags}
              />
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
};
export default StatisticsSettingPage;
