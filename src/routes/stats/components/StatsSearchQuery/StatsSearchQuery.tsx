import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Justify, Select } from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import './style.less';
import { RangePicker } from '@tea/component/datepicker/RangePicker';
import { fetchDomainList } from '@src/routes/domain/actions';

interface Props {
  initialValues?: any;
  // values?: any,
  // options: any;
  onSubmit: (values: any) => void;
}

const StatsSearchQuery: React.FC<Props> = (props) => {
  const { onSubmit, initialValues = {} } = props;

  const [domainOptions, setDomainOptions] = useState([
    { value: t('加载中...'), disabled: true },
  ]);

  useEffect(() => {
    fetchDomainList().then((res) => {
      const { EmailIdentities = [] } = res || {};
      let domains = EmailIdentities.filter(
        ({ SendingEnabled }) => SendingEnabled,
      ).map((one: any) => {
        const { IdentityName } = one;
        return { value: IdentityName, text: IdentityName };
      });
      domains = [{ value: '', text: t('所有') }, ...domains];
      console.log('domains:', domains);
      setDomainOptions(domains);
    });
  }, []);

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues,
    validate: () => ({
      domain: undefined,
      timeRange: !timeRange ? t('请选择时间段') : undefined,
    }),
  });

  const domainField = useField('domain', form);
  // const titleField = useField("title", form);
  const timeRange = useField('timeRange', form);
  const receiveDomainField = useField('receiveDomain', form);

  return (
    <form onSubmit={handleSubmit} className="stats-search-query">
      <Justify
        left={
          <Form layout="inline">
            <Form.Item label={t('发信域名')}>
              <Select
                className="select-field"
                {...domainField.input}
                type="simulate"
                appearance="button"
                options={domainOptions}
                // value={favorite}
                // onChange={value => setFavorite(value)}
                placeholder={t('请选择域名')}
              />
            </Form.Item>
            <Form.Item
              label={t('日期范围')}
              // label="昵称"
              // status={getValidateStatus(fileField.meta, validating)}
              // message={getValidateStatus(fileField.meta, validating) === "error" && fileField.meta.error}
              // suffix={t("请上传txt或者html文件。如涉及变量，请使用{{变量名}}表示，如：尊敬的{{姓名}}", { "变量名": "{{变量名}}", "姓名": "{{姓名}}" })}
            >
              <RangePicker
                {...timeRange.input}
                // defaultValue={[moment().add(-3, 'd'), moment()]}
                // onChange={value =>
                //   console.log(
                //     value[0].format("YYYY/MM/DD"),
                //     value[1].format("YYYY/MM/DD")
                //   )
                // }
                // onOpenChange={open => console.log(open ? "open" : "close")}
              />
            </Form.Item>
            <Form.Item label={t('收信域名')}>
              <Input
                {...receiveDomainField.input}
                // type="simulate"
                // appearance="button"
                // options={domainOptions}
                // value={favorite}
                // onChange={value => setFavorite(value)}
                placeholder={t('输入收信域名')}
              />
            </Form.Item>
          </Form>
        }
        right={
          <div className="right-search-btn-field">
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              disabled={validating}
            >
              {t('查询')}
            </Button>
          </div>
        }
      />
    </form>
  );
};

export default StatsSearchQuery;
