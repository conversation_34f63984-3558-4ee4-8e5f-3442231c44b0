import React from 'react';
import { Button, Form, Justify, SearchBox } from '@tea/component';
import { useField, FormRenderProps } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import './style.less';
import { RangePicker } from '@tea/component/datepicker/RangePicker';

interface Props {
  formRenderProps: FormRenderProps<any>;
}

const BlackListSearchQuery: React.FC<Props> = (props) => {
  const { formRenderProps } = props;
  const { form, handleSubmit, validating, submitting } = formRenderProps;

  const timeRange = useField('timeRange', form);
  const receiveEmailAddress = useField('emailAddress', form);

  return (
    <form onSubmit={handleSubmit} className="stats-search-query">
      {/* <Table.ActionPanel>*/}
      <Justify
        left={
          <>
            <Form layout="inline">
              <Form.Item label={t('日期范围')}>
                <RangePicker {...timeRange.input} />
              </Form.Item>
              <Form.Item label={t('收件邮箱地址')}>
                <SearchBox
                  className="email-address-search-box"
                  size="m"
                  {...receiveEmailAddress.input}
                  placeholder={t('<EMAIL>')}
                />
              </Form.Item>
            </Form>
          </>
        }
        right={
          <div className="right-search-btn-field">
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              disabled={validating}
            >
              {t('查询')}
            </Button>
          </div>
        }
      />
      {/* </Table.ActionPanel>*/}

      {/* <Form.Action>*/}
      {/*  */}
      {/* </Form.Action>*/}
    </form>
  );
};

export default BlackListSearchQuery;
