import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { t, Trans } from '@tea/app/i18n';
import { Button, Card, List, Table } from '@tencent/tea-component';
import _ from 'lodash';
import {
  Alert,
  // Bubble, Icon,
  StatusTip,
} from '@tea/component';
import StatsSearchQuery from '@src/routes/stats/components/NewSearchQuery';
import {
  getDeliveryStatistic,
  getDeliveryStatisticByMonth,
} from '@src/routes/stats/actions';
import moment from 'moment';
import { BasicLine, MetaData } from '@tencent/tea-chart/lib';
import { useHistory } from '@tencent/tea-app';
import { FormApi } from 'final-form';
import { TimeRangeType } from '../../const';

const { pageable } = Table.addons;

const initialQueryValues = {
  fromDomain: '',
  timeRange: [moment().add(-3, 'd'), moment()],
  shortRecipientDomain: '',
  taskTag: '',
  templateId: 0,
  isMonth: TimeRangeType.day,
};

export default function StatusTable({
  dataLabelMap,
  needDeliveredRate = false,
  showDownload,
  headerRenderMap = {},
  customButtonRender,
  alert,
}: {
  dataLabelMap: { [k: string]: string };
  headerRenderMap?: Record<string, React.ReactNode>;
  needDeliveredRate?: boolean;
  showDownload?: boolean;
  alert?: string;
  customButtonRender?: (form: FormApi<any, Partial<any>>) => React.ReactNode;
}) {
  const history = useHistory();
  const [listLoading, setListLoading] = useState(true);
  const [listData, setData] = useState([]);
  const queryValues = initialQueryValues;

  useEffect(() => {
    statsQuery(queryValues);
  }, []);

  function statsQuery(values) {
    const {
      fromDomain,
      timeRange = [],
      shortRecipientDomain,
      templateId,
      taskTag,
      isMonth,
    } = values;
    const [BeginDate, EndDate] = timeRange.map((one) =>
      one.format('YYYY-MM-DD'),
    );
    const data = {
      FromDomain: fromDomain,
      ShortRecipientDomain: shortRecipientDomain,
      BeginDate,
      EndDate,
      TemplateId: Number(templateId),
      TaskTag: taskTag,
    };
    setListLoading(true);
    return (
      isMonth === TimeRangeType.day
        ? getDeliveryStatistic
        : getDeliveryStatisticByMonth
    )(data)
      .then((res) => {
        console.log('getDeliveryStatistic Over', res);
        const { Data = [] } = res;
        setData(Data);
        // setCreateModalVisible(false);
      })
      .catch(() => {})
      .finally(() => {
        setListLoading(false);
      });
  }

  const onSearchSubmit = useCallback((values) => {
    console.log('onSearchSubmit', values);
    return statsQuery(values);
  }, []);

  const totalData = useMemo(() => {
    if (!listData.length || listLoading) return [];

    const total = _.reduce(
      listData,
      (acc, curr) => {
        _.forEach(curr, (value, key) => {
          // 排除 Date 字段并确保值是数字
          if (key !== 'Date' && _.isNumber(value)) {
            acc[key] = (acc[key] || 0) + value;
          }
        });
        return acc;
      },
      { Date: t('总') },
    );

    return [total];
  }, [listData, listLoading]);

  // const dataLabelMap = {
  //   AcceptedCount: t('发送数量'),
  //   BounceCount: t('退信数量'),
  //   ClickCount: t('点击链接用户数'),
  //   DeliveredCount: t('送达数量'),
  //   OpenCount: t('打开邮件用户数'),
  //   RequestCount: t('调用数量'),
  //   UnsubscribeCount: t('退订数量'),
  //   SentCount: t('发送数量'),
  //   ValidCount: t('计费邮件数量'),
  //   DroppedCount: t('拒绝发送数量'),
  //   ComplaintCount: t('垃圾投诉数量'),

  // };

  const lineData = useMemo(() => {
    const lineData = [];
    listData.forEach((one) => {
      const { Date, ...others } = one;
      Object.keys(dataLabelMap).forEach((key) => {
        // 去掉显示软退信
        if (key !== 'DeferredCount') {
          lineData.push({
            type: key,
            value: others[key],
            date: Date,
          });
        }
      });
    });
    return lineData;
  }, [listData]);

  const columns = [
    {
      key: 'Date',
      header: t('日期'),
    },
    ...Object.keys(dataLabelMap).map((key) => ({
      key,
      header: headerRenderMap[key] ?? dataLabelMap[key],
    })),
    needDeliveredRate && {
      key: 'Rate',
      header: t('送达率'),
      render: (item) => {
        const { ValidCount, DeliveredCount } = item;
        let rate = '0.00';
        if (ValidCount !== 0)
          rate = ((DeliveredCount / ValidCount) * 100).toFixed(2);
        return <span>{rate}%</span>;
      },
    },
  ].filter((r) => r);

  return (
    <div>
      {alert === 'mail' ? (
        <Alert>
          <h4>{t('注意事项')}</h4>
          <List>
            <List.Item>
              {t('数据统计存在一定时间延迟，实时数据仅供参考使用。')}
            </List.Item>
            <List.Item>
              <Trans>
                邮件跟踪数据统计，支持对账号级及域名级自助设置
                是否统计邮件打开用户及点击用户数，
                <Button
                  type="link"
                  style={{ verticalAlign: 'baseline' }}
                  onClick={() => {
                    history.push('/ses/statistics-setting');
                  }}
                >
                  去设置
                </Button>
                。
              </Trans>
            </List.Item>
            <List.Item>
              {t(
                '如设置关闭邮件的跟踪数据统计能力，则对应账号或发信域名的邮件打开用户数或点击链接用户数不会被统计。',
              )}
            </List.Item>
          </List>
        </Alert>
      ) : (
        <Alert hideIcon={true}>
          <Trans>数据统计存在一定时间延迟，实时数据仅供参考使用。</Trans>
        </Alert>
      )}
      <Card>
        <Card.Body className="flex-one-row">
          <StatsSearchQuery
            onSubmit={onSearchSubmit}
            initialValues={queryValues}
            showDownload={showDownload}
            customButtonRender={customButtonRender}
          />
        </Card.Body>
      </Card>
      <Card>
        <Card.Body>
          <BasicLine
            connectNulls={true}
            height={250}
            position="date*value"
            dataSource={lineData}
            color="type"
            tooltip={{
              enableSort: true,
              header: { typeText: t('类型'), valueText: t('值') },
              formatter: (values: MetaData[]) =>
                values.map((one) => {
                  const { label, ...others } = one;
                  return { ...others, label: dataLabelMap[label] };
                }),
            }}
            legend={{
              formatter: (label: string) => dataLabelMap[label],
            }}
            canvasMode
            scale={{
              time: {
                type: 'time',
                timeParseFormat: 'YYYY-MM-DD',
              },
            }}
          />
        </Card.Body>
      </Card>
      <Card>
        <Table
          verticalTop
          records={totalData}
          recordKey="Date"
          columns={columns}
        />
        <Table
          hideHeader
          verticalTop
          records={listData}
          recordKey="Date"
          // rowDisabled={record => record.status === "start"}
          // rowClassName={record => record.status}
          topTip={
            listLoading && (
              <StatusTip
                status={listLoading ? 'loading' : 'found'}
                // onClear={() => setStatus("loading")}
                // onRetry={() => setStatus("loading")}
              />
            )
          }
          columns={columns}
          addons={[pageable()]}
        />
      </Card>
    </div>
  );
}
