import React, { useMemo, useState } from 'react';
import { Modal, Button } from '@tencent/tea-component';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import { t } from '@tencent/tea-app/lib/i18n';
import { Trans } from '@tea/app/i18n';
import { useHistory } from '@tencent/tea-app';
import { updateStatisticsLink } from '../actions';
import { FlagType } from '../StatisticsSettingPage';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export const AccountConfirmDialog = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [visible, setShowState, defaultVal] = useDialog<{
    type: string;
    value: 0 | 1;
    initflags: FlagType;
  }>(dialogRef);

  const { type, value, initflags } = defaultVal;

  const { message, description } = useMemo(() => {
    function getModalTitle(type, value) {
      switch (type) {
        case 'OpenFlag':
          return {
            message: value
              ? t('开启打开邮件用户数统计')
              : t('不再统计打开邮件用户数'),
            description: value ? (
              <p>
                <Trans>
                  开启后，您可在
                  <Button
                    type="link"
                    style={{ verticalAlign: 'baseline' }}
                    onClick={() => {
                      setShowState(false);
                      history.push('/ses/mail-tracking');
                    }}
                  >
                    邮件跟踪数据
                  </Button>
                  页面查看本腾讯云账号下所有发信域名发送的邮件的打开用户数
                </Trans>
              </p>
            ) : (
              t(
                '关闭后，将不会统计本腾讯云账号下所有发信域名发送的邮件的打开用户数',
              )
            ),
          };
        case 'ClickFlag':
          return {
            message: value
              ? t('开启链接点击用户数统计')
              : t('不再统计链接点击用户数'),
            description: value ? (
              <p>
                <Trans>
                  开启后，您可在
                  <Button
                    type="link"
                    style={{ verticalAlign: 'baseline' }}
                    onClick={() => {
                      setShowState(false);
                      history.push('/ses/mail-tracking');
                    }}
                  >
                    邮件跟踪数据
                  </Button>
                  页面查看本腾讯云账号下所有发信域名发送的邮件的链接点击用户数
                </Trans>
              </p>
            ) : (
              t(
                '关闭后，将不会统计本腾讯云账号下所有发信域名发送的邮件的链接点击用户数',
              )
            ),
          };
        default:
          return {};
      }
    }

    return getModalTitle(type, value);
  }, [type, value, history, setShowState]);

  const handlerSubmit = async () => {
    setLoading(true);
    updateStatisticsLink({
      ...initflags,
      [type]: value,
    })
      .then(() => {
        setShowState(false);
        onSuccess();
      })
      .catch(() => {})
      .then(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={message}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <div>{description}</div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="weak"
            htmlType="button"
            onClick={() => {
              setShowState(false);
            }}
            loading={loading}
          >
            {t('取消')}
          </Button>
          <Button type="primary" onClick={handlerSubmit} loading={loading}>
            {t('确定')}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
