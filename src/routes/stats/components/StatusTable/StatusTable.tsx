import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { t, Trans } from '@tea/app/i18n';
import { Card, Table } from '@tencent/tea-component';
import { Alert, Bubble, Icon, StatusTip } from '@tea/component';
import StatsSearchQuery from '@src/routes/stats/components/StatsSearchQuery/StatsSearchQuery';
import { searchQuery } from '@src/routes/stats/actions';
import moment from 'moment';
import { BasicLine, MetaData } from '@tencent/tea-chart/lib';

const { pageable } = Table.addons;

const initialQueryValues = {
  domain: '',
  timeRange: [moment().add(-3, 'd'), moment()],
  receiveDomain: '',
};

export function StatusTable() {
  // const [list, setList] = useState([]);
  // const [newModalVisible, setCreateModalVisible] = useState(false);
  const [listLoading, setListLoading] = useState(true);
  const [data, setData] = useState({
    DailyVolumes: [],
    OverallVolume: {},
  });
  const { DailyVolumes = [] } = data || {};
  const queryValues = initialQueryValues;

  useEffect(() => {
    statsQuery(queryValues);
  }, []);

  function statsQuery(values) {
    const { domain, timeRange = [], receiveDomain } = values;
    const [StartDate, EndDate] = timeRange.map(one => one.format('YYYY-MM-DD'));
    const data = { StartDate, EndDate, Domain: domain, ReceivingMailboxType: receiveDomain };
    setListLoading(true);
    return searchQuery(data).then((res) => {
      console.log('searchQuery Over', res);
      setData(res);
      // setCreateModalVisible(false);
      // fetchList();
    })
      .catch(() => {

      })
      .finally(() => {
        setListLoading(false);
      });
  }

  const onSearchSubmit = useCallback((values) => {
    console.log('onSearchSubmit', values);
    return statsQuery(values);
  }, []);

  const dataLabelMap = {
    AcceptedCount: t('发送数量'),
    BounceCount: t('退信数量'),
    ClickedCount: t('点击链接用户数'),
    DeliveredCount: t('送达数量'),
    OpenedCount: t('打开邮件用户数'),
    RequestCount: t('调用数量'),
    UnsubscribeCount: t('退订数量'),
  };

  const lineData = useMemo(() => {
    const data = [];
    DailyVolumes.forEach((one) => {
      const { SendDate, ...others } = one;
      Object.keys(others).forEach((key) => {
        data.push({
          type: key,
          value: others[key],
          date: SendDate,
        });
      });
    });
    return data;
  }, [DailyVolumes]);

  return (
    <div>
      {/* <Table.ActionPanel>*/}
      {/*  <Justify*/}
      {/*    left={*/}
      {/*      <>*/}
      {/*        <Button type="primary" onClick={() => {*/}
      {/*          setCreateModalVisible(true);*/}
      {/*        }}>{t("新建")}</Button>*/}
      {/*        /!*<Button>开机</Button>*!/*/}
      {/*      </>*/}
      {/*    }*/}
      {/*    right={*/}
      {/*      <>*/}
      {/*        <SearchBox />*/}
      {/*        /!*<Button icon="setting" />*!/*/}
      {/*        /!*<Button icon="refresh" />*!/*/}
      {/*        /!*<Button icon="download" />*!/*/}
      {/*      </>*/}
      {/*    }*/}
      {/*  />*/}
      {/* </Table.ActionPanel>*/}
      <Alert hideIcon={true}>
        <Trans>
          数据统计存在一定时间延迟，实时数据仅供参考使用。
        </Trans>
      </Alert>
      <Card>
        <Card.Body className='flex-one-row'>
          <StatsSearchQuery onSubmit={onSearchSubmit} initialValues={queryValues} />
        </Card.Body>
      </Card>
      <Card>
        <Card.Body>
          <BasicLine
            connectNulls={true}
            height={250}
            position="date*value"
            dataSource={lineData}
            color="type"
            tooltip={{
              enableSort: true,
              header: { typeText: t('类型'), valueText: t('值') },
              formatter: (values: MetaData[]) => values.map((one) => {
                const { label, ...others } = one;
                return { ...others, label: dataLabelMap[label] };
              }),
            }}
            legend={{
              formatter: (label: string) => dataLabelMap[label],
            }}
            canvasMode
            scale={{
              time: {
                type: 'time',
                timeParseFormat: 'YYYY-MM-DD',
              },
              // type: {
              //   // alias: LangLocal("staticIntentDesc12"),
              //   alias: '什么'
              // }
              // value: { stack },
            }}
          />
        </Card.Body>
      </Card>
      <Card>
        <Table
          verticalTop
          records={data.DailyVolumes}
          recordKey="SendDate"
          // rowDisabled={record => record.status === "start"}
          // rowClassName={record => record.status}
          topTip={
            listLoading && (
              <StatusTip
                status={listLoading ? 'loading' : 'found'}
                // onClear={() => setStatus("loading")}
                // onRetry={() => setStatus("loading")}
              />
            )
          }
          columns={[
            {
              key: 'SendDate',
              header: t('日期'),
            },
            {
              key: 'RequestCount',
              header: dataLabelMap.RequestCount,
            },
            {
              key: 'AcceptedCount',
              header: dataLabelMap.AcceptedCount,
            },
            {
              key: 'DeliveredCount',
              header: dataLabelMap.DeliveredCount,
            },
            {
              key: 'OpenedCount',
              // header: t("打开数"),
              header: () => (
                <>
                  {dataLabelMap.OpenedCount}
                  <Bubble content={t('打开邮件的用户数量，根据收件人去重')}>
                    <Icon type="info" />
                  </Bubble>
                </>
              ),
            },
            {
              key: 'ClickedCount',
              header: dataLabelMap.ClickedCount,
            },
            {
              key: 'BounceCount',
              header: dataLabelMap.BounceCount,
            },
            {
              key: 'UnsubscribeCount',
              header: dataLabelMap.UnsubscribeCount,
            },
          ]}
          addons={[pageable()]}
        />
      </Card>
      {/* <Card>*/}
      {/*  <Card.Body>*/}
      {/*    {JSON.stringify(data)}*/}
      {/*  </Card.Body>*/}
      {/* </Card>*/}

    </div>);
}
