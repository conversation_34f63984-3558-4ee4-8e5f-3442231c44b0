import React from 'react';
import { t } from '@tencent/tea-app/lib/i18n';
import { Card, H3, Switch } from '@tencent/tea-component';
import { useDialogRef } from '@src/routes/global-components/useDialog';
import { AccountConfirmDialog } from './AccountConfirmDialog';

const AccountSettingCard = ({ info, loading, retry }) => {
  const dialogRef = useDialogRef();

  async function handleChange(type: string, value: boolean) {
    console.log(info);
    dialogRef.current.open({
      type,
      value: value ? 1 : 0,
      initflags: info,
    });
  }

  return (
    <Card>
      <Card.Header>
        <H3>{t('账号级跟踪数据统计设置')}</H3>
      </Card.Header>
      <Card.Body>
        <p>
          <Switch
            value={info?.OpenFlag}
            disabled={loading}
            onChange={(value: boolean) => {
              handleChange('OpenFlag', value);
            }}
          >
            {t('打开邮件用户数统计')}
          </Switch>
        </p>
        <p style={{ paddingTop: 16 }}>
          <Switch
            value={info?.ClickFlag}
            disabled={loading}
            onChange={(value: boolean) => {
              handleChange('ClickFlag', value);
            }}
          >
            {t('点击链接用户数统计')}
          </Switch>
        </p>
      </Card.Body>
      <AccountConfirmDialog dialogRef={dialogRef} onSuccess={retry} />
    </Card>
  );
};
export default AccountSettingCard;
