import React, { useMemo, useState } from 'react';
import { useSetState } from 'react-use';
import { t } from '@tencent/tea-app/lib/i18n';
import {
  Button,
  Card,
  H3,
  Justify,
  SearchBox,
  Table,
  Text,
} from '@tencent/tea-component';
import _ from 'lodash';
import { pageable, selectable } from '@tencent/tea-component/lib/table/addons';
import { useTableTopTip } from '@src/routes/global-components/table-top-tip/useTableTopTip';
import { useDialogRef } from '@src/routes/global-components/useDialog';
import { EditDomainSettingDialog } from './EditDomainSettingDialog';
import { FlagType } from '../StatisticsSettingPage';

const KAIQI = 1;
const GUANBI = 0;

const switchOptions = [
  { value: KAIQI, text: t('已开启') },
  { value: GUANBI, text: t('已关闭') },
];

type DomainProps = {
  list: {
    DomainName: string;
    OpenFlag: FlagType['OpenFlag'];
    ClickFlag: FlagType['ClickFlag'];
  }[];
  loading: boolean;
  retry: () => any;
  initFlags: FlagType;
};

const DomainSettingTable = ({
  list,
  loading,
  retry,
  initFlags,
}: DomainProps) => {
  const dialogRef = useDialogRef();
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [searchKeys, setSearchKeys] = useSetState({
    page_index: 1,
    page_size: 10,
    domain: '',
  });

  const { filterList, total } = useMemo(() => {
    const _list = (list ?? []).filter((v) =>
      searchKeys.domain ? v.DomainName?.includes(searchKeys.domain) : true,
    );
    return {
      filterList: _list,
      total: _list.length || 0,
    };
  }, [searchKeys.domain, list]);

  const TableTopTip = useTableTopTip({
    record: filterList,
    loading,
  });

  return (
    <>
      <Card>
        <Card.Header>
          <H3>{t('发信域名级跟踪数据统计设置')}</H3>
        </Card.Header>
        <Card.Body>
          <Table.ActionPanel>
            <Justify
              left={
                <>
                  <Button
                    style={{ verticalAlign: 'baseline' }}
                    type="primary"
                    disabled={!selectedKeys.length}
                    onClick={() => {
                      dialogRef.current.open({
                        domains: selectedKeys,
                        ClickFlag: '0',
                        OpenFlag: '0',
                        type: 'batch',
                        initFlags,
                      });
                    }}
                  >
                    {t('批量设置')}
                  </Button>
                </>
              }
              right={
                <>
                  <SearchBox
                    placeholder={t('输入发信域名')}
                    onChange={(value) => {
                      setSearchKeys({ domain: value, page_index: 1 });
                    }}
                  />
                </>
              }
            />
          </Table.ActionPanel>
          <Table
            compact
            bordered
            records={filterList}
            recordKey="DomainName"
            topTip={TableTopTip}
            columns={[
              { header: t('发信域名'), key: 'DomainName' },
              {
                header: t('打开邮件用户数统计'),
                key: 'openFlag',
                align: 'center',
                render: (row) => (
                  <Text theme={row.OpenFlag === KAIQI ? 'success' : 'weak'}>
                    {
                      _.find(switchOptions, (v) => v.value === row.OpenFlag)
                        ?.text
                    }
                  </Text>
                ),
              },
              {
                header: t('点击链接用户数统计'),
                key: 'clickFlag',
                align: 'center',
                render: (row) => (
                  <Text theme={row.ClickFlag === KAIQI ? 'success' : 'weak'}>
                    {
                      _.find(switchOptions, (v) => v.value === row.ClickFlag)
                        ?.text
                    }
                  </Text>
                ),
              },
              {
                header: t('操作'),
                key: 'operation',
                align: 'center',
                render: (row) => {
                  return (
                    <Button
                      type="link"
                      style={{ marginRight: 0 }}
                      onClick={() => {
                        dialogRef.current.open({
                          domains: [row.DomainName],
                          ClickFlag: row.ClickFlag + '',
                          OpenFlag: row.OpenFlag + '',
                          type: 'single',
                          initFlags,
                        });
                      }}
                    >
                      {t('编辑')}
                    </Button>
                  );
                },
              },
            ]}
            addons={[
              pageable({
                recordCount: total,
                pageIndex: searchKeys.page_index,
                pageSize: searchKeys.page_size,
                onPagingChange: (query) => {
                  setSearchKeys({
                    page_index: query.pageIndex,
                    page_size: query.pageSize,
                  });
                },
              }),
              selectable({
                value: selectedKeys,
                onChange: (keys) => {
                  setSelectedKeys(keys);
                },
                targetColumnKey: 'domain',
              }),
            ]}
          />
        </Card.Body>
      </Card>
      <EditDomainSettingDialog
        dialogRef={dialogRef}
        onSuccess={() => {
          setSelectedKeys([]);
          retry();
        }}
      />
    </>
  );
};
export default DomainSettingTable;
