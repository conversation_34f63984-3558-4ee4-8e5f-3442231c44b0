import React, { useEffect, useState } from 'react';
import {
  Button,
  Form,
  Justify,
  Modal,
  Segment,
  Select,
  message,
} from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import style from './style.module.less';
import { RangePicker } from '@tea/component/datepicker/RangePicker';
import { fetchDomainList } from '@src/routes/domain/actions';
import { TO_DOMAIN_LIST } from '@src/constants/defaultConstant';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/formFn';
import _ from 'lodash';
import moment, { Moment } from 'moment';
import { downloadDeliveryStatistic } from '../../actions';
import useFetchTemplates from '@src/routes/global-components/useFetchTemplates';
import { FormApi } from 'final-form';
import { TimeRangeType } from '../../const';

interface Props {
  initialValues?: any;
  onSubmit: (values: any) => void;
  showDownload?: boolean;
  customButtonRender?: (form: FormApi<any, Partial<any>>) => React.ReactNode;
}

const timePickerOptions = [
  { text: t('按日'), value: TimeRangeType.day },
  { text: t('按月'), value: TimeRangeType.month },
];
const StatsSearchQuery: React.FC<Props> = (props) => {
  const {
    onSubmit,
    initialValues = {},
    showDownload,
    customButtonRender,
  } = props;

  const [domainOptions, setDomainOptions] = useState([
    { value: t('加载中...'), disabled: true },
  ]);
  const { templates } = useFetchTemplates();

  const [downloading, setDownloading] = useState(false);
  const [visible, setVisible] = useState<boolean>(false);

  // 处理点击下载
  const onClickDownload = ({ downloadTimeRange }) => {
    if (downloadTimeRange.length !== 2)
      return message.error({ content: t('请选择时间范围') });
    const [BeginDate, EndDate] = downloadTimeRange?.map((v) =>
      moment(v).format('YYYY-MM-DD'),
    );
    const data = {
      IsMonth: Number(isMonth.input?.value),
      BeginDate,
      EndDate,
    };
    setVisible(false);
    message.success({ content: t('下载中，请耐心等待'), duration: 2000 });
    setDownloading(true);
    downloadDeliveryStatistic(data)
      .then((res: any) => {
        setDownloading(false);
        if (res?.CosPath) {
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = res.CosPath;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      })
      .catch(() => setDownloading(false));
  };

  useEffect(() => {
    fetchDomainList().then((res) => {
      const { EmailIdentities = [] } = res || {};
      let domains = EmailIdentities.filter(
        ({ SendingEnabled }) => SendingEnabled,
      ).map((one: any) => {
        const { IdentityName } = one;
        return { value: IdentityName, text: IdentityName };
      });
      domains = [{ value: '', text: t('所有') }, ...domains];
      console.log('domains:', domains);
      setDomainOptions(domains);
    });
  }, []);

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues,
    validate: () => ({
      domain: undefined,
      timeRange: !timeRange ? t('请选择时间段') : undefined,
    }),
  });

  const domainField = useField('fromDomain', form);
  // const titleField = useField("title", form);
  const timeRange = useField('timeRange', form);
  // const receiveDomainField = useField('receiveDomain', form);
  const shortRecipientDomain = useField('shortRecipientDomain', form);
  const taskTag = useField('taskTag', form);
  const templateId = useField('templateId', form);
  const isMonth = useField('isMonth', form);
  const limitTimeRange = [moment().subtract(6, 'months'), moment()];

  const transformRangeTime = ([start, end]: Moment[], isMonth) => {
    const [limitStart, limitEnd] = limitTimeRange;
    if (isMonth === TimeRangeType.month) {
      const endMoment = moment(end).endOf('month');
      // 确保 start 和 endMoment 在 limitTimeRange 之内
      const adjustedStart = moment(start).isBefore(limitStart)
        ? limitStart
        : moment(start);
      const adjustedEnd = endMoment.isAfter(limitEnd) ? limitEnd : endMoment;

      // 返回调整后的时间范围
      return [adjustedStart, adjustedEnd];
    }
    return [start, end];
  };
  const handleRangeTimeTypeChange = (type) => {
    isMonth.input?.onChange(type);
    isMonth.input.value !== type && timeRange.input?.onChange(undefined);
  };

  return (
    <>
      <form onSubmit={handleSubmit} className={style['stats-search-query']}>
        <Justify
          bottom
          left={
            <Form layout="inline">
              <Form.Item label={t('邮件类型')}>
                <Select
                  {...taskTag.input}
                  className={style['select-field']}
                  type="simulate"
                  appearance="button"
                  options={[
                    { value: '', text: t('所有') },
                    { value: 'task', text: t('批量') },
                    { value: 'trigger', text: t('触发') },
                  ]}
                />
              </Form.Item>
              <Form.Item label={t('发信域名')}>
                <Select
                  className={style['select-field']}
                  {...domainField.input}
                  type="simulate"
                  appearance="button"
                  options={domainOptions}
                  placeholder={t('请选择域名')}
                />
              </Form.Item>
              <Form.Item label={t('收信域名')}>
                <Select
                  className={style['select-field']}
                  type="simulate"
                  appearance="button"
                  {...shortRecipientDomain.input}
                  options={[{ value: '', text: t('所有') }, ...TO_DOMAIN_LIST]}
                  placeholder={t('请选择域名')}
                />
              </Form.Item>
              <Form.Item label={t('模板选择')}>
                <Select
                  {...templateId.input}
                  appearance="button"
                  className={style['select-field']}
                  // type="simulate"
                  searchable
                  clearable
                  matchButtonWidth
                  options={templates}
                />
              </Form.Item>
              <div className={style['range-picker-box']}>
                <Segment
                  {...isMonth.input}
                  options={timePickerOptions}
                  onChange={handleRangeTimeTypeChange}
                />
                <Form.Item label={t('日期范围')}>
                  <RangePicker
                    {...timeRange.input}
                    key={isMonth.input.value}
                    calendarType={
                      isMonth.input.value === TimeRangeType.day
                        ? 'date'
                        : 'month'
                    }
                    onChange={(dates) => {
                      const transformDates = transformRangeTime(
                        dates,
                        isMonth.input.value,
                      );
                      timeRange.input.onChange(transformDates);
                    }}
                    range={limitTimeRange}
                  />
                </Form.Item>
              </div>
            </Form>
          }
          right={
            <div className={style['right-search-btn-field']}>
              <Button
                type="primary"
                htmlType="submit"
                loading={submitting}
                disabled={validating}
              >
                {t('查询')}
              </Button>
              {customButtonRender
                ? customButtonRender(form)
                : showDownload && (
                    <Button
                      htmlType="button"
                      onClick={() => setVisible(true)}
                      loading={downloading}
                    >
                      {t('下载')}
                    </Button>
                  )}
            </div>
          }
        />
      </form>
      <Modal visible={visible} size="s" onClose={() => setVisible(false)}>
        <Modal.Body>
          <FinalForm
            onSubmit={onClickDownload}
            initialValuesEqual={(val, oldVal) => _.isEqual(val, oldVal)}
            initialValues={{ downloadTimeRange: timeRange.input.value }}
          >
            {({ handleSubmit, validating, submitting }) => (
              <form onSubmit={handleSubmit}>
                <Form layout="default">
                  <Field name="downloadTimeRange">
                    {({ input, meta }) => (
                      <Form.Item
                        required
                        showStatusIcon={false}
                        label={t('日期范围')}
                        status={getStatus(meta, validating)}
                        message={
                          getStatus(meta, validating) === 'error' && meta.error
                        }
                      >
                        <RangePicker
                          {...(input as any)}
                          calendarType={
                            isMonth.input.value === TimeRangeType.day
                              ? 'date'
                              : 'month'
                          }
                          range={limitTimeRange}
                          onChange={(dates) => {
                            const transformDates = transformRangeTime(
                              dates,
                              isMonth.input.value,
                            );
                            input.onChange(transformDates);
                          }}
                        />
                      </Form.Item>
                    )}
                  </Field>
                </Form>
                <Form.Action style={{ textAlign: 'center' }}>
                  <Button type="primary" htmlType="submit" loading={submitting}>
                    {t('提交')}
                  </Button>
                </Form.Action>
              </form>
            )}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default StatsSearchQuery;
