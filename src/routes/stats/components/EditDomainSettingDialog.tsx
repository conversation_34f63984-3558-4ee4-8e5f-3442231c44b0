import React, { useState } from 'react';
import { Modal, Button, Form, Radio, RadioGroup } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import _ from 'lodash';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import { getStatus } from '@src/utils/formFn';
import { t } from '@tencent/tea-app/lib/i18n';
import { Trans, Slot } from '@tea/app/i18n';
import { updateStatisticsLink } from '../actions';
import { FlagType } from '../StatisticsSettingPage';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export const EditDomainSettingDialog = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [loading, setLoading] = useState(false);
  const [visible, setShowState, defaultVal] = useDialog<{
    domains: string[];
    OpenFlag: FlagType['OpenFlag'];
    ClickFlag: FlagType['ClickFlag'];
    type: string;
    initFlags: FlagType;
  }>(dialogRef);
  const { domains = [], OpenFlag, ClickFlag, type, initFlags } = defaultVal;

  const handlerSubmit = async (vals: any) => {
    setLoading(true);
    const DomainStatisticsLinks = domains.map((v) => ({
      DomainName: v,
      OpenFlag: +vals.OpenFlag,
      ClickFlag: +vals.ClickFlag,
    }));
    updateStatisticsLink({
      ...initFlags,
      DomainStatisticsLinks,
    })
      .then(() => {
        setShowState(false);
        Modal.alert({
          size: 's',
          message:
            type === 'batch'
              ? t('本次选择的{{count}}个发信域名', { count: domains.length })
              : t('发信域名：{{domain}}', { domain: domains.join(',') }),
          description: (
            <div style={{ padding: 20, color: '#000' }}>
              <Trans>
                <p>
                  <Slot
                    content={
                      +vals.OpenFlag
                        ? t('已开启打开邮件用户数统计能力')
                        : t('已关闭打开邮件用户数统计能力')
                    }
                  />
                </p>
                <p>
                  <Slot
                    content={
                      +vals.ClickFlag
                        ? t('已开启点击链接用户数统计能力')
                        : t('已关闭点击链接用户数统计能力')
                    }
                  />
                </p>
              </Trans>
            </div>
          ),
          buttonText: t('关闭'),
          onClose: () => {
            onSuccess();
          },
        });
      })
      .catch(() => {})
      .then(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={
          type === 'batch'
            ? t('已选择{{count}}个发信域名', { count: domains.length })
            : t('发信域名：{{domain}}', { domain: domains.join(',') })
        }
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={{ domains, OpenFlag, ClickFlag }}
          >
            {({ handleSubmit, validating }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Field name="OpenFlag">
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('打开邮件用户数统计')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <RadioGroup {...input}>
                            <Radio name="1">{t('是')}</Radio>
                            <Radio name="0">{t('否')}</Radio>
                          </RadioGroup>
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="ClickFlag">
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('点击链接用户数统计')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <RadioGroup {...input}>
                            <Radio name="1">{t('是')}</Radio>
                            <Radio name="0">{t('否')}</Radio>
                          </RadioGroup>
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      {type === 'batch' ? t('批量设置') : t('设置')}
                    </Button>
                    <Button
                      type="weak"
                      loading={loading}
                      onClick={(e) => {
                        e?.preventDefault();
                        setShowState(false);
                      }}
                    >
                      {t('取消')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
