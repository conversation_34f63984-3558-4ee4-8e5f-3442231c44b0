import React, { useState } from 'react';
import { t, Trans } from '@tea/app/i18n';
import { Bubble, Button, Icon, Layout, message } from '@tencent/tea-component';
import NewStatusTable from '@src/routes/stats/components/NewStatusTable';
import RegionSelect from '@src/components/RegionSelect/Page';
import _ from 'lodash';
import './style.less';
import { useHistory } from '@tencent/tea-app';
import moment from 'moment';
import { downloadTraceStatistic } from './actions';
import { downloadFile } from '@src/utils/CommonUtils';

const { Body, Content } = Layout;

export function MailTrackingPage() {
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const dataLabelMap = {
    DeliveredCount: t('送达数量'),
    OpenCount: t('打开邮件用户数'),
    ClickCount: t('点击链接用户数'),
    UnsubscribeCount: t('退订数量'),
    ComplaintCount: t('垃圾投诉数量'),
    // BounceCount: t('退信数量'),
    // RequestCount: t('调用数量'),
  };

  const handleDownload = (formValues) => {
    const {
      fromDomain,
      shortRecipientDomain,
      templateId,
      taskTag,
      timeRange,
      isMonth,
    } = formValues;
    if (timeRange?.length !== 2)
      return message.error({ content: t('请选择时间范围') });
    const [BeginDate, EndDate] = timeRange?.map((v) =>
      moment(v).format('YYYY-MM-DD'),
    );
    const data = {
      BeginDate,
      EndDate,
      FromDomain: fromDomain,
      ShortRecipientDomain: shortRecipientDomain,
      TemplateId: templateId,
      TaskTag: taskTag,
      IsMonth: isMonth,
    };

    message.success({ content: t('下载中，请耐心等待'), duration: 2000 });
    setLoading(true);
    downloadTraceStatistic(data)
      .then((res: any) => {
        if (res?.CosPath) {
          downloadFile(res.CosPath);
        }
      })
      .catch((msg) => message.error({ content: msg }))
      .finally(() => setLoading(false));
  };

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header
              title={t('邮件跟踪数据')}
              subtitle={<RegionSelect />}
            />
            <Content.Body>
              <NewStatusTable
                dataLabelMap={dataLabelMap}
                alert="mail"
                customButtonRender={(form) => {
                  return (
                    <Button
                      htmlType="button"
                      loading={loading}
                      onClick={() => handleDownload(form.getState().values)}
                    >
                      {t('下载')}
                    </Button>
                  );
                }}
                headerRenderMap={{
                  OpenCount: (
                    <>
                      <Trans>
                        打开邮件用户数
                        <Bubble
                          content={
                            <>
                              <Trans>
                                仅开启了“打开邮件用户数”统计能力才会被统计，可在
                                <Button
                                  type="link"
                                  style={{ verticalAlign: 'baseline' }}
                                  onClick={() => {
                                    history.push('/ses/statistics-setting');
                                  }}
                                >
                                  统计设置页
                                </Button>
                                查看是否开启。
                              </Trans>
                            </>
                          }
                        >
                          <Icon type="help" />
                        </Bubble>
                      </Trans>
                    </>
                  ),
                  ClickCount: (
                    <>
                      <Trans>
                        点击链接用户数
                        <Bubble
                          content={
                            <>
                              <Trans>
                                仅开启了“点击链接用户数”统计能力才会被统计，可在
                                <Button
                                  type="link"
                                  style={{ verticalAlign: 'baseline' }}
                                  onClick={() => {
                                    history.push('/ses/statistics-setting');
                                  }}
                                >
                                  统计设置页
                                </Button>
                                查看是否开启。
                              </Trans>
                            </>
                          }
                        >
                          <Icon type="help" />
                        </Bubble>
                      </Trans>
                    </>
                  ),
                }}
              />
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
}
