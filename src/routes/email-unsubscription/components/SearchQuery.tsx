import React, { useCallback, useEffect, useState } from 'react';
import { Form } from '@tea/component';
import { SearchBox } from '@tencent/tea-component';

import { useField, FormRenderProps } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import { RangePicker } from '@tea/component/datepicker/RangePicker';
import moment from 'moment';
import { fetchDomainList } from '@src/routes/domain/actions';
import { TO_DOMAIN_LIST } from '@src/constants/defaultConstant';
import { useInterval } from 'react-use';

const DOWNLOAD_STATUS = {
  PROCESSING: 'Processing',
  COMPLETE: 'Completed',
  FAILED: 'Failed',
  EXPIRED: 'Expired',
  QUEUING: 'Queuing',
};

interface Props {
  formRenderProps: FormRenderProps<any>;
  eventId: string;
  needDownload: boolean;
}

const SearchQuery: React.FC<Props> = (props) => {
  const { formRenderProps, eventId, needDownload } = props;
  const { form, handleSubmit, validating, submitting } = formRenderProps;

  const timeRange = useField('timeRange', form);
  const FromDomain = useField('FromDomain', form);
  const EmailAddress = useField('EmailAddress', form);

  return (
    <form onSubmit={handleSubmit}>
      <>
        <Form layout="inline">
          <Form.Item label={t('日期范围')}>
            <RangePicker {...timeRange.input} />
          </Form.Item>
          <Form.Item label={t('收件邮箱地址')} style={{ width: '380px' }}>
            <SearchBox
              name="EmailAddress"
              hideButton={false}
              size="l"
              style={{ width: '300px' }}
              autoComplete="email"
              {...EmailAddress.input}
              placeholder={t('请输入邮箱地址搜索')}
              onSearch={(value) => {
                handleSubmit();
              }}
            />
          </Form.Item>
          <Form.Item label={t('被退订的发信域名')} style={{ width: '400px' }}>
            <SearchBox
              name="FromDomain"
              hideButton={false}
              size="l"
              style={{ width: '300px' }}
              autoComplete="on"
              {...FromDomain.input}
              placeholder={t('请输入发信域名搜索')}
              onSearch={(value) => {
                console.log('value', value);
                handleSubmit();
              }}
            />
          </Form.Item>
        </Form>
      </>
    </form>
  );
};

export default SearchQuery;
