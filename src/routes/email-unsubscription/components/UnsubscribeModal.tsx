import React from 'react';
import { t } from '@tea/app/i18n';
import { Modal, Button, Text } from '@tencent/tea-component';

interface ModalProps {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  loading: boolean;
  dataToDelete: Array<any>;
}
const UnsubscribeModal: React.FC<ModalProps> = ({
  visible,
  onConfirm,
  onCancel,
  loading,
  dataToDelete,
}) => {
  const nums = dataToDelete.length;
  const emailAddress = dataToDelete[0];
  console.log('hahahaha', dataToDelete);
  let content =
    nums === 1
      ? t('确认移除收件邮箱地址 {{email}} ?', { email: emailAddress })
      : '';
  return (
    <Modal visible={visible} onClose={onCancel} caption={content}>
      <Modal.Body>
        <Text theme="label">
          {t('移除后，平台将恢复向该邮箱发送邮件。请谨慎操作！')}
        </Text>
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={onConfirm} loading={loading}>
          {t('移除')}
        </Button>
        <Button type="weak" onClick={onCancel}>
          {t('取消')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default UnsubscribeModal;
