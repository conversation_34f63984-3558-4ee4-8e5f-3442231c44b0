import React from 'react';
import { t } from '@tea/app/i18n';
import { Layout } from '@tencent/tea-component';
import { TabPanel, Tabs } from '@tea/component';
import EmailStatusTable from '@src/routes/email-unsubscription/components/Table';
import './style.less';
import { STATISTIC_PATH } from '@src/constants/defaultConstant';
import _ from 'lodash';
import RegionSelect from '@src/components/RegionSelect/Page';

const { Body, Content } = Layout;

export function EmailUnsubscriptionPage() {
  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('退订')} subtitle={<RegionSelect />} />
            <Content.Body>
              <EmailStatusTable />
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
}
