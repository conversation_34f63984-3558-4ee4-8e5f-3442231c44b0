import React, { useState, useCallback, useEffect } from 'react';
import { t, Trans } from '@tea/app/i18n';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import {
  Modal,
  Text,
  Button,
  message,
  SelectMultiple,
  ExternalLink,
  Icon,
} from '@tencent/tea-component';
import { DomainInfo, getConfigurableIpDomainMapping } from '../../actions';
import style from './style.module.less';
import { useAsyncRetry } from 'react-use';
import { useOptionsTips } from '@src/hooks/useOptionsTips';

interface DialogValue {
  ip: string;
  id: string;
  domains: DomainInfo[];
}

interface DialogProps {
  dialogRef: DialogRef<DialogValue>;
  onConfirm: (value: { id: string; sendDomains: string[] }) => Promise<void>;
}

export const AddDomainDialog: React.FC<DialogProps> = ({
  dialogRef,
  onConfirm,
}) => {
  const [visible, setShowState, value] = useDialog<DialogValue>(dialogRef);
  const [sendDomains, setSendDomains] = useState<string[]>([]);

  const {
    value: domainOptions = [],
    loading: refreshing,
    retry: fetchDomainOptions,
  } = useAsyncRetry(async () => {
    if (!value?.ip) return [];

    try {
      const res = await getConfigurableIpDomainMapping({
        Ip: value.ip,
        QueryType: 0,
      });

      return res.ConfigurableItems.filter((item) => item.Status === 0).map(
        (item) => ({
          value: item.Item,
          text: item.Item,
          disabled: value.domains.some(
            (domain) => domain.SendDomain === item.Item,
          ),
        }),
      );
    } catch (error) {
      message.error({ content: t('获取域名列表失败，请稍后重试') });
      return [];
    }
  }, [value?.ip, value?.domains]);

  useEffect(() => {
    if (!visible) {
      setSendDomains([]);
    }
  }, [visible]);

  const handleConfirm = async () => {
    if (!value || !sendDomains.length) return;
    try {
      await onConfirm({
        id: value.id,
        sendDomains: sendDomains.filter(
          (domain) => !value.domains.some((d) => d.SendDomain === domain),
        ),
      });
      setShowState(false);
      message.success({ content: t('添加发信域名成功') });
    } catch (error) {
      message.error({ content: t('添加发信域名失败，请稍后重试。') });
    }
  };

  const optionsTips = useOptionsTips({
    loading: refreshing,
    dataLength: domainOptions.length,
    emptyText: t('暂无可添加发信域名'),
  });

  return (
    <Modal
      visible={visible}
      caption={t('添加发信域名')}
      onClose={() => setShowState(false)}
      size="m"
    >
      <Modal.Body>
        <div style={{ marginBottom: 16 }}>
          <Text theme="label" style={{ display: 'inline-block', width: 70 }}>
            {t('独立IP')}
          </Text>
          <Text>{value?.ip}</Text>
        </div>
        <div>
          <Text theme="label" style={{ display: 'inline-block', width: 70 }}>
            {t('发信域名')}
          </Text>
          <div style={{ display: 'inline-block', width: 'calc(100% - 70px)' }}>
            <SelectMultiple
              size="l"
              footer={
                <div className={style.sendDomainFooter}>
                  <Trans>
                    找不到发信域名？
                    <Button
                      type="link"
                      onClick={fetchDomainOptions}
                      loading={refreshing}
                    >
                      刷新
                    </Button>
                    或
                    <ExternalLink href={`/ses/domain?type=openNewDialog`}>
                      新建发信域名
                    </ExternalLink>
                  </Trans>
                </div>
              }
              listHeight={343}
              staging={false}
              appearance="button"
              options={domainOptions}
              value={sendDomains}
              onChange={setSendDomains}
              tips={optionsTips}
              placeholder={t('请选择发信域名')}
            />
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          type="primary"
          loading={refreshing}
          onClick={handleConfirm}
          disabled={!sendDomains.length}
        >
          {t('添加')}
        </Button>
        <Button onClick={() => setShowState(false)}>{t('取消')}</Button>
      </Modal.Footer>
    </Modal>
  );
};
