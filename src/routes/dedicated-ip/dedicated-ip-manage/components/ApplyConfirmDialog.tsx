import React, { useState } from 'react';
import {
  <PERSON>dal,
  Button,
  Alert,
  Checkbox,
  ExternalLink,
  Text,
} from '@tencent/tea-component';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import { t, Trans } from '@tencent/tea-app/lib/i18n';
import { getQCMainHost } from '@src/utils/domain';
import { isIntl } from '@src/utils/CommonUtils';
import moment from 'moment';
import { IPItem } from './ApplyIPDialog';

interface DialogProps {
  dialogRef: DialogRef;
  onConfirm: (ipList: IPItem[]) => void;
}

export const ApplyConfirmDialog = (props: DialogProps) => {
  const { dialogRef, onConfirm } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    ipCount: number;
    ipList: IPItem[];
  }>(dialogRef);
  const [agree, setAgree] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!agree) {
      return;
    }
    setLoading(true);
    try {
      await onConfirm(defaultVal.ipList);
      setShowState(false);
    } finally {
      setLoading(false);
    }
  };

  const startTime = moment().format('YYYY-MM-DD');

  return (
    <Modal
      visible={visible}
      size="m"
      caption={t('确认申请独立IP？')}
      onClose={() => setShowState(false)}
    >
      <Modal.Body>
        <Alert type="info">
          {t('独立IP一旦申请就开始计费，当月不支持停用。')}
        </Alert>
        <div style={{ margin: '20px 0' }}>
          <div style={{ marginBottom: 8 }}>
            <Text theme="label">
              <span style={{ display: 'inline-block', width: 100 }}>
                {t('独立IP数量')}
              </span>
            </Text>
            <Text>
              {defaultVal?.ipCount} {t('个')}
            </Text>
          </div>
          <div>
            <Text theme="label">
              <span style={{ display: 'inline-block', width: 100 }}>
                {t('开始计费时间')}
              </span>
            </Text>
            <Text>{startTime}</Text>
          </div>
        </div>
        <Checkbox value={agree} onChange={setAgree}>
          <Trans>
            我已阅读并同意
            <ExternalLink
              href={
                isIntl
                  ? `//${getQCMainHost()}/document/product/1084/39335#c582aca7-2f41-44f2-9a6d-4967382254e1`
                  : `//${getQCMainHost()}/document/product/1288/47930`
              }
            >
              《计费规则》
            </ExternalLink>
          </Trans>
        </Checkbox>
      </Modal.Body>
      <Modal.Footer>
        <Button
          type="primary"
          onClick={handleSubmit}
          loading={loading}
          disabled={!agree}
        >
          {t('确认申请')}
        </Button>
        <Button onClick={() => setShowState(false)}>{t('取消')}</Button>
      </Modal.Footer>
    </Modal>
  );
};
