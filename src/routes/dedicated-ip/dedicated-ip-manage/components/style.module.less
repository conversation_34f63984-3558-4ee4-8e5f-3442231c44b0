.applyIPDialog {
  :global(.app-ses-table__header thead tr) {
    background-color: #f2f4f8;
  }
  :global(.app-ses-table.app-ses-table--verticaltop .app-ses-table__box) td {
    vertical-align: middle;
  }
}

.sendDomainFooter {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
}

.stopServiceDialog {
  .checkbox {
    margin-top: 16px;
  }
  // 隐藏时间轴的最后一个item
  :global(.app-ses-timeline-item:last-child) {
    padding-bottom: 0;
  }
}
