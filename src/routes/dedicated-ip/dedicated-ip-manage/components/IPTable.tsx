import React, { useMemo, useState } from 'react';
import { t } from '@tea/app/i18n';
import {
  Bubble,
  Button,
  Icon,
  Table,
  Text,
  message,
} from '@tencent/tea-component';
import _ from 'lodash';
import {
  DomainStatus,
  StatusType,
  dedicatedIPStatusOpts,
  domainStatusOpts,
} from '../const';
import { useTableTopTip } from '@src/routes/global-components/table-top-tip/useTableTopTip';
import {
  deleteDomainSendIp,
  DomainInfo,
  IpInfo,
  IsDeactivatableType,
} from '../../actions';
import { updateDomainSendIp } from '../../actions';
import { useDialogRef } from '@src/routes/global-components/useDialog';
import { DeleteDomainDialog } from './DeleteDomainDialog';
import { StopDomainDialog } from './StopDomainDialog';
import { StopIPDialog } from './StopIPDialog';
import moment from 'moment';
import { TagDisplayIcon, useLoadTagSDK } from '@src/components/TagComponents';
import EditTagModal from '@src/components/EditTagModal';

const { pageable, expandable, sortable, filterable } = Table.addons;

interface IPTableProps {
  list: IpInfo[];
  total: number;
  loading: boolean;
  expandedKeys: string[];
  onExpandedKeysChange: (keys: string[], { event }: any) => void;
  onPageChange: (pageIndex: number, pageSize: number) => void;
  pageNo: number;
  pageSize: number;
  onAddDomain: (ip: string, domains: DomainInfo[]) => void;
  onStopIP: (ip: string) => Promise<void>;
  onRetry: () => void;
  onApplyIP: () => void;
  onTagListChange: (tagList: { TagKey: string; TagValue: string }[]) => void;
  tagList: { TagKey: string; TagValue: string }[];
}

export const IPTable: React.FC<IPTableProps> = ({
  list,
  total,
  loading,
  expandedKeys,
  onExpandedKeysChange,
  onPageChange,
  pageNo,
  pageSize,
  onAddDomain,
  onStopIP,
  onRetry,
  onApplyIP,
  onTagListChange,
  tagList,
}) => {
  const [sorts, setSorts] = useState([]);

  const { tagFilterable } = useLoadTagSDK();
  const [filterStatus, setFilterStatus] = useState('all');
  const editTagModalRef = useDialogRef();

  const deleteDomainDialogRef = useDialogRef<{
    ip: string;
    domain: string;
    id: string;
    isLastDomain: boolean;
  }>();

  const stopDomainDialogRef = useDialogRef<{
    ip: string;
    domain: string;
    id: string;
    isLastDomain: boolean;
    hasMultipleIPs: boolean;
  }>();

  const stopIPDialogRef = useDialogRef<{
    ip: string;
    id: string;
  }>();

  const handleUpdateDomainStatus = async (id: string, status: DomainStatus) => {
    try {
      await updateDomainSendIp({
        OperType: 0,
        UpdateValues: [
          {
            Name: 'status',
            Value: status,
          },
        ],
        Filters: [{ Name: 'id', Values: [id] }],
      });
      onRetry();
      message.success({
        content:
          status === DomainStatus.Close
            ? t('关闭发信域名成功')
            : t('开启发信域名成功'),
      });
    } catch (error) {
      message.error({
        content:
          status === DomainStatus.Close
            ? t('关闭发信域名失败，请稍后重试。')
            : t('开启发信域名失败，请稍后重试。'),
      });
    }
  };

  const handleDeleteDomain = async (id: string) => {
    try {
      await deleteDomainSendIp({
        Id: id,
      });
      onRetry();
      message.success({ content: t('删除发信域名成功') });
    } catch (error) {
      message.error({ content: t('删除发信域名失败，请稍后重试。') });
    }
  };
  const tableRecords = useMemo(() => {
    return [...list].sort(sortable.comparer(sorts)).filter((ip) => {
      if (filterStatus === 'all') return true;
      return String(ip.Status) === String(filterStatus);
    });
  }, [list, sorts, filterStatus]);

  const topTip = useTableTopTip({
    record: tableRecords,
    loading,
    emptyTip: (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 10,
          gap: 3,
        }}
      >
        <Icon type="info" />
        <Text theme="text">{t('暂无数据')}</Text>
        <Button type="link" onClick={onApplyIP}>
          {t('申请独立IP')}
        </Button>
      </div>
    ),
  });

  const tagFilterableAddon = !!tagFilterable
    ? [
        tagFilterable({
          serviceType: 'ses',
          resourcePrefix: 'independentip',
          column: 'TagList',
          value: tagList.map((item) => ({
            tagKey: item.TagKey,
            tagValue: item.TagValue,
          })),
          onChange: (tags) =>
            onTagListChange(
              tags.map((item) => ({
                TagKey: item.tagKey,
                TagValue: item.tagValue,
              })),
            ),
          max: 3,
          disableUnset: true,
        }),
      ]
    : [];
  return (
    <>
      <Table
        topTip={topTip}
        records={tableRecords}
        recordKey="Ip"
        columns={[
          {
            key: 'Ip',
            header: t('独立IP'),
          },
          {
            key: 'Status',
            header: t('状态'),
            render({ Status }) {
              return (
                <Text
                  theme={`${Status}` === StatusType.Open ? 'success' : 'danger'}
                >
                  {
                    _.find(
                      dedicatedIPStatusOpts,
                      (v) => v.value === `${Status}`,
                    )?.text
                  }
                </Text>
              );
            },
          },
          {
            key: 'BillingStartTime',
            header: t('开始计费时间'),
            render({ BillingStartTime }) {
              return moment(BillingStartTime).format('YYYY-MM-DD');
            },
          },
          {
            key: 'BillingStopTime',
            header: t('停用时间'),
            render({ BillingStopTime }) {
              const isBeforeToday = moment(BillingStopTime).isBefore(moment());
              return isBeforeToday
                ? moment(BillingStopTime).format('YYYY-MM-DD')
                : '-';
            },
          },
          {
            key: 'TagList',
            header: t('标签'),
            width: 200,
            render: ({ TagList }) =>
              TagList && TagList.length > 0 ? (
                <TagDisplayIcon
                  tags={TagList.map((item) => ({
                    tagKey: item.TagKey,
                    tagValue: item.TagValue,
                  }))}
                />
              ) : (
                '-'
              ),
          },
          {
            key: 'action',
            header: t('操作'),
            render({ Ip, Status, IsDeactivatable, Domains, TagList }) {
              const isDeactivatable =
                IsDeactivatable === IsDeactivatableType.No;
              const EditTagButton = () => (
                <Button
                  type="link"
                  onClick={() => {
                    const currentTags = (TagList || []).map((item) => ({
                      tagKey: item.TagKey,
                      tagValue: item.TagValue,
                    }));
                    editTagModalRef.current.open({
                      resourceIds: [Ip],
                      resourceType: 'independentip',
                      currentTags,
                    });
                  }}
                >
                  {t('编辑标签')}
                </Button>
              );

              return (
                <>
                  {`${Status}` === StatusType.Open ? (
                    <>
                      <Button
                        type="link"
                        onClick={() => onAddDomain(Ip, Domains)}
                      >
                        {t('添加发信域名')}
                      </Button>
                      <EditTagButton />
                      <Bubble
                        content={
                          isDeactivatable
                            ? t('当月新增的独立IP不支持停用')
                            : undefined
                        }
                      >
                        <Button
                          disabled={isDeactivatable}
                          type="link"
                          onClick={() => {
                            stopIPDialogRef.current.open({
                              ip: Ip,
                            });
                          }}
                        >
                          {t('停用')}
                        </Button>
                      </Bubble>
                    </>
                  ) : (
                    <EditTagButton />
                  )}
                </>
              );
            },
          },
        ]}
        addons={[
          expandable({
            expandedKeys,
            onExpandedKeysChange,
            shouldRecordExpandable: (record) =>
              record.Domains.length > 0 &&
              `${record.Status}` === StatusType.Open,
            render: (record) => (
              <Table
                verticalTop
                records={record.Domains}
                columns={[
                  {
                    key: 'SendDomain',
                    header: t('发信域名'),
                  },
                  {
                    key: 'ToDomain',
                    header: t('收信域名'),
                  },
                  {
                    key: 'CreateTime',
                    header: t('创建时间'),
                    render({ CreateTime }) {
                      return moment(CreateTime).format('YYYY-MM-DD HH:mm:ss');
                    },
                  },
                  {
                    key: 'UpdateTime',
                    header: t('更新时间'),
                    render({ UpdateTime }) {
                      return moment(UpdateTime).format('YYYY-MM-DD HH:mm:ss');
                    },
                  },
                  {
                    key: 'Status',
                    header: t('状态'),
                    render({ Status }) {
                      return (
                        <Text
                          theme={
                            `${Status}` === DomainStatus.Open
                              ? 'success'
                              : 'danger'
                          }
                        >
                          {
                            _.find(
                              domainStatusOpts,
                              (v) => v.value === `${Status}`,
                            )?.text
                          }
                        </Text>
                      );
                    },
                  },
                  {
                    key: 'action',
                    header: t('操作'),
                    width: '15%',
                    render({ Id, Status, SendDomain }) {
                      const domainCount = list.filter(
                        (ip) =>
                          `${ip.Status}` === StatusType.Open &&
                          ip.Domains.some(
                            (domain) => domain.SendDomain === SendDomain,
                          ),
                      ).length;

                      return (
                        <>
                          <Button
                            type="link"
                            onClick={() => {
                              const isOpen = `${Status}` === StatusType.Open;
                              if (isOpen) {
                                stopDomainDialogRef.current.open({
                                  ip: record.Ip,
                                  id: Id,
                                  isLastDomain: record.Domains.length === 1,
                                  hasMultipleIPs: domainCount > 1,
                                });
                              } else {
                                handleUpdateDomainStatus(Id, DomainStatus.Open);
                              }
                            }}
                          >
                            {`${Status}` === StatusType.Open
                              ? t('关闭')
                              : t('开启')}
                          </Button>
                          <Button
                            type="link"
                            onClick={() =>
                              deleteDomainDialogRef.current.open({
                                ip: record.Ip,
                                domain: SendDomain,
                                id: Id,
                                isLastDomain: record.Domains.length === 1,
                              })
                            }
                          >
                            {t('删除')}
                          </Button>
                        </>
                      );
                    },
                  },
                ]}
              />
            ),
          }),
          pageable({
            recordCount: total,
            pageIndex: pageNo,
            pageSize: pageSize,
            onPagingChange: ({ pageIndex, pageSize }) => {
              onPageChange(pageIndex || 1, pageSize || 10);
            },
          }),
          filterable({
            type: 'single',
            column: 'Status',
            value: filterStatus,
            onChange: (value) => setFilterStatus(value),
            // 增加 "全部" 选项
            all: {
              value: 'all',
              text: t('全部'),
            },
            // 选项列表
            options: [
              { value: StatusType.Open, text: t('使用中') },
              { value: StatusType.Close, text: t('已停用') },
            ],
          }),
          sortable({
            columns: [
              {
                key: 'BillingStartTime',
                prefer: 'desc',
                sorter(first, second) {
                  console.log(first, second);
                  return moment(first.BillingStartTime).diff(
                    moment(second.BillingStartTime),
                  );
                },
              },
            ],
            value: sorts,
            onChange: (sorts) => {
              setSorts(sorts);
            },
          }),
          ...tagFilterableAddon,
        ]}
      />
      <DeleteDomainDialog
        dialogRef={deleteDomainDialogRef}
        onConfirm={(params) => handleDeleteDomain(params.id)}
      />
      <StopDomainDialog
        dialogRef={stopDomainDialogRef}
        onConfirm={(params) =>
          handleUpdateDomainStatus(params.id, DomainStatus.Close)
        }
      />
      <StopIPDialog
        dialogRef={stopIPDialogRef}
        onConfirm={(params) => onStopIP(params.ip)}
      />
      <EditTagModal dialogRef={editTagModalRef} onConfirm={onRetry} />
    </>
  );
};
