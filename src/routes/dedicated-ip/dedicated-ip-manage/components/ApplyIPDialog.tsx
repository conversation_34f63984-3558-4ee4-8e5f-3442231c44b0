import React, { useEffect, useState } from 'react';
import {
  Modal,
  Button,
  Alert,
  Table,
  List,
  ExternalLink,
  message,
  Bubble,
  SelectMultiple,
  Text,
  Icon,
} from '@tencent/tea-component';
import {
  DialogRef,
  useDialog,
  useDialogRef,
} from '@src/routes/global-components/useDialog';
import { t, Trans } from '@tencent/tea-app/lib/i18n';
import { AddIcon } from '@tencent/tea-icons-react';
import { ApplyConfirmDialog } from './ApplyConfirmDialog';
import { allocateDedicatedIp } from '../../actions';
import styles from './style.module.less';
import { useTableTopTip } from '@src/routes/global-components/table-top-tip/useTableTopTip';
import { fetchDomainList } from '@src/routes/domain/actions';
import { getQCMainHost } from '@src/utils/domain';
import { isIntl } from '@src/utils/CommonUtils';
import { useAsyncRetry } from 'react-use';
import { useOptionsTips } from '@src/hooks/useOptionsTips';
import { InfoTip, TagSelectPanel } from '@src/components/TagComponents';

export const serviceLinkHref = `//${getQCMainHost()}/online-service?from=intro_ses`;

interface DialogProps {
  dialogRef: DialogRef;
  onConfirm?: () => void;
}

export interface IPItem {
  id: string;
  ip: string;
  sendDomains: string[];
  receiveDomain: string;
  tagList: { TagKey: string; TagValue: string }[];
}

const MAX_IP_COUNT = 3;

export const ApplyIPDialog = (props: DialogProps) => {
  const { dialogRef, onConfirm } = props;
  const [visible, setShowState] = useDialog(dialogRef);
  const [ipList, setIpList] = useState<IPItem[]>([]);
  const [loading, setLoading] = useState(false);
  const confirmDialogRef = useDialogRef();

  const {
    value: domainOptions = [],
    loading: refreshing,
    retry: fetchDomainOptions,
  } = useAsyncRetry(async () => {
    try {
      const res = await fetchDomainList();
      return res.EmailIdentities.map((item) => ({
        value: item.IdentityName,
        text: item.IdentityName,
      }));
    } catch (error) {
      message.error({ content: t('获取域名列表失败，请稍后重试') });
      return [];
    }
  }, [visible]);

  const optionsTips = useOptionsTips({
    loading: refreshing,
    dataLength: domainOptions.length,
    emptyText: t('暂无可添加发信域名'),
  });

  useEffect(() => {
    if (!visible) {
      setIpList([
        {
          id: Date.now().toString(),
          ip: t('独立IP{{index}}', { index: 1 }),
          sendDomains: [],
          receiveDomain: t('全部'),
          tagList: [],
        },
      ]);
    }
  }, [visible]);

  const handleAddIP = () => {
    if (ipList.length >= MAX_IP_COUNT) {
      message.warning({
        content: t('最多只能申请{{count}}个独立IP', { count: MAX_IP_COUNT }),
      });
      return;
    }

    const newIndex = ipList.length + 1;
    setIpList([
      ...ipList,
      {
        id: Date.now().toString(),
        ip: t('独立IP{{index}}', { index: newIndex }),
        sendDomains: [],
        receiveDomain: t('全部'),
        tagList: [],
      },
    ]);
  };

  const handleDeleteIP = (id: string) => {
    const newList = ipList.filter((item) => item.id !== id);
    // 重新编号
    const updatedList = newList.map((item, index) => ({
      ...item,
      ip: t('独立IP{{index}}', { index: index + 1 }),
    }));
    setIpList(updatedList);
  };

  const handleSubmit = async () => {
    // 检查是否有未选择发信域名的记录
    const hasEmptySendDomain = ipList.some((item) => !item.sendDomains.length);
    if (hasEmptySendDomain) {
      message.error({
        content: t('请为每个独立IP选择发信域名'),
      });
      return;
    }
    setShowState(false);
    confirmDialogRef.current?.open({
      ipCount: ipList.length,
      ipList: ipList,
    });
  };

  const handleConfirm = async (ipList: IPItem[]) => {
    setLoading(true);
    try {
      // 将 ipList 转换为 API 需要的格式
      const ipDomainConfigs = ipList.map((ip) => ({
        Ip: ip.ip,
        Domain: ip.sendDomains,
        TagList: ip.tagList,
      }));

      await allocateDedicatedIp({
        IpDomainConfigs: ipDomainConfigs,
      });

      message.success({ content: t('独立IP已下发并申请成功') });
      onConfirm?.();
      setShowState(false);
    } catch (error) {
      message.error({ content: t('独立IP申请失败') });
    } finally {
      setLoading(false);
    }
  };

  const disabledAddIP = ipList.length >= MAX_IP_COUNT;
  const tableTip = useTableTopTip({
    record: ipList,
  });
  const disableApply = ipList.some((ip) => ip.sendDomains.length === 0);

  return (
    <>
      <Modal
        visible={visible}
        size={1000}
        caption={t('申请独立IP')}
        onClose={() => setShowState(false)}
        className={styles.applyIPDialog}
      >
        <Modal.Body>
          <Alert type="info">
            <List type="bullet">
              <List.Item>
                <Trans>
                  独立IP一旦配置就开始计费，每月3日前会根据上月您账号下配置启用的独立IP个数
                  <Alert.Strong>自动扣除上月费用</Alert.Strong>
                  ，详见
                  <ExternalLink
                    href={
                      isIntl
                        ? `//${getQCMainHost()}/document/product/1084/39335#c582aca7-2f41-44f2-9a6d-4967382254e1`
                        : `//${getQCMainHost()}/document/product/1288/47930`
                    }
                  >
                    计费概述
                  </ExternalLink>
                  。
                </Trans>
              </List.Item>
              <List.Item>
                <Trans>
                  独立IP是研发团队提前预热准备好的资源。为保证资源能被合理有效使用，
                  <Alert.Strong>每月最多支持申请3个独立 IP</Alert.Strong>
                  。了解
                  <ExternalLink
                    href={
                      isIntl
                        ? `//${getQCMainHost()}/document/product/1084/39653`
                        : `//${getQCMainHost()}/document/product/1288/52779`
                    }
                  >
                    申请建议
                  </ExternalLink>
                  。
                </Trans>
              </List.Item>
            </List>
          </Alert>
          <Table
            topTip={tableTip}
            verticalTop
            records={ipList}
            rowClassName={() => styles.ipTableRow}
            columns={[
              {
                key: 'ip',
                header: t('独立IP'),
                width: 100,
                render: ({ ip }) => <span>{ip}</span>,
              },
              {
                key: 'sendDomain',
                header: t('发信域名'),
                width: 200,
                render: ({ id, sendDomains }) => (
                  <SelectMultiple
                    listHeight={343}
                    size="m"
                    footer={
                      <div className={styles.sendDomainFooter}>
                        <Trans>
                          找不到发信域名？
                          <Button
                            type="link"
                            onClick={fetchDomainOptions}
                            loading={refreshing}
                          >
                            刷新
                          </Button>
                          或
                          <ExternalLink href={`/ses/domain?type=openNewDialog`}>
                            新建发信域名
                          </ExternalLink>
                        </Trans>
                      </div>
                    }
                    staging={false}
                    appearance="button"
                    options={domainOptions}
                    value={sendDomains}
                    onChange={(value) => {
                      setIpList(
                        ipList.map((item) =>
                          item.id === id
                            ? { ...item, sendDomains: value }
                            : item,
                        ),
                      );
                    }}
                    tips={optionsTips}
                    placeholder={t('请选择发信域名')}
                  />
                ),
              },
              {
                key: 'receiveDomain',
                header: t('收信域名'),
                align: 'center',
                width: 100,
                render: ({ receiveDomain }) => <span>{receiveDomain}</span>,
              },
              {
                key: 'tagList',
                header: (
                  <>
                    {t('标签')}
                    {InfoTip && <InfoTip />}
                  </>
                ),
                width: 400,
                render: ({ id, tagList }) => (
                  <TagSelectPanel
                    serviceType="ses"
                    resourcePrefix="independentip"
                    value={tagList}
                    max={10}
                    onChange={(_tagList) => {
                      const tagList = _tagList.map((item) => ({
                        TagKey: item.tagKey,
                        TagValue: item.tagValue,
                      }));
                      setIpList(
                        ipList.map((item) =>
                          item.id === id ? { ...item, tagList } : item,
                        ),
                      );
                    }}
                  />
                ),
              },
              {
                key: 'action',
                header: t('操作'),
                width: 100,
                render: ({ id }) => (
                  <Button type="link" onClick={() => handleDeleteIP(id)}>
                    {t('删除')}
                  </Button>
                ),
              },
            ]}
          />

          <Bubble
            placement="right"
            className="app-ses-alert"
            placementOffset={[20, 0]}
            content={
              disabledAddIP ? (
                <Trans>
                  每月最多支持申请{{ MAX_IP_COUNT }}个独立IP，如有疑问请
                  <a href={serviceLinkHref} target="_blank" rel="noreferrer">
                    联系腾讯云在线客服
                  </a>
                </Trans>
              ) : undefined
            }
          >
            <Button
              type="link"
              onClick={handleAddIP}
              style={{ marginTop: 16 }}
              disabled={disabledAddIP}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <AddIcon />
                {t('添加独立IP')}
              </div>
            </Button>
          </Bubble>
        </Modal.Body>
        <Modal.Footer>
          <Bubble
            content={disableApply ? t('请为每个独立IP选择发信域名') : undefined}
          >
            <Button
              type="primary"
              onClick={handleSubmit}
              loading={loading}
              disabled={ipList.length === 0 || disableApply}
            >
              {t('申请')}
            </Button>
          </Bubble>
          <Button onClick={() => setShowState(false)}>{t('取消')}</Button>
        </Modal.Footer>
      </Modal>
      <ApplyConfirmDialog
        dialogRef={confirmDialogRef}
        onConfirm={(ipList) => handleConfirm(ipList)}
      />
    </>
  );
};
