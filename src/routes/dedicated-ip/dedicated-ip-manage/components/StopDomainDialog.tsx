import React, { useState } from 'react';
import { t } from '@tea/app/i18n';
import { Modal, Button, Text } from '@tencent/tea-component';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';

interface DialogParams {
  ip: string;
  domain: string;
  id: string;
  isLastDomain: boolean;
  hasMultipleIPs: boolean;
}

interface DialogProps {
  dialogRef: DialogRef<DialogParams>;
  onConfirm: (params: DialogParams) => Promise<void>;
}

export const StopDomainDialog: React.FC<DialogProps> = ({
  dialogRef,
  onConfirm,
}) => {
  const [visible, setShowState, params] = useDialog<DialogParams>(dialogRef);
  const [loading, setLoading] = useState(false);

  const handleConfirm = async () => {
    if (!params) return;
    setLoading(true);
    try {
      await onConfirm(params);
      setShowState(false);
    } catch (error) {
      // 错误已在父组件处理
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    if (params?.isLastDomain) {
      return (
        <>
          <Text theme="label">
            {t('1.关闭后独立IP{{ip}}将没有任何生效发信域名。', {
              ip: params?.ip,
            })}
          </Text>
          <br />
          <Text theme="label">
            {t(
              '2.若还需要独立IP继续帮助您发信提高到达率，请及时为其添加发信域名。',
            )}
          </Text>
          <br />
          <Text theme="label">
            {t(
              '3.若您不想继续使用独立IP{{ip}}服务，请及时停用独立IP，避免持续产生费用。',
              {
                ip: params?.ip,
              },
            )}
          </Text>
        </>
      );
    }

    if (params?.hasMultipleIPs) {
      return <Text theme="label">{t('关闭后，后续可再次启用。')}</Text>;
    }

    return (
      <Text theme="label">
        {t('关闭后，该发信域名绑定的独立IP将清零，系统将默认采用共享IP发信。')}
      </Text>
    );
  };

  return (
    <Modal
      visible={visible}
      caption={t('确认关闭发信域名{{domain}}吗？', {
        domain: params?.domain,
      })}
      onClose={() => setShowState(false)}
      size="m"
    >
      <Modal.Body>{renderContent()}</Modal.Body>
      <Modal.Footer>
        <Button type="primary" loading={loading} onClick={handleConfirm}>
          {t('确认关闭')}
        </Button>
        <Button onClick={() => setShowState(false)}>{t('取消')}</Button>
      </Modal.Footer>
    </Modal>
  );
};
