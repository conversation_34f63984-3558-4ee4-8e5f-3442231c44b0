import React, { useEffect, useState } from 'react';
import { t, Trans } from '@tea/app/i18n';
import {
  Modal,
  Button,
  Alert,
  Checkbox,
  Stepper,
  Text,
} from '@tencent/tea-component';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import { getDomainSendIp, StopIpStatus } from '../../actions';
import style from './style.module.less';
import { isIntl } from '@src/utils/CommonUtils';
import { useHistory } from '@tea/app/index';
import { useAsyncRetry } from 'react-use';
import { StatusType } from '../const';

interface DialogProps {
  dialogRef: DialogRef;
  status: StopIpStatus;
  usedIps?: string[];
  onConfirm: () => Promise<void>;
}

export const StopServiceDialog: React.FC<DialogProps> = ({
  dialogRef,
  status,
  usedIps = [],
  onConfirm,
}) => {
  const [visible, setShowState] = useDialog(dialogRef);
  const [loading, setLoading] = useState(false);
  const [checked, setChecked] = useState(false);
  const history = useHistory();

  const { value: ips = [], loading: refreshing } = useAsyncRetry(async () => {
    if (!visible) return [];
    try {
      const res = await getDomainSendIp({
        Offset: 0,
        Limit: 100,
        Status: StatusType.Open,
      });

      return res.IpDomainList.map((item) => item.Ip);
    } catch (error) {
      return [];
    }
  }, [visible]);

  const handleConfirm = async () => {
    if (!checked) return;
    setLoading(true);
    try {
      await onConfirm();
      setShowState(false);
    } catch (error) {
      // 错误已在父组件处理
    } finally {
      setLoading(false);
    }
  };

  const BillLink = () => (
    <Button
      type="link"
      style={{ verticalAlign: 'baseline' }}
      onClick={() => {
        history.push(
          isIntl ? '/expense/bill/view?tab=detail' : '/expense/transactions',
        );
      }}
    >
      {isIntl ? t('明细账单') : t('收支明细')}
    </Button>
  );

  const renderContent = () => {
    switch (status) {
      case StopIpStatus.HasUsed:
        const steps = [
          {
            id: 'stop_ip',
            label: t('停用所有使用中的独立IP'),
            detail: (
              <>
                <Text theme="label">
                  {t('1.停用IP增值服务前需先停用使用中的独立IP：{{ips}}', {
                    ips: refreshing ? t('加载中...') : ips.join('，'),
                  })}
                </Text>
                <br />
                <Text theme="label">
                  {t(
                    '2.停用后，独立IP相关的所有发信域名将被删除，平台将会采用共享IP发信，效果可能会受影响。',
                  )}
                </Text>
                <br />
                <Text theme="label">
                  {t(
                    '3.停用当月，仍会按照实际使用天数折算停用当月费用并于次月1日体现在账单中。',
                  )}
                </Text>
              </>
            ),
          },
          {
            id: 'stop_service',
            label: t('关闭独立IP增值服务'),
            detail: (
              <>
                <Text theme="label">
                  {t('1.关闭服务后，再次使用时需要重新开通。')}
                </Text>
                <br />
                <Text theme="label">
                  <Trans>
                    2.关闭服务后，待次月3日前扣完当月独立IP使用费用，开通时预冻结的资金会自动返还账户，到时可点击控制台&gt;费用中心&gt;
                    <BillLink />
                    查看详情。
                  </Trans>
                </Text>
              </>
            ),
          },
        ];

        return (
          <>
            <Alert>
              <Text>
                {t('关闭独立IP增值服务将同时停用所有正在使用中的独立IP。')}
              </Text>
            </Alert>
            <Stepper nowrap type="process-vertical-dot" steps={steps} />
          </>
        );
      case StopIpStatus.Pending:
        return (
          <>
            <Text theme="label">
              {t('1.关闭服务后，再次使用时需要重新开通。')}
            </Text>
            <br />
            <Text theme="label">
              <Trans>
                2.关闭服务后，待次月3日前扣完当月费用，开通时预冻结的资金会自动返还账户，到时可点击控制台&gt;费用中心&gt;
                <BillLink />
                查看详情。
              </Trans>
            </Text>
          </>
        );
      case StopIpStatus.Completed:
      case StopIpStatus.Unused:
      default:
        return (
          <>
            <Text theme="label">
              {t('1.关闭服务后，再次使用时需要重新开通。')}
            </Text>
            <br />
            <Text theme="label">
              <Trans>
                2.关闭服务后，开通时预冻结的资金会自动返还账户，请稍后点击控制台&gt;费用中心&gt;
                <BillLink />
                查看详情。
              </Trans>
            </Text>
          </>
        );
    }
  };

  useEffect(() => {
    setChecked(false);
  }, [visible]);

  return (
    <Modal
      visible={visible}
      destroyOnClose
      caption={t('确认关闭【独立IP增值服务】吗？')}
      onClose={() => setShowState(false)}
      size="m"
      className={style.stopServiceDialog}
    >
      <Modal.Body>
        {renderContent()}
        <div className={style.checkbox}>
          <Checkbox value={checked} onChange={setChecked}>
            <Text>{t('我已阅读并知晓以上信息')}</Text>
          </Checkbox>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          type="primary"
          loading={loading}
          disabled={!checked}
          onClick={handleConfirm}
        >
          {t('确认关闭')}
        </Button>
        <Button onClick={() => setShowState(false)}>{t('取消')}</Button>
      </Modal.Footer>
    </Modal>
  );
};
