import React from 'react';
import { t } from '@tea/app/i18n';
import { Button, Bubble, Justify, SearchBox } from '@tencent/tea-component';

interface OperationBarProps {
  canUnlock: boolean;
  defaultIp: string;
  onApplyIP: () => void;
  onStopService: () => void;
  onSearch: (value: string) => void;
  isUnfrozenFunds: boolean; // 是否未解冻资金,为了兼容二期，二期客户可能未解冻资金，所以需要让他们进入页面，可以去点击解冻
  onUnfrozenFunds: () => void;
}

export const OperationBar: React.FC<OperationBarProps> = ({
  canUnlock,
  defaultIp,
  onApplyIP,
  onStopService,
  onSearch,
  onUnfrozenFunds,
  isUnfrozenFunds,
}) => {
  return (
    <Justify
      style={{ marginBottom: 20 }}
      left={
        <>
          {!isUnfrozenFunds ? (
            <>
              <Button type="primary" onClick={onApplyIP}>
                {t('申请独立IP')}
              </Button>
              <Bubble
                style={{
                  maxWidth: '1000px',
                }}
                content={
                  !canUnlock
                    ? t('当前包含当月新增的独立IP，因此不支持关闭服务。')
                    : ''
                }
              >
                <Button disabled={!canUnlock} onClick={onStopService}>
                  {t('关闭服务')}
                </Button>
              </Bubble>
            </>
          ) : (
            <Bubble
              style={{
                maxWidth: '1000px',
              }}
              content={t(
                '成功停用【独立IP增值服务】且次月1日完成扣费后，才能进行“解冻资金”操作。',
              )}
            >
              <Button type="primary" onClick={onUnfrozenFunds}>
                {t('解冻资金')}
              </Button>
            </Bubble>
          )}
        </>
      }
      right={
        <SearchBox
          style={{ width: 320 }}
          defaultValue={defaultIp}
          loading={false}
          showHelp={false}
          multiline={false}
          simple={false}
          iconPlacement={'end'}
          placeholder={t('请输入独立IP、发信域名搜索')}
          onSearch={onSearch}
          onClear={() => onSearch('')}
        />
      }
    />
  );
};
