import React, { useState } from 'react';
import { t } from '@tea/app/i18n';
import { Modal, Button, Text } from '@tencent/tea-component';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';

interface DialogParams {
  ip: string;
  id: string;
}

interface DialogProps {
  dialogRef: DialogRef<DialogParams>;
  onConfirm: (params: DialogParams) => Promise<void>;
}

export const StopIPDialog: React.FC<DialogProps> = ({
  dialogRef,
  onConfirm,
}) => {
  const [visible, setShowState, params] = useDialog<DialogParams>(dialogRef);
  const [loading, setLoading] = useState(false);

  const handleConfirm = async () => {
    if (!params) return;
    setLoading(true);
    try {
      await onConfirm(params);
      setShowState(false);
    } catch (error) {
      // 错误已在父组件处理
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      caption={t('确认停用独立IP{{ip}}吗？', {
        ip: params?.ip,
      })}
      onClose={() => setShowState(false)}
      size="m"
    >
      <Modal.Body>
        <Text theme="label">
          {t(
            '1.停用后，独立IP{{ip}}相关的所有发信域名配置将被删除，平台将会采用共享IP发信，效果可能会受影响。',
            {
              ip: params?.ip,
            },
          )}
        </Text>
        <br />
        <Text theme="label">
          {t(
            '2.停用当月，仍会按照实际使用天数折算停用当月费用并于次月1日体现在账单中。',
          )}
        </Text>
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" loading={loading} onClick={handleConfirm}>
          {t('确认停用')}
        </Button>
        <Button onClick={() => setShowState(false)}>{t('取消')}</Button>
      </Modal.Footer>
    </Modal>
  );
};
