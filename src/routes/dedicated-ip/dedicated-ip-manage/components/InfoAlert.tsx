import React from 'react';
import { t } from '@tea/app/i18n';
import { Alert, List, Text } from '@tencent/tea-component';

export const InfoAlert: React.FC = () => {
  return (
    <Alert type="info">
      <List type="bullet">
        <List.Item>
          <Alert.Strong>
            {t(
              '状态为"使用中"的独立IP每月3日前会自动扣除上月费用。若您不再继续使用，请及时停用独立IP，避免持续产生费用。',
            )}
          </Alert.Strong>
        </List.Item>
        <List.Item>
          <Text>
            {t(
              '停用所有独立IP且以后不再继续使用请点击"关闭服务"；停用单个独立IP请点击操作列"停用"按钮。',
            )}
          </Text>
        </List.Item>
      </List>
    </Alert>
  );
};
