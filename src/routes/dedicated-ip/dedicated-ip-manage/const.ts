import { t } from '@tea/app/i18n';

export enum StatusType {
  Close = '2',
  Open = '1',
}

export enum DomainStatus {
  Close = '0',
  Open = '1',
}

export const initValues = {
  domain: '',
  ip: '',
  toDomain: '',
  status: '',
  createTime: '',
};

export const domainStatusOpts = [
  { value: DomainStatus.Close, text: t('已关闭') },
  { value: DomainStatus.Open, text: t('生效中') },
];

export const dedicatedIPStatusOpts = [
  { value: StatusType.Close, text: t('已停用') },
  { value: StatusType.Open, text: t('使用中') },
];
