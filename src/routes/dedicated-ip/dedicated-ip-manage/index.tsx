import React, { useCallback, useEffect, useState } from 'react';
import { Layout, Card, message, Modal } from '@tencent/tea-component';
import { useAsyncRetry, useSearchParam, useSetState } from 'react-use';
import { useDialogRef } from '../../global-components/useDialog';
import { useHistory } from '@tencent/tea-app';
import RegionSelect from '@src/components/RegionSelect/Page';
import { t } from '@tea/app/i18n';
import {
  createDomainSendIp,
  DomainInfo,
  getConfigurableIpCount,
  getDomainSendIp,
  StopIpStatus,
  unfreezeFee,
  updateDomainSendIp,
} from '../actions';
import { StatusType, initValues } from './const';
import { InfoAlert } from './components/InfoAlert';
import { OperationBar } from './components/OperationBar';
import { IPTable } from './components/IPTable';
import { ApplyIPDialog } from './components/ApplyIPDialog';
import { AddDomainDialog } from './components/AddDomainDialog';
import { StopServiceDialog } from './components/StopServiceDialog';
import style from '../style.module.less';
import { useUrlParamTrigger } from '@src/hooks/useUrlParamTrigger';
import { AccountStatus } from '../const';

const { Body, Content } = Layout;

interface DedicatedIPManageProps {
  IPData: {
    statusOfIp: AccountStatus;
    stopTime?: string;
  };
  onRetry: () => void;
}

const DedicatedIPManage: React.FC<DedicatedIPManageProps> = ({
  IPData,
  onRetry,
}) => {
  const [params, setParams] = useSetState({
    pageNo: 1,
    pageSize: 10,
    Keyword: '',
    TagList: [] as { TagKey: string; TagValue: string }[],
  });
  const defaultIp = useSearchParam('ip');
  const applyIPDialogRef = useDialogRef();
  const addDomainDialogRef = useDialogRef<{
    ip: string;
    id: string;
    domains: DomainInfo[];
  }>();
  const stopServiceDialogRef = useDialogRef();
  const [expandedKeys, setExpandedKeys] = useState([]);

  const { value, loading, retry } = useAsyncRetry(async () => {
    const { pageNo, pageSize, Keyword, TagList } = params;
    const res = await getDomainSendIp({
      Keyword,
      TagList,
      Offset: (pageNo - 1) * pageSize,
      Limit: pageSize,
    });
    // 如果重新请求后，IP列表发生变化，则需要清空展开的IP
    setExpandedKeys((prev) => {
      return prev.filter((key) => {
        const record = res.IpDomainList.find((item) => item.Ip === key);
        return record?.Domains.length > 0 &&
          `${record.Status}` === StatusType.Open
          ? true
          : false;
      });
    });
    return res;
  }, [params]);

  const list = value?.IpDomainList ?? [];
  const total = value?.Total ?? 0;
  const status = value?.Status ?? StopIpStatus.Completed;
  // const status = 4;
  // 获取使用中的IP列表
  const usedIps = list
    .filter((item) => `${item.Status}` === StatusType.Open)
    .map((item) => item.Ip);

  const handleSearch = useCallback(
    (searchValue: string) => {
      setParams({
        pageNo: 1,
        pageSize: params.pageSize,
        Keyword: searchValue,
      });
    },
    [params.pageSize, setParams],
  );

  const handleStopIP = async (ip: string) => {
    try {
      await updateDomainSendIp({
        OperType: 1,
        Filters: [{ Name: 'ip', Values: [ip] }],
      });
      retry();
      message.success({
        content: t('停用独立IP成功'),
      });
    } catch (error) {
      message.error({ content: t('停用独立IP失败，请稍后重试。') });
    }
  };

  const handleStopService = async () => {
    try {
      await updateDomainSendIp({
        OperType: 2,
      });
      message.success({ content: t('关闭独立IP增值服务成功') });
      onRetry();
    } catch (error) {
      message.error({ content: t('关闭独立IP增值服务失败，请稍后重试') });
      throw error;
    }
  };

  const handleAddDomain = async (ip: string, domains: DomainInfo[]) => {
    // 如果要回显 domains 再给他
    addDomainDialogRef.current.open({ ip, id: ip, domains: [] });
  };

  const handleAddDomainConfirm = async (value: {
    id: string;
    sendDomains: string[];
  }) => {
    try {
      // 要后端支持批量更新
      await createDomainSendIp({
        IpDomainConfigs: [
          {
            Ip: value.id,
            Domain: value.sendDomains,
          },
        ],
      });
      retry();
      message.success({ content: t('添加发信域名成功') });
    } catch (error) {
      message.error({ content: t('添加发信域名失败，请稍后重试。') });
      throw error;
    }
  };
  const handleApplyIPDialogOpen = async () => {
    try {
      // 检查是否可申请独立IP资源
      const res = await getConfigurableIpCount();
      if (res.Count > 0) {
        applyIPDialogRef.current.open();
      } else {
        Modal.error({
          message: t('抱歉，当前独立IP库存不足'),
          description: t(
            '系统已经接收到您的诉求且已开始预热准备，请耐心等待，2-4周后再进行添加',
          ),
          buttonText: t('知道了'),
        });
      }
    } catch (error) {
      message.error({ content: t('申请独立IP失败，请稍后重试') });
    }
  };

  useUrlParamTrigger({
    paramName: ['type', 'ip'],
    onTrigger: (value, name) => {
      if (name === 'type' && value === 'openNewDialog') {
        handleApplyIPDialogOpen();
      }
      if (name === 'ip') {
        handleSearch(value);
      }
    },
  });

  const canUnlock = status !== StopIpStatus.Unavailable && !loading;

  const isUnfrozenFunds = IPData.statusOfIp === AccountStatus.UNFROZEN_FUNDS;
  const handleUnfrozenFunds = async () => {
    try {
      await unfreezeFee();
      message.warning({ content: t('如未解冻成功，请确保已完成次月1日扣费') });
      onRetry();
    } catch (error) {
      message.error({ content: t('解冻失败，请稍后重试') });
    }
  };
  const handleTagListChange = useCallback(
    (tagList: { TagKey: string; TagValue: string }[]) => {
      setParams({
        pageNo: 1,
        TagList: tagList,
      });
    },
    [setParams],
  );

  return (
    <Layout className={style['deficated-ip-manage']}>
      <Body>
        <Content>
          <Content.Header title={t('独立IP')} subtitle={<RegionSelect />} />
          <Content.Body>
            <InfoAlert />
            <OperationBar
              defaultIp={defaultIp === null ? undefined : defaultIp}
              canUnlock={canUnlock}
              onApplyIP={handleApplyIPDialogOpen}
              onStopService={() => stopServiceDialogRef.current.open()}
              onSearch={handleSearch}
              isUnfrozenFunds={isUnfrozenFunds}
              onUnfrozenFunds={handleUnfrozenFunds}
            />
            <Card>
              <IPTable
                onRetry={retry}
                list={list}
                total={total}
                loading={loading}
                expandedKeys={expandedKeys}
                tagList={params.TagList}
                onExpandedKeysChange={(keys, { event }) => {
                  event.stopPropagation();
                  setExpandedKeys(keys);
                }}
                onPageChange={(pageIndex, pageSize) => {
                  setParams({
                    pageNo: pageIndex,
                    pageSize,
                  });
                }}
                pageNo={params.pageNo}
                pageSize={params.pageSize}
                onAddDomain={handleAddDomain}
                onStopIP={handleStopIP}
                onApplyIP={handleApplyIPDialogOpen}
                onTagListChange={handleTagListChange}
              />
            </Card>
          </Content.Body>
        </Content>
      </Body>
      <ApplyIPDialog dialogRef={applyIPDialogRef} onConfirm={retry} />
      <AddDomainDialog
        dialogRef={addDomainDialogRef}
        onConfirm={handleAddDomainConfirm}
      />
      <StopServiceDialog
        dialogRef={stopServiceDialogRef}
        status={status}
        usedIps={usedIps}
        onConfirm={handleStopService}
      />
    </Layout>
  );
};

export default DedicatedIPManage;
