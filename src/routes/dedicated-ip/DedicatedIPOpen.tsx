import React from 'react';
import { t, Trans, Slot } from '@tea/app/i18n';
import {
  Blank,
  Button,
  ExternalLink,
  Layout,
  Alert,
  Text,
  Checkbox,
  message,
  List,
  Modal,
} from '@tencent/tea-component';
import { useHistory } from '@tencent/tea-app';
import { useAsyncRetry, useToggle } from 'react-use';
import { getQCMainHost } from '@src/utils/domain';
import { isIntl } from '@src/utils/CommonUtils';
import { getRealAccountInfo, openAccount } from '../account-open/actions';
import { useDialogRef } from '../global-components/useDialog';
import { OpenConfirmDialog } from './components/OpenConfirmDialog';
import styles from './style.module.less';

const { Body, Content } = Layout;

interface Props {
  onRetry: () => void;
}

const price = isIntl ? t('120美元') : t('900元');

const DedicatedIPOpen = ({ onRetry }: Props) => {
  const history = useHistory();
  const [agree, setAgree] = useToggle(false);
  const [openLoading, setLoading] = useToggle(false);
  const dialogRef = useDialogRef();

  const { value: authType, loading } = useAsyncRetry(async () => {
    const res = await getRealAccountInfo();
    return res?.AuthType;
  }, []);

  const handleSubmit = async () => {
    dialogRef.current.open({
      AuthType: authType,
    });
  };

  const _loading = loading || openLoading;

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('开通服务')} />
            <Content.Body>
              <Blank
                theme="open"
                title={t('独立IP增值服务')}
                operation={
                  <Button
                    type="primary"
                    disabled={!agree}
                    onClick={handleSubmit}
                    loading={_loading}
                  >
                    {t('立即开通')}
                  </Button>
                }
              >
                <Text theme="strong">
                  {t(
                    '不受其他用户发信干扰，可以保证发信域名和IP的信誉度，提高邮件到达率',
                  )}
                </Text>
                <Alert
                  type="info"
                  style={{ margin: '10px 0px 20px' }}
                  className={styles.dedicatedIPOpen}
                >
                  <List type="bullet">
                    <List.Item>
                      <Trans>
                        独立IP是基于发信域名维度配置的，请先完成
                        <Button
                          type="link"
                          style={{ verticalAlign: 'baseline' }}
                          onClick={() => {
                            history.push('/ses/domain');
                          }}
                        >
                          <Text
                            underline
                            theme="text"
                            style={{ fontWeight: 'normal' }}
                          >
                            <Slot content={t('发信域名配置')} />
                          </Text>
                        </Button>
                        。
                      </Trans>
                    </List.Item>
                    <List.Item>
                      {t(
                        '独立IP采用月结后付费方式，每月3日前会自动扣除上月费用。若您不再继续使用，请及时停用独立IP或者关闭服务。独立IP添加当月不允许停用。',
                      )}
                    </List.Item>
                    <List.Item>
                      <Trans>
                        为保证资源能被合理使用，开通需预先冻结费用
                        <Slot content={price} />
                        ，了解
                        <ExternalLink
                          href={`//${getQCMainHost()}/document/product/555/12039?from_cn_redirect=1`}
                        >
                          <Text style={{ fontWeight: 'normal' }}>冻结说明</Text>
                        </ExternalLink>
                        。请保证余额充足，前往
                        <Button
                          type="link"
                          style={{ verticalAlign: 'baseline' }}
                          onClick={() => {
                            history.push(
                              isIntl ? '/expense' : '/expense/recharge',
                            );
                          }}
                        >
                          <Text
                            underline
                            theme="text"
                            style={{ fontWeight: 'normal' }}
                          >
                            <Slot content={t('充值')} />
                          </Text>
                        </Button>
                        。
                      </Trans>
                    </List.Item>
                  </List>
                </Alert>

                <div>
                  <Checkbox value={agree} onChange={(value) => setAgree(value)}>
                    <Trans>
                      我已阅读并同意
                      <ExternalLink
                        href={
                          isIntl
                            ? `//${getQCMainHost()}/document/product/301/9248`
                            : `//${getQCMainHost()}/document/product/301/98546?from_cn_redirect=1`
                        }
                      >
                        《腾讯云邮件推送服务条款》
                      </ExternalLink>
                      <ExternalLink
                        href={
                          isIntl
                            ? `//${getQCMainHost()}/document/product/1084/39335#c582aca7-2f41-44f2-9a6d-4967382254e1`
                            : `//${getQCMainHost()}/document/product/1288/47930`
                        }
                      >
                        《计费规则》
                      </ExternalLink>
                    </Trans>
                  </Checkbox>
                </div>
              </Blank>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      <OpenConfirmDialog dialogRef={dialogRef} onRetry={onRetry} />
    </>
  );
};
export default DedicatedIPOpen;
