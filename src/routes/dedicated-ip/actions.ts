import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';
import { handleRequestError } from '@src/utils/RequestUtils';
import _ from 'lodash';
import { StatusType } from './dedicated-ip-manage/const';

// 0:无法停用服务（有当月新增独立IP）
// 1:已完成所有停用ip的扣费
// 2:已停用ip待完成次月扣费
// 3:未开通过独立IP
// 4:有使用中独立IP
export enum StopIpStatus {
  Unavailable = 0,
  Completed = 1,
  Pending = 2,
  Unused = 3,
  HasUsed = 4,
}

export enum IPStatus {
  Unavailable = 0,
  Completed = 1,
  Pending = 2,
  Unused = 3,
  HasUsed = 4,
}

export enum IsDeactivatableType {
  Yes = 1,
  No = 0,
}

interface GetDomainSendIpParams {
  Keyword?: string;
  Offset?: number;
  Limit?: number;
  Status?: StatusType;
  TagList?: { TagKey: string; TagValue: string }[];
}

// 接口响应类型定义
export interface DomainInfo {
  Id: string;
  SendDomain: string;
  ToDomain: string;
  CreateTime: string;
  UpdateTime: string;
  Status: number;
}

export interface IpInfo {
  Id: string;
  Ip: string;
  Status: StatusType;
  IsDeactivatable: IsDeactivatableType;
  BillingStartTime: string;
  BillingStopTime: string;
  Domains: DomainInfo[];
  TagList: { TagKey: string; TagValue: string }[];
}

export interface DomainSendIpResponse {
  IpDomainList: IpInfo[];
  Total: number;
  Status: StopIpStatus;
}

export function getDomainSendIp(
  params?: GetDomainSendIpParams,
): Promise<DomainSendIpResponse> {
  const cmd = SES_CMD.GET_DOMAIN_SEND_IP;
  const postData = {
    ..._.pickBy(params, (v) => !_.isNil(v) && v !== ''),
  };
  return requestApiV3(cmd, postData, {
    serviceType: 'ses',
  })
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      handleRequestError(e);
    });
}

export function getUserSendIp(params) {
  // debugger
  const cmd = SES_CMD.QUERY_USER_SEND_IP;
  const postData = {
    ..._.pickBy(params, (v) => !_.isNil(v) && v !== ''),
  };
  return requestApiV3(cmd, postData, {
    serviceType: 'ses',
  })
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      // console.log('getDomainSendIpRequestError', e);
      handleRequestError(e);
    });
}

export function unfreezeFee() {
  const cmd = SES_CMD.DO_FEE_MANAGER;
  return requestApiV3(
    cmd,
    {
      OperType: 1,
    },
    {
      serviceType: 'ses',
    },
  )
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      console.log('getDomainSendIpRequestError', e);
      throw handleRequestError(e);
    });
}

export function updateDomainSendIp(params) {
  const cmd = SES_CMD.UPDATE_DOMAIN_SEND_IP;
  const postData = { ...params };
  return requestApiV3(cmd, postData, {
    serviceType: 'ses',
  })
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      console.log('updateDomainSendIpRequestError', e);
      throw handleRequestError(e);
    });
}

export function getConfigurableIpDomainMapping(params: {
  Ip?: string;
  SendDomain?: string;
  QueryType: 0 | 1; // 0: 查询IP，1: 查询域名
}): Promise<{
  ConfigurableItems: {
    Item: string;
    Status: 0 | 1; // 0: 可配置，1: 已配置
  }[];
}> {
  const cmd = SES_CMD.GET_CONFIGURABLE_IP_DOMAIN_MAPPING;
  const postData = { ...params };
  return requestApiV3(cmd, postData, { serviceType: 'ses' })
    .then((res) => res)
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function allocateDedicatedIp(params: {
  IpDomainConfigs: {
    Ip: string;
    Domain: string[];
    TagList?: { TagKey: string; TagValue: string }[];
  }[];
}) {
  const cmd = SES_CMD.ALLOCATE_DEDICATED_IP;
  const postData = { ...params };
  return requestApiV3(cmd, postData, { serviceType: 'ses' })
    .then((res: any) => {
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function getConfigurableIpCount() {
  const cmd = SES_CMD.GET_CONFIGURABLE_IP_COUNT;
  return requestApiV3(cmd, {}, { serviceType: 'ses' }, { tipLoading: false });
}

export function createDomainSendIp(params: {
  IpDomainConfigs: {
    Ip: string;
    Domain: string[];
  }[];
}) {
  const cmd = SES_CMD.CREATE_DOMAIN_SEND_IP;
  const postData = { ...params };
  return requestApiV3(cmd, postData, { serviceType: 'ses' })
    .then((res: any) => {
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function deleteDomainSendIp(params: { Id: string }) {
  const cmd = SES_CMD.DELETE_DOMAIN_SEND_IP;
  const postData = { ...params };
  return requestApiV3(cmd, postData, { serviceType: 'ses' })
    .then((res: any) => {
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}
