import React, { useEffect, useState } from 'react';
import {
  Modal,
  Button,
  message,
  Text,
  ExternalLink,
} from '@tencent/tea-component';
import {
  DialogRef,
  useDialog,
  useDialogRef,
} from '@src/routes/global-components/useDialog';
import { Slot, t, Trans } from '@tencent/tea-app/lib/i18n';
import { useHistory } from '@tencent/tea-app';
import { openAccount } from '@src/routes/account-open/actions';
import { price } from './const';
import { getQCMainHost } from '@src/utils/domain';
import { isIntl } from '@src/utils/CommonUtils';

interface DialogProps {
  dialogRef: DialogRef;
  onRetry: () => void;
}

export const OpenConfirmDialog = (props: DialogProps) => {
  const { dialogRef, onRetry } = props;
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [visible, setShowState, defaultVal] = useDialog<{
    AuthType: number;
  }>(dialogRef);

  const BillLink = () => (
    <Button
      type="link"
      style={{ verticalAlign: 'baseline' }}
      onClick={() => {
        history.push(
          isIntl ? '/expense/bill/view?tab=detail' : '/expense/transactions',
        );
      }}
    >
      {isIntl ? t('明细账单') : t('收支明细')}
    </Button>
  );

  const alertSuccess = () =>
    Modal.success({
      message: t('【独立IP增值服务】开通成功'),
      description: (
        <Trans>
          已成功开通【独立IP增值服务】，并冻结费用{{ price }}
          ，点击控制台&gt;费用中心&gt;
          <BillLink />
          查看详情。
        </Trans>
      ),
      buttonText: t('知道了'),
      onClose: () => {
        onRetry();
      },
    });

  const alertError = () =>
    Modal.error({
      message: t('当前账户余额不足'),
      description: (
        <>
          {t(
            '开通【独立IP增值服务】预计冻结费用{{price}}。当前您的账户余额不足，还请先进行充值。（代金券无法抵扣冻结费用）',
            { price },
          )}
          <ExternalLink
            href={`//${getQCMainHost()}/document/product/555/12039?from_cn_redirect=1`}
          >
            {t('充值冻结指引')}
          </ExternalLink>
        </>
      ),
      buttons: [
        <Button
          key="ok"
          type="primary"
          onClick={() => {
            history.push(isIntl ? '/expense' : '/expense/recharge');
          }}
        >
          {t('前往充值')}
        </Button>,
        <Button key="cancel" onClick={() => {}}>
          {t('取消')}
        </Button>,
      ],
    });
  const handlerSubmit = async () => {
    setLoading(true);
    try {
      await openAccount({
        AuthType: defaultVal?.AuthType,
        ResourceType: 1,
      });
      setLoading(false);
      alertSuccess();
      setShowState(false);
    } catch (error) {
      setLoading(false);
      if (error.code === 'OperationDenied.InsufficientAccountBalance') {
        alertError();
        setShowState(false);
      }
    }
  };

  return (
    <Modal
      visible={visible}
      size="m"
      onClose={() => setShowState(false)}
      caption={t('确认开通【独立IP增值服务】吗？')}
    >
      <Modal.Body>
        <Text theme="label">
          {t(
            '为保证资源被合理使用，要开通【独立IP增值服务】，平台预计冻结费用{{price}}，实际冻结金额以账单为准。',
            { price },
          )}
        </Text>
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={handlerSubmit} loading={loading}>
          {t('确认开通')}
        </Button>
        <Button
          htmlType="button"
          onClick={() => {
            setShowState(false);
          }}
          loading={loading}
        >
          {t('取消')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
