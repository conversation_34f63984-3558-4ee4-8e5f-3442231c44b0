import { isIntl } from '@src/utils/CommonUtils';
import { t } from '@tea/app/i18n';
import { omit, pickBy, forIn, isNil } from 'lodash';

export const price = isIntl ? t('120美元') : t('900元');

export const getFilters = (params) => {
  const filtered = {
    ...omit(
      pickBy(params, (v) => !isNil(v) && v !== ''),
      ['pageNo', 'pageSize'],
    ),
  };
  const arr = [];
  forIn(filtered, (value, key) => {
    arr.push({
      Name: key,
      Values: [value],
    });
  });
  return arr.length ? arr : undefined;
};
export const getFormatIPList = (list, maxLength = 3) => {
  let result = list.slice(0, maxLength);
  const suffix = list.length > maxLength ? '...' : '';
  return result.join('、') + suffix;
};
