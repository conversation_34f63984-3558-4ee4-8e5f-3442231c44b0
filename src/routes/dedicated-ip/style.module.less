.deficated-ip-manage {
  .operation-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    span {
      display: flex !important;
    }
  }
}

.dedicatedIPOpen {
  :global(.app-ses-text-underline) {
    // 实现下划线
    // text-decoration: underline;
    &:after {
      border-bottom-style: solid;
    }
  }
  :global(.app-ses-link-external) {
    &:after {
      display: none;
    }
  }
}
