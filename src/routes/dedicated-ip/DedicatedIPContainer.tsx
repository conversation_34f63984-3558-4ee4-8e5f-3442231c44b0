import React from 'react';
import { useAsyncRetry } from 'react-use';
import DedicatedIPOpen from './DedicatedIPOpen';
import DedicatedIPManage from './dedicated-ip-manage';
import { fetchAccount } from '../ses-index/actions';
import { useHistory } from '@tencent/tea-app';
import { Route, Router } from 'react-router-dom';
import { AccountOpenStatus, AccountStatus } from './const';

const DedicatedIPContainer = () => {
  const history = useHistory();

  const { value, loading, retry } = useAsyncRetry(async () => {
    // StatusOfIp='独立IP资源状态：0未开通，1正常，2隔离，3销毁，4未解冻资金'
    const res = await fetchAccount();
    return { statusOfIp: res?.StatusOfIp, stopTime: res?.StopTime };
  }, []);

  const isOpened = AccountOpenStatus.includes(value?.statusOfIp);

  if (loading) {
    return <div>loading...</div>;
  }

  if (!isOpened) {
    return <DedicatedIPOpen onRetry={retry} />;
  }

  return (
    <Router history={history}>
      <Route
        path="/ses/dedicated-ip"
        exact
        component={() => (
          <DedicatedIPManage IPData={{ ...value }} onRetry={retry} />
        )}
      />
    </Router>
  );
};
export default DedicatedIPContainer;
