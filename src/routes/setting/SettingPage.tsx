import React, { useCallback, useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import {
  Layout,
  Tabs,
  Card,
  message,
  TabPanel,
  Alert,
} from '@tencent/tea-component';
import './style.less';
import SettingForm from '@src/routes/setting/components/SendEmailForm/SettingForm';
import { fetchAccount } from '@src/routes/ses-index/actions';
import { saveSetting } from '@src/routes/setting/actions';
import EmailSendCallbackAddress from './components/EmailSendCallbackAddress';
import RegionSelect from '@src/components/RegionSelect/Page';
import CallbackSetting from './components/CallbackSetting';

const { Body, Content } = Layout;

const AttentionTips = () => {
  const introductionList = [
    t(
      '设置一个回调地址，腾讯云将在产生递送成功、腾讯云拒信、ESP退信、用户打开邮件、点击链接、退订等事件后通知到回调地址。该地址默认为空，不通知。',
    ),
    t('“发信地址级”回调优先级高于“账户级”回调：'),
    t('只能创建1个“账户级”回调；1个发信地址只能创建1个“发信地址级”回调。'),
  ];

  const listDetail = [
    {
      number: 'a) ',
      text: t(
        '既设置“账户级”回调又设置“发信地址级”回调，则“发信地址级”设置仅对该发信地址生效，而“账户级”设置对该发信地址以外的其他发信地址生效；',
      ),
    },
    {
      number: 'b) ',
      text: t('仅设置“账户级”回调则对当前账户下所有发信地址生效；'),
    },
    {
      number: 'c) ',
      text: t('仅设置“发信地址级”回调则对该发信地址生效。'),
    },
  ];
  return (
    <Alert>
      <h4 style={{ marginBottom: 8 }}>{t('注意事项')}</h4>
      {introductionList.map((item, idx) => (
        <p key={idx}>
          {idx + 1}. {item}
          {idx === 1 &&
            listDetail.map((detail) => (
              <p key={detail.number}>
                &nbsp;&nbsp;&nbsp;&nbsp;{detail.number}
                {detail.text}
              </p>
            ))}
        </p>
      ))}
    </Alert>
  );
};

export const SettingPage = () => {
  const [data, setData] = useState({});
  const [dataLoading, setDataLoading] = useState(true);
  const [editable, setEditable] = useState(false);

  const tabs = [
    { id: 'addrSetting', label: t('回调地址配置') },
    { id: 'callbackSetting', label: t('回调基础配置') },
  ];

  const fetchAccountInto = useCallback(() => {
    setDataLoading(true);
    // setDomainList([]);
    fetchAccount()
      .then((res: any) => {
        // console.log('fetchAccount res=', res);
        // DailySendingLimit: 300000
        // FreeQuota: 9993
        // Opened: true
        // RequestId: "********-0a31-4cdd-aee2-cb9b9e4bfe32"
        // Status: 0
        // UserHourlySendingLimit: 10
        // WebhookUrl: ""
        const { WebhookUrl } = res;
        setData({
          Url: WebhookUrl,
        });
      })
      .catch((e) => {
        console.log('catch e', e);
      })
      .finally(() => {
        setDataLoading(false);
      });
  }, []);

  const onCreateSubmit = useCallback((values) => {
    console.log('onCreateSubmit', values);
    const { Destination, ...others } = values;
    const Destination2 = Destination?.split('\n');
    const data = { Destination: Destination2, ...others };
    return saveSetting(data)
      .then(() => {
        // console.log('saveSetting Over', res);
        message.success({ content: t('保存成功') });
        // fetchList();
        return fetchAccountInto();
      })
      .then(() => setEditable(false))
      .catch((e) => {
        throw e;
      });
  }, []);

  useEffect(() => {
    fetchAccountInto();
  }, []);

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('回调地址')} subtitle={<RegionSelect />} />
            <Content.Body>
              <Tabs ceiling animated={false} tabs={tabs}>
                <TabPanel id="addrSetting">
                  <AttentionTips />
                  {/* 内容区域一般使用 Card 组件显示内容 */}
                  <Card>
                    <Card.Header>
                      <h3>{t('账户级回调地址')}</h3>
                    </Card.Header>
                    <Card.Body>
                      <SettingForm
                        className="send-form"
                        onSubmit={onCreateSubmit}
                        loading={dataLoading}
                        values={data}
                        editable={editable}
                      />
                    </Card.Body>
                  </Card>
                  <EmailSendCallbackAddress />
                </TabPanel>
                <TabPanel id="callbackSetting">
                  <CallbackSetting />
                </TabPanel>
              </Tabs>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
};
