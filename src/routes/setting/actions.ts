import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';

export function saveSetting(data: any) {
  console.log('sendEmail start data=', data);
  const cmd = SES_CMD.UPDATE_WEBHOOK;
  const postData = data;
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function listAddressTrackUrl(data: any) {
  const cmd = SES_CMD.LIST_ADDRESS_TRACK_URL;
  return requestApiV3(cmd, data)
    .then((res: any) => res)
    .catch(e => handleRequestError(e));
}

export function deleteAddressTrackUrl(data: any) {
  const cmd = SES_CMD.DELETE_ADDRESS_TRACK_URL;
  return requestApiV3(cmd, data)
    .then((res: any) => res)
    .catch(e => handleRequestError(e));
}

export function createAddressTrackUrl(data: any) {
  const cmd = SES_CMD.CREATE_ADDRESS_TRACK_URL;
  return requestApiV3(cmd, data)
    .then((res: any) => res)
    .catch(e => handleRequestError(e));
}

export function updateAddressTrackUrl(data: any) {
  const cmd = SES_CMD.UPDATE_ADDRESS_TRACK_URL;
  return requestApiV3(cmd, data)
    .then((res: any) => res)
    .catch(e => handleRequestError(e));
}

export function getSelectedEvent(data: any) {
  const cmd = SES_CMD.GET_SELECTED_EVENT;
  return requestApiV3(cmd, data)
    .then((res: any) => res)
    .catch(e => handleRequestError(e));
}

export function updateSelectedEvent(data: any) {
  const cmd = SES_CMD.UPDATE_SELECTED_EVENT;
  return requestApiV3(cmd, data)
    .then((res: any) => res)
    .catch(e => handleRequestError(e));
}
