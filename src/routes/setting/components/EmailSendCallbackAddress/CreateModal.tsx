import React, { useEffect } from 'react';
import { Button, Form, Input, Modal, Select } from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import { getValidateStatus } from '@src/utils/FormUtils';
import { t } from '@tea/app/i18n';
import { SesFormItem } from '@src/components/SesFormItem';

interface NewDomainModalProps {
  values?: any;
  // options: any;
  visible: boolean;
  addressList: Array<any>;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

const INITIAL_VALUES = { address: '', trackUrl: '' };

const NewModal: React.FC<NewDomainModalProps> = (props) => {
  const { visible, onCancel, onSubmit, addressList, values } = props;
  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: INITIAL_VALUES,
    validate: ({ address, trackUrl }: any) => ({
      address: !address ? true : undefined,
      trackUrl:
        !/^((ht|f)tps?):\/\/[\w-]+(\.[\w-]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?$/.test(
          trackUrl,
        )
          ? true
          : undefined,
    }),
  });

  const address = useField('address', form);
  const trackUrl = useField('trackUrl', form);

  useEffect(() => {
    if (values?.Address) {
      // 更新时显示传入值
      form.reset({
        address: values?.Address,
        trackUrl: values?.TrackUrl,
      });
    }
  }, [values]);

  return (
    <Modal
      className="edit-domain-modal"
      visible={visible}
      caption={!values?.Address ? t('新建') : t('修改')}
      onClose={onCancel}
      size={600}
    >
      <form onSubmit={handleSubmit}>
        <Modal.Body>
          <Form
          // layout="vertical"
          >
            <Form.Item
              label={t('发信地址')}
              required={true}
              status={getValidateStatus(address.meta, validating)}
              message={
                getValidateStatus(address.meta, validating) === 'error' &&
                address.meta.error
              }
            >
              <Select
                {...address.input}
                disabled={values?.Address}
                appearance="button"
                size="l"
                options={
                  !values?.Address
                    ? addressList
                    : [{ text: values?.Address, value: values?.Address }]
                }
              />
            </Form.Item>
            <SesFormItem
              label={t('回调地址')}
              required={true}
              suffix={t('同1个发信地址只能创建1个“发信地址级”回调。')}
              status={getValidateStatus(trackUrl.meta, validating)}
              message={
                getValidateStatus(trackUrl.meta, validating) === 'error' &&
                trackUrl.meta.error
              }
            >
              <Input
                {...trackUrl.input}
                size="l"
                placeholder={'https://abc.def.ghi/jk'}
                autoComplete="off"
              />
            </SesFormItem>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            disabled={validating}
          >
            {t('提交')}
          </Button>
          <Button type="weak" htmlType="button" onClick={onCancel}>
            {t('取消')}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default NewModal;
