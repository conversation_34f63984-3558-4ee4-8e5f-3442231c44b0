import React, { useEffect, useState } from 'react';
import { <PERSON>ton, Card, H3, Justify, Modal, SearchBox, Table } from '@tencent/tea-component';
import { t } from '@tencent/tea-app/lib/i18n';
import {
  listAddressTrackUrl,
  deleteAddressTrackUrl,
  createAddressTrackUrl,
  updateAddressTrackUrl,
} from '../../actions';
import NewModal from './CreateModal';
import { fetchAddressList } from '@src/routes/address/actions';

const { pageable, autotip } = Table.addons;

const initialPageData = {
  pageIndex: 1,
  pageSize: 10,
};

export default function CallbackAddress() {
  const [tableList, setTableList] = useState([]);
  const [listTotal, setListTotal] = useState(0);
  // 当前行数据
  const [currentRow, setCurrentRow] = useState<any>({});
  // 所有发信地址
  const [addressList, setAddressList] = useState([]);
  // 新增修改弹框的控制
  const [newModalVisible, setNewModalVisible] = useState(false);
  // 列表loading
  const [listLoading, setListLoading] = useState(false);
  // 搜索值
  const [searchBox, setSearchBox] = useState('');
  // 当前页
  const [pageData, setPageData] = useState(initialPageData);

  const doGet = (address, pageData) => {
    setListLoading(true);
    const params = {
      Offset: (pageData.pageIndex - 1) * pageData.pageSize,
      Limit: pageData.pageSize,
      Address: address,
    };
    listAddressTrackUrl(params)
      .then((res) => {
        const { Data = [], TotalCount = 0 } = res || {};
        setTableList(Data);
        setListTotal(TotalCount);
      })
      .finally(() => {
        setListLoading(false);
      });
  };

  const doAdd = (row) => {
    setNewModalVisible(false);
    setListLoading(true);
    const { address, trackUrl } = row;
    const params = {
      Address: address,
      TrackUrl: trackUrl,
    };
    if (!currentRow?.Address) {
      createAddressTrackUrl(params).then(() => {
        doGet(searchBox, pageData);
      });
    } else {
      updateAddressTrackUrl(params).then(() => {
        doGet(searchBox, pageData);
      });
    }
  };
  const doDelete = (row) => {
    Modal.confirm({
      message: t('确认删除？'),
      okText: t('删除'),
      cancelText: t('取消'),
    }).then((result) => {
      if (result) {
        setListLoading(true);
        const { Address } = row;
        deleteAddressTrackUrl({ Address }).then(() => {
          // 当页只有1条，删除后应跳到前一页
          if (tableList.length === 1 && pageData.pageIndex > 1) {
            setPageData({ ...pageData, pageIndex: pageData.pageIndex - 1 });
            doGet(searchBox, { ...pageData, pageIndex: pageData.pageIndex - 1 });
          } else {
            doGet(searchBox, pageData);
          }
        });
      }
    });
  };

  const onPagingChange = (page) => {
    setPageData(page);
    doGet(searchBox, page);
  };

  const doSearchChange = (value) => {
    setSearchBox(value);
    setPageData(initialPageData);
    doGet(value, initialPageData);
  };

  const doUpdate = (value) => {
    console.log(value);
    setCurrentRow(value);
    setNewModalVisible(true);
  };

  useEffect(() => {
    doGet(searchBox, pageData);
    fetchAddressList().then((res) => {
      const { EmailSenders = [] } = res || {};
      const list = EmailSenders.map(item => ({
        text: item.EmailAddress,
        value: item.EmailAddress,
      }));
      setAddressList(list);
    });
  }, []);

  return (
    <>
      <Card>
        <Card.Header>
          <Justify
            style={{ paddingRight: 20 }}
            left={
              <>
                <H3 style={{ display: 'inline-block' }}>{t('发送地址级回调地址')}</H3>
                <Button
                  style={{ verticalAlign: 'baseline' }}
                  type="primary"
                  onClick={() => {
                    setCurrentRow({});
                    setNewModalVisible(true);
                  }}
                >
                  {t('新建')}
                </Button>
              </>
            }
            right={
              <>
                <SearchBox onChange={doSearchChange} />
              </>
            }
          />
        </Card.Header>
        <Card.Body>
          <Table
            records={tableList}
            recordKey="Address"
            columns={[
              {
                key: 'Address',
                header: t('发信地址'),
              },
              {
                key: 'TrackUrl',
                header: t('回调地址'),
              },
              {
                key: 'action',
                header: t('操作'),
                width: 120,
                render: item => (
                  <>
                    <Button
                      type="link"
                      onClick={() => {
                        doUpdate(item);
                      }}
                    >
                      {t('修改')}
                    </Button>
                    <Button
                      type="link"
                      onClick={() => {
                        doDelete(item);
                      }}
                    >
                      {t('删除')}
                    </Button>
                  </>
                ),
              },
            ]}
            addons={[
              pageable({
                recordCount: listTotal,
                pageIndex: pageData.pageIndex,
                pageSize: pageData.pageSize,
                pageSizeOptions: [10, 20, 30, 50, 100],
                // pageSizeOptions: [1, 2, 3, 5, 10, 20],
                onPagingChange: (query) => {
                  onPagingChange(query);
                },
              }),
              autotip({
                isLoading: listLoading,
              }),
            ]}
          />
        </Card.Body>
      </Card>
      {newModalVisible && (
        <NewModal
          visible={newModalVisible}
          values={currentRow}
          addressList={addressList}
          onCancel={() => {
            setNewModalVisible(false);
          }}
          onSubmit={doAdd}
        />
      )}
    </>
  );
}
