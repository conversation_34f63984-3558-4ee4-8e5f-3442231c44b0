import React, { useState, useEffect } from 'react';
import { t } from '@tencent/tea-app/lib/i18n';
import {
  Card,
  ExternalLink,
  Button,
  Checkbox,
  Text,
  Icon,
  StatusTip,
  Row,
  Col,
  Justify,
  H3,
} from '@tencent/tea-component';
import { useAsyncRetry } from 'react-use';
import { getSelectedEvent, updateSelectedEvent } from '../actions';
import _ from 'lodash';
import { isMainLand } from '@src/utils/CommonUtils';

const callbackEvent = [
  { value: 1, text: t('邮件延迟发送回调'), label: 'deferred' },
  { value: 2, text: t('邮件成功发送回调'), label: 'delivered' },
  { value: 4, text: t('邮件丢弃回调'), label: 'dropped' },
  { value: 8, text: t('邮件打开回调'), label: 'open' },
  { value: 16, text: t('邮件点击回调'), label: 'click' },
  { value: 32, text: t('收件服务商拒信回调'), label: 'bounce' },
  { value: 64, text: t('收件人举报回调'), label: 'spamreport' },
  { value: 128, text: t('收件人退订回调'), label: 'unsubscribe' },
];

function getEventFromSum(sum: number) {
  return sum
    ? sum
        .toString(2)
        .split('')
        .reverse()
        .map((v, index) => (v === '1' ? 2 ** index : 0))
        .filter((v) => v)
    : [];
}

const EventFilterSetting = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState([]);
  const [setLoading, setSetLoading] = useState(false);

  const { value, loading, retry } = useAsyncRetry(async () => {
    const res = await getSelectedEvent({});
    return res.SelectedEvent;
  }, []);

  useEffect(() => {
    const list = getEventFromSum(value);
    setEditValue(_.map(list, String));
  }, [value]);

  async function doEdit(value: number[]) {
    setSetLoading(true);
    const params = {
      SelectedEvent: _.sum(_.map(value, Number)),
    };
    await updateSelectedEvent(params);
    setIsEditing(false);
    setSetLoading(false);
    retry();
  }

  return (
    <Card>
      <Card.Header>
        <Justify
          style={{ paddingRight: 20 }}
          left={
            <H3 style={{ display: 'inline-block' }}>{t('回调事件过滤')}</H3>
          }
          right={
            <>
              <ExternalLink
                href={
                  isMainLand
                    ? `https://${window.QCLOUD_ROOT_HOST}/document/product/1288/52368`
                    : 'https://www.tencentcloud.com/document/product/1084/39492'
                }
              >
                {t('回调指南')}
              </ExternalLink>
              {!isEditing && (
                <Button
                  type="link"
                  style={{ verticalAlign: 'baseline' }}
                  onClick={() => setIsEditing(true)}
                >
                  {t('编辑')}
                </Button>
              )}
            </>
          }
        />
      </Card.Header>
      <Card.Body>
        <div style={{ padding: '16px' }}>
          {loading && <StatusTip status="loading" />}
          {!loading &&
            (!isEditing ? (
              <Row>
                {callbackEvent.map((v) => (
                  <Col span={6} key={v.value}>
                    <Text theme={v.value & value ? 'success' : 'weak'}>
                      {v.text}
                    </Text>
                    {v.value & value ? (
                      <Icon type="success" style={{ marginLeft: '5px' }} />
                    ) : null}
                  </Col>
                ))}
              </Row>
            ) : (
              <section>
                <Checkbox.Group
                  value={editValue}
                  onChange={(value) => {
                    setEditValue(value);
                  }}
                >
                  <Row>
                    {callbackEvent.map((item) => (
                      <Col span={6} key={item.value}>
                        <Checkbox name={`${item.value}`}>{item.text}</Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
                <div style={{ marginTop: '25px' }}>
                  <Button
                    type="primary"
                    onClick={() => doEdit(editValue)}
                    style={{ marginRight: '8px' }}
                    loading={setLoading}
                  >
                    {t('设置')}
                  </Button>
                  <Button
                    onClick={() => setIsEditing(false)}
                    loading={setLoading}
                  >
                    {t('取消')}
                  </Button>
                </div>
              </section>
            ))}
        </div>
      </Card.Body>
    </Card>
  );
};
export default EventFilterSetting;
