import React, { useEffect, useState } from 'react';
import { Button, Form, Icon, Input } from '@tea/component';
import { useField, useForm } from 'react-final-form-hooks';
import { getValidateStatus } from '@src/utils/FormUtils';
import { t } from '@tea/app/i18n';
import './style.less';
import { SesFormItem } from '@src/components/SesFormItem';

interface Props {
  values?: any;
  loading?: boolean;
  onSubmit: (values: any) => void;
  className?: string;
  editable?: boolean;
}

function validateUrl(Url: string) {
  if (!Url) {
    return undefined;
  }
  if (!/^((ht|f)tps?):\/\/[\w-]+(\.[\w-]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?$/.test(Url)) {
    return true;
  }
  return undefined;
}

const SettingForm: React.FC<Props> = (props) => {
  const {
    onSubmit,
    className,
    values: propsValues = { Url: '' },
    loading = false,
    editable: defaultEditable = true,
  } = props;

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: propsValues,
    validate: ({ Url }) => ({
      Url: validateUrl(Url),
    }),
  });

  const urlField = useField('Url', form);

  const [editable, setEditable] = useState(defaultEditable);

  useEffect(() => {
    console.log('reset', propsValues);
    form.reset(propsValues);
    form.resetFieldState(urlField.input.name);
  }, [propsValues, editable]);

  const handleFormSubmit = (values) => {
    handleSubmit(values)
      .then((res) => {
        console.log('handleFormSubmit res=', res);
        setEditable(false);
      })
      .catch((e) => {
        console.log('handleFormSubmit catch e=', e);
      });
  };

  return (
    <form onSubmit={handleFormSubmit} className={`send-email-form ${className}`}>
      <Form
      // layout="fixed"
      >
        <SesFormItem
          label={t('回调地址')}
          required={true}
          // label="昵称"
          // suffix={<div className='long-suffix'>{t('设置一个回调地址，腾讯云将在产生递送成功、腾讯云拒信、ESP退信、用户打开邮件、点击链接、退订等事件后通知到回调地址。该地址默认为空，不通知。')}</div>}
          status={getValidateStatus(urlField.meta, validating)}
          message={getValidateStatus(urlField.meta, validating) === 'error' && urlField.meta.error}
        >
          {editable ? (
            <Input {...urlField.input} placeholder={t('https://abc.def.ghi/jk')} />
          ) : (
            <Form.Text>
              <div className="editable-field">
                <div className="editable-field__text">{urlField.input.value || t('无')}</div>
                <Icon
                  className="editable-field__icon"
                  type="pencil"
                  link={true}
                  onClick={() => {
                    setEditable(true);
                  }}
                />
              </div>
            </Form.Text>
          )}
        </SesFormItem>
      </Form>
      {editable && (
        <Form.Action>
          <Button
            className="send-submit-btn"
            type="primary"
            htmlType="submit"
            loading={submitting}
            disabled={validating || loading}
          >
            {t('保存')}
          </Button>
          <Button
            className="send-submit-btn"
            onClick={() => {
              form.reset(propsValues);
              form.resetFieldState(urlField.input.name);
              setEditable(false);
            }}
          >
            {t('取消')}
          </Button>
        </Form.Action>
      )}
    </form>
  );
};

export default SettingForm;
