import React from 'react';
import { Upload, Text, Button } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';

export const TemplateFileUpload = (props) => {
  console.log('TemplateFileUpload props=', props);
  const { onChange, value } = props;
  const { file } = value || {};
  // const file = value; // {name/size}
  const setValue = (value) => {
    onChange && onChange(value);
  };
  // const [file, setFile] = useState(value);
  // const [status, setStatus] = useState(null);
  // const [percent, setPercent] = useState(null);


  // function handleStart(file, { xhr }) {
  //   setFile(file);
  //   onChange && onChange(file);
  //   setStatus("validating");
  //   xhrRef.current = xhr;
  //   xhr.abort();
  // }
  //
  // function handleProgress({ percent }) {
  //   setPercent(percent);
  // }
  //
  // function handleSuccess(result) {
  //   console.log("TemplateFileUpload handleSuccess", result);
  //   setStatus("success");
  // }

  // function handleError() {
  //   setStatus("error");
  //   Modal.alert({
  //     type: "error",
  //     message: "错误",
  //     description: "请求服务器失败",
  //   });
  // }

  const toBase64 = file => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });

  async function beforeUpload(file, fileList, isAccepted) {
    if (!isAccepted) {
      // setStatus("error");
    }
    const base64 = await toBase64(file);
    setValue({ file, base64 });
    return false; // 让它不要走网络上传
    // return isAccepted;
  }

  // function handleAbort() {
  //   if (xhrRef.current) {
  //     xhrRef.current.abort();
  //   }
  //   setFile(null);
  //   setStatus(null);
  //   setPercent(null);
  // }

  return (
    // <Form.Control status={status} message="请上传 zip 格式文件，大小 1MB 以内">
    <Upload
      // action="https://run.mocky.io/v3/68ed7204-0487-4135-857d-0e4366b2cfad"
      action={() => false}
      accept="text/plain,text/html"
      maxSize={1024 * 1024}
      // onStart={handleStart}
      // onProgress={handleProgress}
      // onSuccess={handleSuccess}
      // onError={handleError}
      beforeUpload={beforeUpload}
      {...props}
    >
      {({ open, isDragging }) => (
        <Upload.Dragger
          filename={file && file.name}
          // percent={percent}
          description={
            file && (
              <>
                <p>{t('文件大小')}: {Math.floor(file.size / 1024)}K</p>
                {/* <p>上传日期：-</p>*/}
              </>
            )
          }
          button={
            status === 'validating' ? (
              <Button type="link"
                // onClick={handleAbort}
              >
                {t('取消上传')}
              </Button>
            ) : (
              <>
                <Button type="link" onClick={open}>
                  {t('重新上传')}
                </Button>
                {/* <Button*/}
                {/*  type="link"*/}
                {/*  style={{ marginLeft: 8 }}*/}
                {/*  onClick={() => setFile(null)}*/}
                {/* >*/}
                {/*  {t('删除')}*/}
                {/* </Button>*/}
              </>
            )
          }
        >
          {isDragging ? (
            t('释放鼠标')
          ) : (
            <>
              <a onClick={open}>{t('点击上传')}</a>
              <Text theme="weak">/{t('拖拽到此区域')}</Text>
            </>
          )}
        </Upload.Dragger>
      )}
    </Upload>
    // </Form.Control>
  );
};
