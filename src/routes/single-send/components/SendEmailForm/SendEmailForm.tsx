import React, { useCallback, useEffect, useState } from 'react';
import { Button, Form, Input, Select } from '@tencent/tea-component';
import { useForm, useField } from 'react-final-form-hooks';
import { getValidateStatus, validateDestination } from '@src/utils/FormUtils';
import { t } from '@tea/app/i18n';
import './style.less';
import { fetchAddressList } from '@src/routes/address/actions';
import { SesFormItem } from '@src/components/SesFormItem';
import { unsubscribeList } from '@src/constants/options';
import useFetchTemplates from '@src/routes/global-components/useFetchTemplates';
import { isPlainObject } from '@src/utils/CommonUtils';

interface Props {
  values?: any;
  // options: any;
  onSubmit: (values: any) => void;
  className?: string;
  // onCancel: () => void;
}

const SendEmailForm: React.FC<Props> = (props) => {
  const { onSubmit, className } = props;

  const { templates } = useFetchTemplates();
  const [senderList, setSenderList] = useState([]);
  // const [senderLoading, setSenderLoading] = useState(true);

  const fetchSenderList = useCallback(() => {
    // setSenderLoading(true);
    // setDomainList([]);
    fetchAddressList()
      .then((res: any) => {
        console.log('fetchAddressList res=', res);
        const { EmailSenders = [] } = res || {};
        const newList = EmailSenders.map((one: any) => {
          const { EmailAddress, EmailSenderName } = one;
          return {
            value: `${EmailSenderName} <${EmailAddress}>`,
            text: `${EmailAddress} (${EmailSenderName})`,
          };
        });

        if (newList.length === 0) {
          newList.push({
            value: 'none',
            text: t('尚未创建发件邮箱'),
            disabled: true,
          });
        }

        setSenderList(newList);
      })
      .finally(() => {
        // setSenderLoading(false);
      });
  }, []);

  useEffect(() => {
    fetchSenderList();
  }, []);

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: { name: '', Unsubscribe: '0' },
    validate: ({
      TemplateID,
      Subject,
      FromEmailAddress,
      Destination,
      TemplateData,
    }: any) => {
      let TemplateDataError = undefined;
      if (TemplateData) {
        try {
          const obj = JSON.parse(TemplateData);
          if (!isPlainObject(obj)) {
            TemplateDataError = t('JSON格式解析错误');
          }
        } catch (e) {
          TemplateDataError = t('JSON格式解析错误：') + e;
        }
      }
      return {
        TemplateID: !TemplateID ? true : undefined,
        Subject: !Subject ? true : undefined,
        FromEmailAddress: !FromEmailAddress ? true : undefined,
        Destination: validateDestination(Destination),
        TemplateData: TemplateDataError,
      };
    },
  });

  // const templateOptions = useMemo(() => {
  //   const options = [
  //     { value: "strawberry", text: "草莓", tooltip: "甜甜甜" },
  //     { value: "apple", text: "苹果", tooltip: "每日一苹果，医生远离我" },
  //     { value: "orange", text: "橙子", tooltip: "丰富 VC 含量" },
  //     { value: "durian", text: "榴莲", disabled: true, tooltip: "榴莲已售罄" },
  //   ];
  //   return options;
  // }, []);

  const templateIdField = useField('TemplateID', form);
  const titleField = useField('Subject', form);
  const senderField = useField('FromEmailAddress', form);
  const destinationField = useField('Destination', form);
  const paramField = useField('TemplateData', form);
  const UnsubscribeField = useField('Unsubscribe', form);
  // const fileField = useField("file", form);

  return (
    <form onSubmit={handleSubmit} className={`send-email-form ${className}`}>
      <Form layout="fixed" fixedLabelWidth="8em">
        <Form.Item
          label={t('模板选择')}
          required={true}
          // label="昵称"
          status={getValidateStatus(templateIdField.meta, validating)}
          message={
            getValidateStatus(templateIdField.meta, validating) === 'error' &&
            templateIdField.meta.error
          }
        >
          <Select
            {...templateIdField.input}
            appearance="button"
            options={templates}
            searchable
            matchButtonWidth
            className="select-field"
            placeholder={t('请选择...')}
          />
        </Form.Item>
        <Form.Item
          label={t('邮件主题')}
          required={true}
          // label="昵称"
          status={getValidateStatus(titleField.meta, validating)}
          message={
            getValidateStatus(titleField.meta, validating) === 'error' &&
            titleField.meta.error
          }
        >
          <Input {...titleField.input} placeholder={t('请输入主题')} />
        </Form.Item>
        <Form.Item
          label={t('发件人选择')}
          required={true}
          // label="昵称"
          status={getValidateStatus(senderField.meta, validating)}
          message={
            getValidateStatus(senderField.meta, validating) === 'error' &&
            senderField.meta.error
          }
        >
          <Select
            {...senderField.input}
            type="native"
            // type="simulate"
            // appearance="button"
            // size="full"
            options={senderList}
            // value={favorite}
            // onChange={value => setFavorite(value)}
            placeholder={t('请选择...')}
          />
        </Form.Item>
        <SesFormItem
          label={t('收件人')}
          required={true}
          suffix={t('此处收件地址填写上限为20个，不同收件地址请换行')}
          status={getValidateStatus(destinationField.meta, validating)}
          message={
            getValidateStatus(destinationField.meta, validating) === 'error' &&
            destinationField.meta.error
          }
        >
          <Input
            multiline
            {...destinationField.input}
            autoComplete="off"
            placeholder={'<EMAIL>\<EMAIL>\n...'}
          />
        </SesFormItem>
        <SesFormItem
          label={t('变量设置')}
          suffix={t('可在此处填写JSON格式的变量设置')}
          status={getValidateStatus(paramField.meta, validating)}
          message={
            getValidateStatus(paramField.meta, validating) === 'error' &&
            paramField.meta.error
          }
        >
          <Input
            multiline
            {...paramField.input}
            autoComplete="off"
            placeholder={`{"userName": "${t('张三')}"}`}
          />
        </SesFormItem>
        <Form.Item
          label={t('退订管理')}
          tips={t(
            '开启后系统将会在您发送的邮件末尾自动加入退订链接，用户退订后将不再收到同一个发信域名发送的邮件。',
          )}
          required={true}
          status={getValidateStatus(UnsubscribeField.meta, validating)}
          message={
            getValidateStatus(UnsubscribeField.meta, validating) === 'error' &&
            UnsubscribeField.meta.error
          }
        >
          <Select
            {...UnsubscribeField.input}
            type="native"
            options={unsubscribeList}
            placeholder={t('请选择...')}
          />
        </Form.Item>
      </Form>
      <Form.Action>
        {/* <div className="send-submit-action">*/}
        {/*  <div className="send-submit-btns">*/}
        <Button
          className="send-submit-btn"
          type="primary"
          htmlType="submit"
          loading={submitting}
          disabled={validating}
        >
          {t('发送')}
        </Button>
        {/* </div>*/}
        {/* </div>*/}
      </Form.Action>
    </form>
  );
};

export default SendEmailForm;
