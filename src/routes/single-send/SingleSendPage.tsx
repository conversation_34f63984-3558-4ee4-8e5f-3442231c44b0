import React, { useCallback } from 'react';
import { t } from '@tea/app/i18n';
import { Layout } from '@tencent/tea-component';
import { sendEmail } from '@src/routes/single-send/actions';
import SendEmailForm from '@src/routes/single-send/components/SendEmailForm/SendEmailForm';
import { Card, message } from '@tea/component';
import './style.less';
import RegionSelect from '@src/components/RegionSelect/Page';

// import { getLinkUrl, LINK_URL_NAME } from '@src/constants/urlConfig';

const { Body, Content } = Layout;

export const SingleSendPage = () => {
  const onCreateSubmit = useCallback((values) => {
    console.log('onCreateSubmit', values);
    const { Destination, ...others } = values;
    const Destination2 = Destination?.split('\n')
      .map(one => `${one}`.trim())
      .filter(one => !!one); // 排除空行和前后空格
    const data = { Destination: Destination2, ...others };
    return sendEmail(data)
      .then((res) => {
        console.log('sendEmail Over', res);
        // setCreateModalVisible(false);
        message.success({ content: t('发送成功') });
        // fetchList();
      })
      .catch(() => {})
      .finally(() => {});
  }, []);

  // const onDeleteConfirm = async (item, idx) => {
  //   const yes = await Modal.confirm({
  //     message: t("确认删除当前所选模板？"),
  //     description: t("删除后，不能再用该模板发送邮件。"),
  //     okText: t("删除"),
  //     cancelText: t("取消"),
  //   });
  //   if (yes) {
  //     return deleteTask(item.TaskID).then(() => {
  //       fetchList();
  //     }).finally(() => {
  //     })
  //   }
  //   // setAnswer(yes ? "已删除" : "未删除");
  // };

  // const apiDocUrl = getLinkUrl(LINK_URL_NAME.apiDoc);

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('邮件发送')} subtitle={<RegionSelect />} />
            <Content.Body>
              {/* 内容区域一般使用 Card 组件显示内容 */}
              <Card className="send-form-page-card">
                <Card.Body>
                  {/* <Alert hideIcon={true}>
                  <Trans>
                    该页面仅支持邮件发送测试，单次测试上限为20个收件地址。
                  </Trans>
                </Alert> */}
                  <SendEmailForm className="send-form" onSubmit={onCreateSubmit} />
                </Card.Body>
              </Card>
              {/* <Table.ActionPanel>*/}
              {/*  <Justify*/}
              {/*    left={*/}
              {/*      <>*/}
              {/*        <Button type="primary" onClick={() => {*/}
              {/*          setCreateModalVisible(true);*/}
              {/*        }}>{t("新建发送任务")}</Button>*/}
              {/*        /!*<Button>刷新</Button>*!/*/}
              {/*      </>*/}
              {/*    }*/}
              {/*    // right={*/}
              {/*    //   <>*/}
              {/*    //     <SearchBox />*/}
              {/*    //     /!*<Button icon="setting" />*!/*/}
              {/*    //     /!*<Button icon="refresh" />*!/*/}
              {/*    //     /!*<Button icon="download" />*!/*/}
              {/*    //   </>*/}
              {/*    // }*/}
              {/*  />*/}
              {/* </Table.ActionPanel>*/}
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      {/* <NewTemplateModal*/}
      {/*  visible={newModalVisible}*/}
      {/*  onCancel={() => {*/}
      {/*    setCreateModalVisible(false);*/}
      {/*  }}*/}
      {/*  onSubmit={onCreateSubmit}*/}
      {/* />*/}
    </>
  );
};
