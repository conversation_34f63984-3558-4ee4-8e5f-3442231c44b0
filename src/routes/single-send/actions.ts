import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';
//
// export function fetchTaskList() {
//   const cmd = SES_CMD.LIST_TASK;
//   return requestApiV3(cmd, {}).then((res: any) => {
//     return res;
//   }).catch((e) => {
//     return handleRequestError(e);
//   });
// }

export function sendEmail(data: any) {
  console.log('sendEmail start data=', data);
  const cmd = SES_CMD.SEND_EMAIL;
  const {
    FromEmailAddress,
    Subject,
    Destination,
    TemplateID,
    TemplateData,
    Unsubscribe,
  } = data;
  // const TemplateData = JSON.stringify({});
  const postData = {
    FromEmailAddress,
    Subject,
    Destination,
    Unsubscribe,
    Template: {
      TemplateID,
      TemplateData: TemplateData || '{}',
    },
    // ReplyToAddresses,
    // Simple,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      console.log('sendEmail catch', e);
      // throw e; // 不自动弹提示，错误的时候上面会出通用提示
      throw handleRequestError(e);
    });
}

// export function getTaskInfo(domain: string) {
//   const cmd = SES_CMD.GET_TASK;
//   return requestApiV3(cmd, { EmailIdentity: domain }).then((res: any) => {
//     return res;
//   }).catch((e) => {
//     return handleRequestError(e);
//   });
// }
//
// export function deleteTask(id: string) {
//   const cmd = SES_CMD.DELETE_TASK;
//   return requestApiV3(cmd, { TaskID: id }).then((res: any) => {
//     return res;
//   }).catch((e) => {
//     return handleRequestError(e);
//   });
// }
