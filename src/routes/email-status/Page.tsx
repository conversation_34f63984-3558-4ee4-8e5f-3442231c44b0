import React from 'react';
import { t } from '@tea/app/i18n';
import { Layout } from '@tencent/tea-component';
// import { TabPanel, Tabs } from '@tea/component';
import EmailStatusTable from '@src/routes/email-status/components/Table';
import './style.less';
import RegionSelect from '@src/components/RegionSelect/Page';

const { Body, Content } = Layout;

export function EmailStatusPage() {
  // const tabs = [
  //   { id: 'blacklist', label: t('黑名单列表') },
  // ];

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('邮件状态')} subtitle={<RegionSelect />} />
            <Content.Body>
              <EmailStatusTable />
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
}
