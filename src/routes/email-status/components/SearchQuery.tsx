import React from 'react';
import { Button, Form, Justify, SearchBox, Text } from '@tea/component';
import { useField, FormRenderProps } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import './SearchQuery.less';
import { RangePicker } from '@tea/component/datepicker/RangePicker';
import moment from 'moment';
import { getValidateStatus } from '@src/utils/FormUtils';

interface Props {
  formRenderProps: FormRenderProps<any>;
}

const SearchQuery: React.FC<Props> = (props) => {
  const { formRenderProps } = props;
  const { form, handleSubmit, validating, submitting } = formRenderProps;

  const timeRange = useField('timeRange', form);
  const receiveEmailAddress = useField('receiveEmailAddress', form);

  function disabledDate(date, start) {
    // 选择范围在今天之前，且7天
    const isAfterToday = date.isAfter(moment(), 'day');
    if (moment.isMoment(start)) {
      return (
        !isAfterToday &&
        !(
          moment(date).subtract(6, 'day').isAfter(start, 'day') ||
          moment(date).add(0, 'day').isBefore(start, 'day')
        )
      );
    }
    return !isAfterToday;
  }

  return (
    <form onSubmit={handleSubmit} className="stats-search-query">
      {/* <Table.ActionPanel>*/}
      <Justify
        left={
          <>
            <Form layout="inline">
              <Form.Item label={t('日期范围')}>
                <RangePicker
                  {...timeRange.input}
                  range={[
                    moment().subtract(29, 'd').startOf('d'),
                    moment().endOf('d'),
                  ]}
                  disabledDate={disabledDate}
                />
              </Form.Item>
              <Form.Item
                label={t('收件邮箱地址')}
                required={true}
                status={getValidateStatus(receiveEmailAddress.meta, validating)}
                extra={
                  <Text theme="danger">
                    {t('注：收件邮箱地址区分大小写，请填入正确的收件邮箱地址')}
                  </Text>
                }
                message={
                  getValidateStatus(receiveEmailAddress.meta, validating) ===
                    'error' && receiveEmailAddress.meta.error
                }
              >
                <SearchBox
                  className="email-address-search-box"
                  size="l"
                  {...receiveEmailAddress.input}
                  placeholder={t('<EMAIL>')}
                />
              </Form.Item>
            </Form>
          </>
        }
        right={
          <div className="right-search-btn-field">
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              disabled={validating}
            >
              {t('查询')}
            </Button>
          </div>
        }
      />
    </form>
  );
};

export default SearchQuery;
