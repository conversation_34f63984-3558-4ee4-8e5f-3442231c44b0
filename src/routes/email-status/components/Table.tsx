import React, { useCallback, useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Card, Table } from '@tencent/tea-component';
import { listEmailSendingRecord } from '@src/routes/email-status/actions';
import moment from 'moment';
import SearchQuery from '@src/routes/email-status/components/SearchQuery';
import { useForm } from 'react-final-form-hooks';
import { autotip } from '@tea/component/table/addons';
import { EVENT_TYPE } from '@src/constants/defaultConstant';
import { validateEmail } from '@src/utils/FormUtils';

const { pageable } = Table.addons;

const initialQueryValues = {
  emailAddress: '',
  timeRange: [moment().add(0, 'w'), moment()],
};

export default function EmailStatusTable() {
  const [list, setList] = useState([]);
  const [total, setTotal] = useState();
  const [listLoading, setListLoading] = useState(false);
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });
  const queryValues = initialQueryValues;

  useEffect(() => {
    statsQuery(queryValues, pageData);
  }, []);

  function statsQuery(values, pageData) {
    console.log('pageData=', pageData);
    const { receiveEmailAddress = '', timeRange = [] } = values;
    const [BeginDate, EndDate] = timeRange.map((one) =>
      one.format('YYYY-MM-DD'),
    );
    const data = {
      To: receiveEmailAddress,
      BeginDate,
      EndDate,
      // Limit: pageData.pageSize,
      // Offset: (pageData.pageIndex - 1) * pageData.pageSize,
    };
    setListLoading(true);
    return (
      listEmailSendingRecord(data)
        .then((res = {}) => {
          console.log('searchQuery Over', res);
          const { Data = [] } = res;
          setList(Data);
          setTotal(Data.length);
        })
        // .catch(() => {})
        .finally(() => {
          setListLoading(false);
        })
    );
  }

  const onSearchSubmit = useCallback((values) => {
    console.log('onSearchSubmit', values, pageData);
    return statsQuery(values, pageData);
  }, []);

  const formRenderProps = useForm({
    onSubmit: onSearchSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: queryValues,
    validate: ({ timeRange, receiveEmailAddress }: any) => ({
      receiveEmailAddress: validateEmail(receiveEmailAddress),
      timeRange: !timeRange ? t('请选择时间段') : undefined,
    }),
  });
  // const { handleSubmit } = formRenderProps;

  // 翻页
  const onPagingChange = (data) => {
    setPageData(data);
  };
  // useEffect(() => {
  //   handleSubmit();
  // }, []);

  return (
    <div className="black-list-table">
      <Card>
        <Card.Body>
          <div className="flex-one-row">
            <SearchQuery formRenderProps={formRenderProps} />
          </div>
        </Card.Body>
      </Card>
      <Card>
        <Table
          verticalTop
          records={list}
          disableTextOverflow
          // recordKey="EmailAddress"
          columns={[
            {
              key: 'CreateTime',
              header: t('创建时间'),
              render: ({ CreateTime }) => (
                <span>
                  {moment(Number(CreateTime)).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              ),
            },
            {
              key: 'FromDomain',
              header: t('发信域名'),
            },
            {
              key: 'TemplateId',
              header: t('模板ID'),
            },
            {
              key: 'Subject',
              header: t('邮件主题'),
            },
            {
              key: 'SmtpMessageId',
              header: 'SmtpMessageId',
            },
            {
              key: 'EventId',
              header: t('事件'),
              render: ({ EventId }) => (
                <span>
                  {EVENT_TYPE.filter((item) => item.id === Number(EventId))[0]
                    ?.value || ''}
                </span>
              ),
            },
            {
              key: 'TaskId',
              header: t('任务ID'),
            },
            {
              key: 'DeliverDesc',
              header: t('递送说明'),
              render: ({ DeliverDesc }) => {
                const regex = /https?:\/\/[^\s/$.?#].[^\s]*/g;
                const replacedText = DeliverDesc.replace(regex, (match) => {
                  return `<a href="${match}" target="_blank">${match}</a>`;
                });
                return (
                  <div dangerouslySetInnerHTML={{ __html: replacedText }}></div>
                );
              },
            },
          ]}
          addons={[
            pageable({
              recordCount: total,
              pageIndex: pageData.pageIndex,
              pageSize: pageData.pageSize,
              pageSizeOptions: [10, 20, 30, 50, 100],
              onPagingChange: (query) => {
                console.log('onPagingChange', query);
                // {pageIndex: 2, pageSize: 10}
                onPagingChange(query);
              },
            }),
            autotip({
              isLoading: listLoading,
            }),
          ]}
        />
      </Card>
    </div>
  );
}
