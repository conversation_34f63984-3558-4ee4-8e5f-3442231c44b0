import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';

export function listEmailSendingRecord(data: any) {
  console.log('searchQuery start data=', data);
  const cmd = SES_CMD.LIST_EMAIL_SENDING_RECORD;
  const { To, BeginDate, EndDate } = data;
  const postData = {
    To,
    BeginDate,
    EndDate,
  };
  return requestApiV3(cmd, postData).then((res: any) => {
    console.log(`request ${cmd}, res=`, res);
    return res;
  })
    .catch((e) => {
      handleRequestError(e);
    });
}
