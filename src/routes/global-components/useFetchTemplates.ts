import { useState, useEffect, useCallback } from 'react';
import { fetchTemplateList } from '@src/routes/template/actions';
import { TEMPLATE_STATUS } from '../template/constants';
import { t } from '@tea/app/i18n';

interface TemplateItem {
  value: string;
  text: string;
  disabled: boolean;
}

const useFetchTemplates = (shouldFetch?) => {
  const [templates, setTemplates] = useState<TemplateItem[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchTemplates = useCallback(async () => {
    setLoading(true);
    try {
      const limit = 1000;
      let offset = 0;
      let totalCount = 0;
      let allTemplates: TemplateItem[] = [];
      let isFirstFetch = true;

      while (isFirstFetch || offset < totalCount) {
        const res = await fetchTemplateList({ Limit: limit, Offset: offset });
        const { TemplatesMetadata = [], TotalCount } = res;
        totalCount = TotalCount || 0;

        const newList: TemplateItem[] = TemplatesMetadata.map((one) => {
          const { TemplateID, TemplateName, TemplateStatus } = one;
          return {
            value: TemplateID,
            text: `${TemplateID}-${TemplateName}${
              TemplateStatus === TEMPLATE_STATUS.PASS
                ? ''
                : ` (${t('该模板尚未通过审核')})`
            }`,
            disabled: TemplateStatus !== TEMPLATE_STATUS.PASS,
          };
        });

        allTemplates = allTemplates.concat(newList);
        setTemplates(allTemplates); // 更新模板状态

        offset += limit;
        isFirstFetch = false;
      }

      if (totalCount === 0) {
        allTemplates.push({
          value: 'none',
          text: t('尚未创建模板'),
          disabled: true,
        });
        setTemplates(allTemplates); // 更新模板状态
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (shouldFetch === true || shouldFetch === undefined) {
      fetchTemplates();
    }
  }, [fetchTemplates, shouldFetch]);

  return { templates, loading };
};

export default useFetchTemplates;
