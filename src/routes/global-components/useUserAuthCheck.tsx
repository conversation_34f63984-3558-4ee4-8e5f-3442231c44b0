import { useEffect } from 'react';
import { fetchAccount } from '../ses-index/actions';
import { useHistory } from '@tencent/tea-app';

export const useUserAuthCheck = () => {
  const history = useHistory();

  useEffect(() => {
    fetchAccount()
      .then(async (res) => {
        if (!res?.Opened) {
          if (history.location.pathname === '/ses/open') return;
          history.replace('/ses/open');
        } else {
          if (history.location.pathname === '/ses/open')
            history.replace('/ses/index');
        }
      })
      .catch(() => {});
  }, []);
};
