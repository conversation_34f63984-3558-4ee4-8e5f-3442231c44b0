import React from 'react';
import { Button, Modal, ModalProps } from '@tencent/tea-component';
import { useToggle } from 'react-use';
import { t } from '@tea/app/i18n';
import { DialogRef, useDialog } from './useDialog';

interface Props {
  title?: string;
  contentText?: React.ReactNode;
  confirmText?: string;
  closeText?: string;
  dialogRef: DialogRef;
  onSuccess?: () => any;
  onClose?: () => void;
  size?: ModalProps['size'];
}
export const ConfirmDialog = ({
  title = t('提示'),
  contentText = t('操作成功'),
  confirmText = t('确定'),
  closeText = t('取消'),
  dialogRef,
  onSuccess,
  onClose,
  size,
}: Props) => {
  const _size = size ?? 's';
  const [visible, setShowState] = useDialog(dialogRef);
  const [loading, toggleLoading] = useToggle(false);

  const handler = async () => {
    toggleLoading(true);
    await onSuccess?.();
    toggleLoading(false);
    setShowState(false);
  };
  const close = () => {
    if (onClose) {
      setShowState(false);
      onClose?.();
    } else {
      handler();
    }
  };

  return (
    <Modal size={_size} visible={visible} caption={title} onClose={close}>
      <Modal.Body>{contentText}</Modal.Body>
      <Modal.Footer>
        <Button loading={loading} type="primary" onClick={handler}>
          {confirmText}
        </Button>
        <Button loading={loading} onClick={close}>
          {closeText}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
