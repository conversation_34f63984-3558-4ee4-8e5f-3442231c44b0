import React from 'react';
import { app } from '@tea/app';
import { accountPackageQuery } from '@src/routes/account-package/actions';
import _ from 'lodash';
import { useEffectOnce } from 'react-use';

const togglePkgMenusVisible = async (visible: boolean) => {
  const menus = await app.sdk.use('menus-sdk');
  menus.sidebar.updateLogic('/ses', { isPackageShow: visible });
};

let isFirstMount = true;

const MenusSdkController: React.FC = ({ children }) => {
  useEffectOnce(() => {
    if (isFirstMount) {
      accountPackageQuery({})
        .then((res = {}) => {
          isFirstMount = false;
          const { AccountPackages = [] } = res;
          const packageLeft = _.sumBy(AccountPackages, (o) => {
            return o.PackageStatus === 0 ? o.AvailableQuota : 0;
          });
          togglePkgMenusVisible(packageLeft > 0);
        })
        .catch(() => {
          togglePkgMenusVisible(false);
        });
    }
  });

  return <>{children}</>;
};
export default MenusSdkController;
