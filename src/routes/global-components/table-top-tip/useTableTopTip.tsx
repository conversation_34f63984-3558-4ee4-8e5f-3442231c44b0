import { t } from '@tea/app/i18n';
import { StatusTip, Status } from '@tencent/tea-component';
import React, { useMemo } from 'react';

interface Props {
  record?: Array<any>;
  loading?: boolean;
  emptyTip?: React.ReactNode;
  loadingTip?: React.ReactNode;
}

export const useTableTopTip = (props: Props) => {
  const { record, loading, emptyTip, loadingTip } = props;
  const isLoading = loading === undefined ? record === undefined : loading;

  const isEmpty = record && record.length === 0;

  const tableTopTip = useMemo(() => {
    if (isLoading) {
      return <StatusTip loadingText={loadingTip} status="loading" />;
    }
    if (isEmpty) {
      return (
        <StatusTip
          emptyText={
            emptyTip ?? <Status size="xs" description={t('暂无数据')} />
          }
          status="empty"
        />
      );
    }
    return false;
  }, [isLoading, isEmpty, loadingTip, emptyTip]);

  return tableTopTip;
};
