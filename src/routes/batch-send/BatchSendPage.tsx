import React, { useCallback, useEffect, useState } from 'react';
import { t, Trans } from '@tea/app/i18n';
import {
  Table,
  Justify,
  Button,
  Card,
  Layout,
  Progress,
  Select,
} from '@tencent/tea-component';
import { Alert, Collapse, Modal } from '@tea/component';
import { batchSendEmail, deleteSendTask, listSendTasks } from './actions';
import NewModal from './components/NewModal/NewModal';
import './style.less';
import { getLinkUrl, LINK_URL_NAME } from '@src/constants/urlConfig';
import RegionSelect from '@src/components/RegionSelect/Page';

const { Body, Content } = Layout;
const { pageable, autotip } = Table.addons;
const warnUpDoc = getLinkUrl(LINK_URL_NAME.warnUpDoc);

const introductionList = [
  t(
    '批量发送适用于营销类、通知类的邮件发送场景。触发类邮件（如身份验证、交易相关等）建议通过 API - SendEmail 接口发送。',
  ),
  // eslint-disable-next-line react/jsx-key
  <Trans>
    批量发送内置自动 Warm Up
    功能，系统会智能判断发信域名/IP的信誉度等级并分配当日最大发信量。
    当实时发信量到达当日最大发信量时，系统会自动暂停发送，剩余未发送的邮件自动进入队列缓存中，并在本次暂停时刻的24小时后自动开始发送。
    详细请参见文档
    <a href={warnUpDoc} target="_blank" rel="noreferrer">
      产品功能-自动Warm Up
    </a>
    章节的说明。
  </Trans>,
  t(
    '同一域名可执行多个发送任务，单日内多个任务的总实时发信量也不能超过当日最大发信量。',
  ),
  t(
    '发送任务进入队列缓存时，任务状态显示为暂停、发送进度条保持静止状态；次日自动开始发送后，任务状态和发送进度会自动更新。',
  ),
];

const getStartTime = (item) => {
  // 批量发送：取创建时间
  // 定时发送：取TimedParm.BeginTime
  // 频率发送：即CycleParm.BeginTime
  switch (item.TaskType) {
    case 1:
      return item.CreateTime;
    case 2:
      return item.TimedParam?.BeginTime;
    case 3:
      return item.CycleParm?.BeginTime;
    default:
      return '-';
  }
};

export const BatchSendPage: React.FC = () => {
  const taskTypeList: any = [
    {
      value: 1,
      text: t('批量发送'),
    },
    {
      value: 2,
      text: t('定时发送'),
    },
    {
      value: 3,
      text: t('频率发送'),
    },
  ];
  const [list, setList] = useState([]);
  const [listLoading, setListLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [taskType, setTaskType] = useState<any>();
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  const [newModalVisible, setNewModalVisible] = useState(false);

  const onPagingChange = (data) => {
    setPageData(data);
  };

  const fetchList = useCallback(
    (params?) => {
      setListLoading(true);
      listSendTasks({
        Offset: (pageData.pageIndex - 1) * pageData.pageSize,
        Limit: pageData.pageSize,
        TaskType: taskType || undefined,
        ...params,
      })
        .then((res: any) => {
          console.log('listSendTasks res=', res);
          setList(res.Data || []);
          setTotal(res.TotalCount || 0);
        })
        .finally(() => {
          setListLoading(false);
        });
    },
    [pageData, taskType],
  );

  const onCreateSubmit = useCallback((values) => {
    const {
      TaskType,
      FromEmailAddress,
      ReceiverId,
      Subject,
      Unsubscribe,
      ADLocation,
    } = values;
    const params = {
      TaskType: Number(TaskType),
      FromEmailAddress,
      ReceiverId,
      Subject,
      Unsubscribe,
      ADLocation: Number(ADLocation),
      Template: {
        TemplateID: values.TemplateID,
        TemplateData: values.TemplateData,
      },
      CycleParam: {
        BeginTime: values.CycleBeginTime?.format('YYYY-MM-DD HH:00:00'),
        IntervalTime: values.CycleIntervalTime && values.CycleIntervalTime * 24,
      },
      TimedParam: {
        BeginTime: values.BeginTime?.format('YYYY-MM-DD HH:00:00'),
      },
    };
    console.log('onCreateSubmit', params);

    return batchSendEmail(params).then((res) => {
      if (res.code && res.code !== 0) {
        return false;
      }
      setNewModalVisible(false);
      fetchList();
    });
  }, []);

  const onDeleteConfirm = async (item) => {
    const yes = await Modal.confirm({
      message: t('确认删除当前发送任务？'),
      description:
        item.TaskStatus !== 10 ? t('删除后，将不会执行该发送任务。') : '',
      okText: t('删除'),
      cancelText: t('取消'),
    });
    if (yes) {
      return deleteSendTask({
        TaskId: item.TaskId,
      }).then(() => {
        fetchList();
      });
    }
  };

  useEffect(() => {
    fetchList();
  }, [pageData]);

  useEffect(() => {
    const timer = setInterval(() => {
      fetchList();
    }, 1000 * 10);
    return () => {
      clearInterval(timer);
    };
  }, [fetchList]);

  const getTaskTypeText = (TaskType) => {
    let text = '-';
    switch (TaskType) {
      case 1:
        text = t('批量发送');
        break;
      case 2:
        text = t('定时发送');
        break;
      case 3:
        text = t('频率发送');
        break;
      default:
        break;
    }
    return text;
  };

  const getTaskStatusText = (TaskStatus) => {
    let text = '-';
    switch (TaskStatus) {
      case 1:
        text = t('待开始');
        break;
      case 5:
        text = t('发送中');
        break;
      case 6:
        text = t('今日暂停发送');
        break;
      case 7:
        text = t('发送异常');
        break;
      case 10:
        text = t('发送完成');
        break;
      default:
        break;
    }
    return text;
  };

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('批量发送')} subtitle={<RegionSelect />} />
            <Content.Body>
              <Alert>
                <h4 style={{ marginBottom: 8 }}>{t('注意事项')}</h4>
                {introductionList.slice(0, 2).map((one, idx) => (
                  <p key={idx}>
                    {idx + 1}. {one}
                  </p>
                ))}
                {introductionList.length > 2 && (
                  <Collapse
                    iconPosition="right"
                    // style={{ marginTop: 8 }}
                  >
                    <Collapse.Panel
                      id="1"
                      title={(active) => (active ? t('收起') : t('展开'))}
                      position="top"
                    >
                      {introductionList.slice(2).map((one, idx) => (
                        <p key={idx}>
                          {idx + 3}. {one}
                        </p>
                      ))}
                    </Collapse.Panel>
                  </Collapse>
                )}
              </Alert>
              <Table.ActionPanel>
                <Justify
                  left={
                    <>
                      <Button
                        type="primary"
                        onClick={() => {
                          setNewModalVisible(true);
                        }}
                      >
                        {t('新建发送任务')}
                      </Button>
                      <Button
                        onClick={() => {
                          fetchList();
                        }}
                      >
                        {t('刷新')}
                      </Button>
                      <Select
                        className="fetch-select"
                        type="simulate"
                        appearance="button"
                        options={taskTypeList}
                        placeholder={t('任务类型')}
                        clearable={true}
                        value={taskType}
                        onChange={(value) => {
                          setTaskType(value);
                          setPageData({
                            pageIndex: 1,
                            pageSize: 10,
                          });
                          fetchList({
                            TaskType: value || undefined,
                          });
                        }}
                      />
                      {/* <Button>开机</Button>*/}
                    </>
                  }
                />
              </Table.ActionPanel>
              <Card>
                <Table
                  verticalTop
                  records={list}
                  recordKey="TaskId"
                  columns={[
                    {
                      key: 'TaskId',
                      header: t('任务ID'),
                      width: 70,
                    },
                    {
                      key: 'CreateTime',
                      header: t('创建时间'),
                      width: 130,
                    },
                    {
                      key: 'FromEmailAddress',
                      header: t('发件人'),
                      width: 130,
                    },
                    {
                      key: 'ReceiverId',
                      header: t('收件人列表ID'),
                      width: 100,
                    },
                    {
                      key: 'ReceiversName',
                      header: t('收件人列表名称'),
                      width: 120,
                    },
                    {
                      key: 'Subject',
                      header: t('邮件主题'),
                      width: 80,
                    },
                    {
                      key: 'Template.TemplateID',
                      header: t('模板ID'),
                      width: 100,
                    },
                    {
                      key: 'SendCount',
                      header: t('发送进度'),
                      width: 140,
                      render: (item) => (
                        <>
                          <Progress
                            percent={Math.floor(
                              (item?.SendCount * 100) /
                                (item?.RequestCount || 1),
                            )}
                            text={(percent) => `${percent} %`}
                          />
                        </>
                      ),
                    },
                    {
                      key: 'TaskType',
                      header: t('任务类型'),
                      width: 80,
                      render: (item) => <>{getTaskTypeText(item.TaskType)}</>,
                    },
                    {
                      key: 'StartTime',
                      header: t('任务开始时间'),
                      width: 130,
                      render: (item) => {
                        return getStartTime(item);
                      },
                    },
                    {
                      key: 'CycleIntervalTime',
                      header: t('周期'),
                      width: 80,
                      render: (item) => {
                        if (item.TaskType === 1 || item.TaskType === 2) {
                          return t('一次性');
                        }
                        return item?.CycleParam?.IntervalTime >= 24
                          ? t('每{{count}}天一次', {
                              count: item.CycleParam.IntervalTime / 24,
                            })
                          : t('每{{count}}小时一次', {
                              count: item.CycleParam.IntervalTime,
                            });
                      },
                    },
                    {
                      key: 'RequestCount',
                      header: t('请求数量'),
                      width: 70,
                    },
                    {
                      key: 'TaskStatus',
                      header: t('任务状态'),
                      width: 80,
                      render: (item) => (
                        <>{getTaskStatusText(item.TaskStatus)}</>
                      ),
                    },
                    {
                      key: 'action',
                      header: t('操作'),
                      width: 100,
                      render: (item) => (
                        <>
                          {item.TaskStatus !== 5 && (
                            <Button
                              type="link"
                              onClick={() => {
                                onDeleteConfirm(item);
                              }}
                            >
                              {t('删除')}
                            </Button>
                          )}
                        </>
                      ),
                    },
                  ]}
                  addons={[
                    pageable({
                      recordCount: total,
                      pageIndex: pageData.pageIndex,
                      pageSize: pageData.pageSize,
                      pageSizeOptions: [10, 20, 30, 50, 100],
                      onPagingChange: (query) => {
                        onPagingChange(query);
                      },
                    }),
                    autotip({
                      isLoading: listLoading,
                    }),
                  ]}
                />
              </Card>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      <NewModal
        visible={newModalVisible}
        onCancel={() => {
          setNewModalVisible(false);
        }}
        onSubmit={onCreateSubmit}
      />
    </>
  );
};
