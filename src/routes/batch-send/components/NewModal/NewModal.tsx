import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Radio,
} from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import { getValidateStatus } from '@src/utils/FormUtils';
import { t, Trans } from '@tea/app/i18n';
import './style.less';
import { SesFormItem } from '@src/components/SesFormItem';
import { fetchAddressList } from '@src/routes/address/actions';
import { fetchContactList } from '@src/routes/contact/actions';
import moment from 'moment';
import { unsubscribeList } from '@src/constants/options';
import useFetchTemplates from '@src/routes/global-components/useFetchTemplates';
import { isPlainObject } from '@src/utils/CommonUtils';
import { useHistory } from '@tencent/tea-app';

enum ReceiverStatus {
  Empty = 1,
  Uploading = 2,
  Success = 3,
  Half = 4,
}
interface ModalProps {
  values?: any;
  // options: any;
  visible: boolean;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

const INITIAL_VALUES = {
  TaskType: 1,
  BeginTime: moment(Date.now()).add(1, 'hour'),
  CycleBeginTime: moment(Date.now()).add(1, 'hour'),
  // Unsubscribe: '2',
};

const NewModal: React.FC<ModalProps> = (props) => {
  const history = useHistory();
  const { visible, onCancel, onSubmit } = props;
  const { templates } = useFetchTemplates(visible);

  const [senderList, setSenderList] = useState([]);
  const [contactenderList, setContactList] = useState([]);
  const taskTypeList: any = [
    {
      value: 1,
      text: t('批量发送'),
    },
    {
      value: 2,
      text: t('定时发送'),
    },
    {
      value: 3,
      text: t('频率发送'),
    },
  ];

  const fetchSenderList = useCallback(() => {
    fetchAddressList()
      .then((res: any) => {
        console.log('fetchAddressList res=', res);
        const { EmailSenders = [] } = res || {};
        const newList = EmailSenders.map((one: any) => {
          const { EmailAddress, EmailSenderName } = one;
          return {
            value: `${EmailSenderName} <${EmailAddress}>`,
            text: `${EmailAddress} (${EmailSenderName})`,
          };
        });

        if (newList.length === 0) {
          newList.push({
            value: 'none',
            text: t('尚未创建发件邮箱'),
            disabled: true,
          });
        }

        setSenderList(newList);
      })
      .finally(() => {});
  }, []);

  const fetchContact = useCallback(() => {
    fetchContactList({
      Limit: 1000,
      Offset: 0,
    })
      .then((res: any) => {
        console.log('fetchContactList res=', res);
        const { Data = [] } = res || {};
        const newList = Data.filter(({ ReceiversStatus }) =>
          [ReceiverStatus.Success, ReceiverStatus.Half].includes(
            ReceiversStatus,
          ),
        ).map((one: any) => {
          const { ReceiverId, ReceiversName, ReceiversStatus } = one;
          return {
            value: `${ReceiverId}`,
            text: `${ReceiversName}`,
            status: ReceiversStatus,
          };
        });

        if (newList.length === 0) {
          newList.push({
            value: 'none',
            text: t('尚未创建收件人列表'),
            disabled: true,
          });
        }

        setContactList(newList);
      })
      .finally(() => {});
  }, []);

  useEffect(() => {
    if (visible) {
      fetchSenderList();
      fetchContact();
    }
  }, [fetchContact, fetchSenderList, visible]);

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    initialValuesEqual: () => true,
    initialValues: INITIAL_VALUES,
    validate: ({
      TaskType,
      ReceiverId,
      Subject,
      TemplateID,
      FromEmailAddress,
      TemplateData,
      BeginTime,
      CycleBeginTime,
      CycleIntervalTime,
      Unsubscribe,
      ADLocation,
    }: any) => {
      let TemplateDataError = undefined;
      if (TemplateData) {
        try {
          const obj = JSON.parse(TemplateData);
          if (!isPlainObject(obj)) {
            TemplateDataError = t('JSON格式解析错误');
          }
        } catch (e) {
          TemplateDataError = t('JSON格式解析错误：') + e;
        }
      }
      return {
        TaskType: !TaskType ? true : undefined,
        ReceiverId: !ReceiverId ? true : undefined,
        Subject:
          !Subject || Subject.length > 100
            ? t('长度为1-{{len}}个字符', { len: 100 })
            : undefined,
        TemplateID: !TemplateID ? true : undefined,
        FromEmailAddress: !FromEmailAddress ? true : undefined,
        BeginTime: TaskType === '2' && !BeginTime ? true : undefined,
        CycleBeginTime: TaskType === '3' && !CycleBeginTime ? true : undefined,
        CycleIntervalTime:
          TaskType === '3' && !CycleIntervalTime ? true : undefined,
        TemplateData: TemplateDataError,
        Unsubscribe: !Unsubscribe ? true : undefined,
        ADLocation: !ADLocation ? true : undefined,
      };
    },
  });

  const taskTypeField = useField('TaskType', form);
  const receiverIdField = useField('ReceiverId', form);
  const SubjectField = useField('Subject', form);
  const templateField = useField('TemplateID', form);
  const templateDataField = useField('TemplateData', form);
  const fromEmailAddressField = useField('FromEmailAddress', form);
  const beginTimeField = useField('BeginTime', form);
  const cycleBeginTimeField = useField('CycleBeginTime', form);
  const cycleIntervalTimeField = useField('CycleIntervalTime', form);
  const UnsubscribeField = useField('Unsubscribe', form);
  const ADLocationField = useField('ADLocation', form);
  const showSuffix = useMemo(() => {
    const selectedContact = contactenderList.find(
      (contact) => contact.value === receiverIdField.input.value,
    );
    return selectedContact?.status === ReceiverStatus.Half;
  }, [receiverIdField.input.value, contactenderList]);

  useEffect(() => {
    if (visible) {
      // 显示编辑框时重置初始值
      form.reset(INITIAL_VALUES);
      form.resetFieldState(taskTypeField.input.name);
      form.resetFieldState(receiverIdField.input.name);
      form.resetFieldState(SubjectField.input.name);
      form.resetFieldState(templateField.input.name);
      form.resetFieldState(templateDataField.input.name);
      form.resetFieldState(fromEmailAddressField.input.name);
      form.resetFieldState(beginTimeField.input.name);
      form.resetFieldState(cycleBeginTimeField.input.name);
      form.resetFieldState(cycleIntervalTimeField.input.name);
      form.resetFieldState(UnsubscribeField.input.name);
    }
  }, [visible]);

  return (
    <Modal
      className="edit-batch-send-task-modal"
      visible={visible}
      caption={t('新建发送任务')}
      onClose={onCancel}
    >
      <form onSubmit={handleSubmit}>
        <Modal.Body>
          <Form
          // layout="vertical"
          >
            <Form.Item
              label={t('任务类型')}
              required={true}
              status={getValidateStatus(taskTypeField.meta, validating)}
              message={
                getValidateStatus(taskTypeField.meta, validating) === 'error' &&
                taskTypeField.meta.error
              }
            >
              <Select
                {...taskTypeField.input}
                options={taskTypeList}
                placeholder={t('请选择...')}
              />
            </Form.Item>
            <Form.Item
              label={t('邮件主题')}
              required={true}
              status={getValidateStatus(SubjectField.meta, validating)}
              message={
                getValidateStatus(SubjectField.meta, validating) === 'error' &&
                SubjectField.meta.error
              }
            >
              <Input {...SubjectField.input} placeholder={t('请输入主题')} />
            </Form.Item>
            <SesFormItem
              label={t('收件人列表')}
              required={true}
              status={getValidateStatus(receiverIdField.meta, validating)}
              suffix={
                showSuffix ? (
                  <Trans>
                    注：该收件人列表导入状态是“导入完成-部分有效”，
                    <a
                      target="_blank"
                      rel="noreferrer"
                      onClick={() => {
                        history.push(
                          '/ses/contact/import?ReceiverId=' +
                            receiverIdField.input.value,
                        );
                      }}
                    >
                      去编辑
                    </a>
                    修改或者删除“无效”收件人
                  </Trans>
                ) : (
                  ''
                )
              }
              message={
                getValidateStatus(receiverIdField.meta, validating) ===
                  'error' && receiverIdField.meta.error
              }
            >
              <Select
                {...receiverIdField.input}
                searchable
                options={contactenderList}
                placeholder={t('请选择...')}
              />
            </SesFormItem>
            <Form.Item
              label={t('模板选择')}
              required={true}
              status={getValidateStatus(templateField.meta, validating)}
              message={
                getValidateStatus(templateField.meta, validating) === 'error' &&
                templateField.meta.error
              }
            >
              <Select
                {...templateField.input}
                searchable
                options={templates}
                placeholder={t('请选择...')}
              />
            </Form.Item>
            <SesFormItem
              label={t('变量设置')}
              suffix={t('可在此处填写JSON格式的变量设置')}
              status={getValidateStatus(templateDataField.meta, validating)}
              message={
                getValidateStatus(templateDataField.meta, validating) ===
                  'error' && templateDataField.meta.error
              }
            >
              <Input
                multiline
                {...templateDataField.input}
                autoComplete="off"
                placeholder={`{"userName": "${t('张三')}"}`}
              />
            </SesFormItem>
            <Form.Item
              label={t('发件人选择')}
              required={true}
              status={getValidateStatus(fromEmailAddressField.meta, validating)}
              message={
                getValidateStatus(fromEmailAddressField.meta, validating) ===
                  'error' && fromEmailAddressField.meta.error
              }
            >
              <Select
                searchable
                {...fromEmailAddressField.input}
                options={senderList}
                placeholder={t('请选择...')}
              />
            </Form.Item>
            <Form.Item
              label={t('退订管理')}
              tips={t(
                '开启后系统将会在您发送的邮件末尾自动加入退订链接，用户退订后将不再收到同一个发信域名发送的邮件。',
              )}
              required={true}
              status={getValidateStatus(UnsubscribeField.meta, validating)}
              message={
                getValidateStatus(UnsubscribeField.meta, validating) ===
                  'error' && UnsubscribeField.meta.error
              }
            >
              <Select
                {...UnsubscribeField.input}
                options={unsubscribeList}
                placeholder={t('请选择...')}
              />
            </Form.Item>
            <Form.Item
              label={t('AD标识')}
              tips={t(
                '开启后系统将会在您发送的邮件主题中自动加入“<AD>”。建议广告营销类邮件开启此项。',
              )}
              required={true}
              status={getValidateStatus(ADLocationField.meta, validating)}
              message={
                getValidateStatus(ADLocationField.meta, validating) ===
                  'error' && ADLocationField.meta.error
              }
            >
              <Radio.Group {...ADLocationField.input}>
                <Radio name="0">{t('不添加')}</Radio>
                <Radio name="1">{t('添加到邮件主题前')}</Radio>
                <Radio name="2">{t('添加到邮件主题后')}</Radio>
              </Radio.Group>
            </Form.Item>
            {taskTypeField.input.value.toString() === '2' && (
              <Form.Item
                label={t('任务开始时间')}
                required={true}
                status={getValidateStatus(beginTimeField.meta, validating)}
                message={
                  getValidateStatus(beginTimeField.meta, validating) ===
                    'error' && beginTimeField.meta.error
                }
              >
                <DatePicker
                  {...beginTimeField.input}
                  format="YYYY-MM-DD HH:00:00"
                  showTime={{ format: 'HH' }}
                  range={[moment().add(1, 'hour'), moment().add(30, 'year')]}
                />
              </Form.Item>
            )}
            {taskTypeField.input.value.toString() === '3' && (
              <>
                <Form.Item
                  label={t('任务开始时间')}
                  required={true}
                  status={getValidateStatus(
                    cycleBeginTimeField.meta,
                    validating,
                  )}
                  message={
                    getValidateStatus(cycleBeginTimeField.meta, validating) ===
                      'error' && cycleBeginTimeField.meta.error
                  }
                >
                  <DatePicker
                    {...cycleBeginTimeField.input}
                    format="YYYY-MM-DD HH:00:00"
                    showTime={{ format: 'HH' }}
                    range={[moment().add(1, 'hour'), moment().add(30, 'year')]}
                  />
                </Form.Item>
                <Form.Item
                  label={t('任务周期')}
                  required={true}
                  status={getValidateStatus(
                    cycleIntervalTimeField.meta,
                    validating,
                  )}
                  message={
                    getValidateStatus(
                      cycleIntervalTimeField.meta,
                      validating,
                    ) === 'error' && cycleIntervalTimeField.meta.error
                  }
                >
                  <InputNumber
                    min={1}
                    unit={t('天')}
                    {...cycleIntervalTimeField.input}
                  />
                </Form.Item>
              </>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            disabled={validating}
          >
            {t('发送')}
          </Button>
          <Button type="weak" htmlType="button" onClick={onCancel}>
            {t('取消')}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default NewModal;
