import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';

export function listSendTasks(params = {}) {
  const cmd = SES_CMD.LIST_SEND_TASKS;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function batchSendEmail(params = {}) {
  const cmd = SES_CMD.BATCH_SEND_EMAIL;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function deleteSendTask(params = {}) {
  const cmd = SES_CMD.DELETE_SEND_TASK;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}
