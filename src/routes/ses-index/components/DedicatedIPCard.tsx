import React from 'react';
import { useHistory } from '@tencent/tea-app';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  ExternalLink,
  LoadingTip,
} from '@tencent/tea-component';
import { t } from '@tencent/tea-app/lib/i18n';
import { useAsyncRetry } from 'react-use';
import { Trans, Slot } from '@tea/app/i18n';
import { getQCMainHost } from '@src/utils/domain';
import { fetchAccount } from '../actions';
import { AccountOpenStatus } from '@src/routes/dedicated-ip/const';
import { getDomainSendIp } from '@src/routes/dedicated-ip/actions';
import { StatusType } from '@src/routes/dedicated-ip/dedicated-ip-manage/const';
import { isIntl } from '@src/utils/CommonUtils';

export const workerLinkHref = `//${window.QCCONSOLE_HOST}/workorder/category`;
export const serviceLinkHref = `//${getQCMainHost()}/online-service?from=intro_ses`;

const DedicatedIPCard = () => {
  const history = useHistory();

  const { value, loading } = useAsyncRetry(async () => {
    // StatusOfIp='独立IP资源状态：0未开通，1正常，2隔离，3销毁'
    const res = await fetchAccount();
    const isOpened = AccountOpenStatus.includes(res?.StatusOfIp);
    if (!isOpened) {
      return { statusOfIp: res?.StatusOfIp, isOpened };
    }
    const { Total = 0 } = await getDomainSendIp({
      Limit: 0,
      Offset: 0,
      Status: StatusType.Open,
    });
    return { isOpened, length: Total };
  }, []);

  if (loading) {
    return (
      <Col span={8}>
        <Card style={{ height: '100%' }}>
          <Card.Body title={t('独立IP')}>
            <LoadingTip />
          </Card.Body>
        </Card>
      </Col>
    );
  }

  if (!value?.isOpened) {
    return (
      <Col span={8}>
        <Card style={{ height: '100%' }}>
          <Card.Body
            title={t('独立IP')}
            operation={
              <Button
                type="link"
                onClick={() => {
                  history.push('/ses/dedicated-ip');
                }}
              >
                {t('开通')}
              </Button>
            }
          >
            <div>
              <Trans>
                独立IP不受其他用户发信干扰，可以保证发信域名和IP的信誉度，提高邮件到达率，点击了解
                <ExternalLink
                  href={`//${getQCMainHost()}/document/product/1288/47930#3ffc1596-07ed-4e3b-9e37-a2264ae2e9af?from_cn_redirect=1`}
                >
                  价格详情
                </ExternalLink>
              </Trans>
            </div>
            <div style={{ marginTop: 4 }}>
              <Trans>
                前置条件：已配置至少一个发信域名和一个发信地址，点击
                <Button
                  type="link"
                  style={{ verticalAlign: 'baseline' }}
                  onClick={() => {
                    history.push('/ses/domain');
                  }}
                >
                  <Slot content={t('配置发信域名')} />
                </Button>
              </Trans>
            </div>
          </Card.Body>
        </Card>
      </Col>
    );
  }

  return (
    <Col span={8}>
      <Card style={{ height: '100%' }}>
        <Card.Body
          title={t('独立IP')}
          operation={
            <Button
              type="link"
              onClick={() => {
                history.push('/ses/dedicated-ip');
              }}
            >
              {t('查看')}
            </Button>
          }
        >
          <div>
            <div>
              {t('当前共有{{count}}个独立IP在使用中', {
                count: value?.length,
              })}
            </div>
            <div style={{ marginTop: 4 }}>
              {t('注意：每月最多支持申请3个独立 IP，点击查看')}
              <Button
                type="link"
                style={{ verticalAlign: 'baseline' }}
                onClick={() => {
                  const url = isIntl
                    ? `//${getQCMainHost()}/document/product/1084/39653`
                    : `//${getQCMainHost()}/document/product/1288/52779`;
                  window.open(url, '_blank');
                }}
              >
                {t('申请建议')}
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>
    </Col>
  );
};
export default DedicatedIPCard;
