import React, { useCallback, useMemo, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Col, H3, ImagePreview, Row, Stepper } from '@tea/component';
import './style.less';
import { LANGUAGE_TYPE, getCurrentLanguage, AREA_TYPE, getCurrentUserArea } from '@src/utils/CommonUtils';
import { Link } from 'react-router-dom';
import step1 from '@src/assets/images/know-step-1.png';
import step2 from '@src/assets/images/know-step-2.png';
import step3 from '@src/assets/images/know-step-3.png';
import step4 from '@src/assets/images/know-step-4.png';
import enStep1 from '@src/assets/images/en/know-step-1.png';
import enStep2 from '@src/assets/images/en/know-step-2.png';
import enStep3 from '@src/assets/images/en/know-step-3.png';
import enStep4 from '@src/assets/images/en/know-step-4.png';
import intlStep1 from '@src/assets/images/intl-zh/know-step-1.png';
import intlStep2 from '@src/assets/images/intl-zh/know-step-2.png';
import intlStep3 from '@src/assets/images/intl-zh/know-step-3.png';
import intlStep4 from '@src/assets/images/intl-zh/know-step-4.png';

const currentLanguage = getCurrentLanguage();
const currentUserArea = getCurrentUserArea();
console.log('FastKnowStep.js currentLanguage:', currentLanguage);

function getImgSrc(idx: number) {
  const imgSteps = [step1, step2, step3, step4];
  const enImgSteps = [enStep1, enStep2, enStep3, enStep4];
  const intlImgSteps = [intlStep1, intlStep2, intlStep3, intlStep4];
  if (currentUserArea === AREA_TYPE.MAINLAND) {
    return imgSteps[idx];
  }
  if (currentLanguage === LANGUAGE_TYPE.EN) {
    return enImgSteps[idx];
  }
  return intlImgSteps[idx];
}

const originSteps = [
  {
    id: 'domain',
    label: t('发信域名配置'),
    detail: (
      <div>
        {t('设置自定义域名，域名管理员配置DNS。')}
        <Link to="/ses/domain"> {t('立即开始')} &gt;</Link>
      </div>
    ),
    imgSrc: getImgSrc(0),
  },
  {
    id: 'address',
    label: t('发信地址配置'),
    imgSrc: getImgSrc(1),
    detail: (
      <div>
        {t('自定义您需要的发信地址。')}
        <Link to="/ses/address"> {t('立即开始')} &gt;</Link>
      </div>
    ),
    // tip: <p>
    //   <ExternalLink>这是tip</ExternalLink>
    // </p>
  },
  {
    id: 'template',
    label: t('发信模版配置'),
    imgSrc: getImgSrc(2),
    detail: (
      <div>
        {t('根据您的需求配置模版内容。')}
        <Link to="/ses/template"> {t('立即开始')} &gt;</Link>
      </div>
    ),
  },
  {
    id: 'createTask',
    label: t('开始发信'),
    imgSrc: getImgSrc(3),
    detail: (
      <div>
        {t('配置完成后，使用普通发送功能发送您的第一封邮件。')}
        <Link to="/ses/send"> {t('立即开始')} &gt;</Link>
      </div>
    ),
  },
];

export function FastKnowStep() {
  const [current, setCurrent] = useState(0);
  const handleStepClick = useCallback((item, index) => {
    console.log('setCurrent', index);
    setCurrent(index);
  }, []);
  const steps: any = useMemo(() => {
    const ret = originSteps.map((one: any, idx) => {
      let { label } = one;
      const { detail } = one;
      if (label) label = (
          <div
            className="can-click"
            onClick={() => {
              handleStepClick(one, idx);
            }}
          >
            {label}
          </div>
      );
      // if (detail) label = (<div className='can-click' onClick={() => {
      //   handleStepClick(one, idx)
      // }}>{detail}</div>);
      return { ...one, label, detail };
    });
    return ret;
  }, []);
  const currentData = useMemo(() => {
    const step = steps[current] || {};
    const { imgSrc, label, id } = step;
    return { src: imgSrc, previewSrc: imgSrc, previewTitle: label, id };
  }, [current, steps]);
  return (
    <div className="fast-know-step-component">
      <Row>
        <Col span={6}>
          <H3 className="my-card__title">{t('快速入门')}</H3>
          <Stepper
            type={'process-vertical-dot'}
            steps={steps}
            current={currentData.id}
          />
        </Col>
        <Col>
          <ImagePreview
            className="right-image"
            src={currentData.src}
            previewSrc={currentData.previewSrc}
            previewTitle={currentData.previewTitle}
          />
        </Col>
      </Row>
    </div>
  );
}
