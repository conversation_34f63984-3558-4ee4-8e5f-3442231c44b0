@import '../../../../styles/variables.less';

.fast-know-step-component {
  .my-card__title {
    margin-bottom: 28px; // 以前的title是18，然后下面有个东西top -10
  }

  .app-ses-step__item {
    color: black;

    .app-ses-step__title {
      font-weight: normal;
    }

    &.is-current {
      .app-ses-step__title {
        font-weight: 700;
      }
    }

    &.is-current ~ .app-ses-step__item {
      color: black;

      .app-ses-step__title {
        font-weight: normal;
      }
    }
  }

  .right-image {
    width: 100%;
    height: 460px; //350px;
    object-fit: contain;
    background: white;
  }

  .can-click {
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}
