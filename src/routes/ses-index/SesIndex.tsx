import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { t, Trans } from '@tea/app/i18n';
import get from 'lodash/get';
import { useHistory } from '@tea/app';
import { <PERSON><PERSON>, Card, Col, Layout, Row } from '@tea/component';
import { FastKnowStep } from '@src/routes/ses-index/components/FastKnowStep';
import './style.less';
import { fetchAccount } from '@src/routes/ses-index/actions';
import { fetchDomainList } from '@src/routes/domain/actions';
import { fetchAddressList } from '@src/routes/address/actions';
import { fetchTemplateList } from '@src/routes/template/actions';
import { getLinkUrl, LINK_URL_NAME } from '@src/constants/urlConfig';
import RegionSelect from '@src/components/RegionSelect/Page';
import DedicatedIPCard from './components/DedicatedIPCard';

const { Body, Content } = Layout;

const newNumberFormat = new Intl.NumberFormat();
const numberFormat = (num) => newNumberFormat.format(num);

export function SesIndex() {
  const history = useHistory();
  const [data, setData]: [any, any] = useState({});
  const [listData, setListData] = useState({
    domainCount: NaN,
    senderCount: NaN,
    templateCount: NaN,
  });
  // const [dataLoading, setDataLoading] = useState(true);

  // 单域名单日最高发信量
  const [domainMaxReputation, setDomainMaxReputation] = useState({
    dailyQuota: NaN,
    reputationLevel: NaN,
  });
  const fetchAccountInto = useCallback(
    () =>
      // setDataLoading(true);
      // setDomainList([]);
      fetchAccount()
        .then((res: any) => {
          // console.log('QueryAccount res=', res);
          // DailySendingLimit: 300000
          // FreeQuota: 9993
          // Opened: true
          // RequestId: "********-0a31-4cdd-aee2-cb9b9e4bfe32"
          // Status: 0
          // UserHourlySendingLimit: 10
          // WebhookUrl: ""
          if (res?.Opened) setData(res);
        })
        .finally(() => {
          // setDataLoading(false);
        }),
    [],
  );

  const fetchLists = useCallback(() => {
    // setDataLoading(true);
    // setDomainList([]);
    Promise.all([
      fetchDomainList(),
      fetchAddressList(),
      fetchTemplateList({ Limit: 1 }),
    ]).then((results) => {
      const [domainInfo, senderInfo, templateInfo] = results || [];
      setDomainMaxReputation({
        dailyQuota: get(domainInfo, 'MaxDailyQuota'),
        reputationLevel: get(domainInfo, 'MaxReputationLevel'),
      });
      const newListData = {
        domainCount: get(domainInfo, 'EmailIdentities.length'),
        senderCount: get(senderInfo, 'EmailSenders.length'),
        templateCount: get(templateInfo, 'TotalCount'),
      };
      // console.log('all then', results);
      setListData(newListData);
    });
  }, []);

  useEffect(() => {
    fetchAccountInto()?.then(() => {
      fetchLists();
    });
  }, []);

  const { DailySendingLimit = NaN, FreeQuota, ReputationPolicyId } = data || {};

  return (
    <Layout>
      <Body>
        <Content>
          <Content.Header
            title={t('概览')}
            subtitle={<RegionSelect />}
          ></Content.Header>
          <Content.Body>
            <Row>
              <Col>
                <Card>
                  <Card.Body title={t('按量计费')}>
                    <Trans>
                      1000封免费测试，超过免费额度的发送将会根据实际使用量计费，查看
                      <a
                        href={getLinkUrl(LINK_URL_NAME.payDoc)}
                        target="_blank"
                        rel="noreferrer"
                      >
                        价格说明
                      </a>
                      。
                    </Trans>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
            <Row>
              <DedicatedIPCard />
              <Col span={8}>
                <Card style={{ height: '100%' }}>
                  <Card.Body
                    title={t('发信域名')}
                    operation={
                      <Button
                        type="link"
                        onClick={() => {
                          history.push('/ses/domain');
                        }}
                      >
                        {t('查看')}
                      </Button>
                    }
                  >
                    <p>
                      {t('当前共有 {{count}} 个发信域名', {
                        count: listData.domainCount,
                      })}
                    </p>
                    {ReputationPolicyId === 0 && (
                      <p>
                        {t('每日发送限制：{{formattedCount}}封', {
                          count: DailySendingLimit,
                          formattedCount: numberFormat(DailySendingLimit),
                        })}
                      </p>
                    )}
                    {ReputationPolicyId > 0 && (
                      <p>
                        {t('单域名最高信誉等级是 {{levelCount}} 级', {
                          levelCount: domainMaxReputation.reputationLevel,
                        })}
                      </p>
                    )}
                    {ReputationPolicyId > 0 && (
                      <p>
                        {t('单域名单日最高发信量为 {{emailCount}} 封', {
                          emailCount: numberFormat(
                            domainMaxReputation.dailyQuota,
                          ),
                        })}
                        ,&nbsp;
                        <a
                          href={getLinkUrl(LINK_URL_NAME.reputationDoc)}
                          target="_blank"
                          rel="noreferrer"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {t('点击查看提升规则')}
                        </a>
                      </p>
                    )}
                  </Card.Body>
                </Card>
              </Col>
              <Col span={8} style={{ height: '100%' }}>
                <Card>
                  <Card.Body
                    style={{ lineHeight: 1 }}
                    title={t('发信地址')}
                    operation={
                      <Button
                        type="link"
                        onClick={() => {
                          history.push('/ses/address');
                        }}
                      >
                        {t('查看')}
                      </Button>
                    }
                  >
                    {t('当前共有 {{count}} 个发信地址', {
                      count: listData.senderCount,
                    })}
                  </Card.Body>
                </Card>
                <Card style={{ marginTop: 3 }}>
                  <Card.Body
                    style={{ lineHeight: 1 }}
                    title={t('发信模板')}
                    operation={
                      <Button
                        type="link"
                        onClick={() => {
                          history.push('/ses/template');
                        }}
                      >
                        {t('查看')}
                      </Button>
                    }
                  >
                    {t('当前共有 {{count}} 个发信模板', {
                      count: listData.templateCount,
                    })}
                  </Card.Body>
                </Card>
              </Col>
            </Row>
            <Row>
              <Col>
                <Card>
                  <Card.Body>
                    <FastKnowStep />
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Content.Body>
        </Content>
      </Body>
    </Layout>
  );
}
