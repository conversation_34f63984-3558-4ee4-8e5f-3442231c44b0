import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';

export function fetchAddressList() {
  const cmd = SES_CMD.LIST_ADDRESS;
  return requestApiV3(cmd, {})
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function addAddress(data: any) {
  console.log('addAddress start');
  const cmd = SES_CMD.ADD_ADDRESS;
  const { name, domain, prefix } = data;
  const postData = {
    EmailAddress: `${prefix}@${domain}`,
    EmailSenderName: name,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      console.log(`request ${cmd}, catch=`, e);
      // throw e;
      throw handleRequestError(e);
    });
}

export function getDomainInfo(domain: string) {
  const cmd = SES_CMD.GET_DOMAIN;
  return requestApiV3(cmd, { EmailIdentity: domain })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function deleteAddress(address: string) {
  const cmd = SES_CMD.DELETE_ADDRESS;
  return requestApiV3(cmd, { EmailAddress: address })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function UpdateEmailSmtpPassWord(data: any) {
  const cmd = SES_CMD.UPDATE_SMTP_PASSWORD;
  return requestApiV3(cmd, data)
    .then((res: any) => res)
    .catch((e) => {
      handleRequestError(e);
    });
}
