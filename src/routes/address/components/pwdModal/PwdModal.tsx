import React, { useCallback, useEffect } from 'react';
import { Button, Form, Input, Modal } from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import { getValidateStatus } from '@src/utils/FormUtils';
import { t } from '@tea/app/i18n';
import './style.less';

interface Props {
  values?: any;
  // options: any;
  visible: boolean;
  caption: string;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

const INITIAL_VALUES = { password: '' };
const PwdModal: React.FC<Props> = (props) => {
  const { visible, onCancel, onSubmit, caption } = props;
  const passwordValidate = useCallback((password) => {
    if (!password) {
      return t('请输入密码');
    }
    if (
      !/^\S*(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])\S*$/.test(password) ||
      password.length < 10 ||
      password.length > 20
    ) {
      return t('长度为10~20位，且必须包含数字、大写字母、小写字母。');
    }

    const numberRegExp = /\d/g;
    const azRegExp = /[a-z]/g;
    const AZRegExp = /[A-Z]/g;
    const numberList = numberRegExp[Symbol.match](password);
    const azList = azRegExp[Symbol.match](password);
    const AZList = AZRegExp[Symbol.match](password);

    if (
      new Set(numberList).size < 2 ||
      new Set(azList).size < 2 ||
      new Set(AZList).size < 2
    ) {
      return t(
        '至少包含2位数字、2位大写字母和2位小写字母，并且数字和字母均不能只由单一字符重复组成。',
      );
    }
    return undefined;
  }, []);

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: INITIAL_VALUES,
    validate: ({ password }: any) => ({
      password: passwordValidate(password),
    }),
  });

  const passwordField = useField('password', form);

  useEffect(() => {
    if (visible) {
      // 显示编辑框时重置初始值
      form.reset(INITIAL_VALUES);
      form.resetFieldState(passwordField.input.name);
      // form.restart(); // restart是react-final-form支持，但是final-form-hook还不支持。所以只能先用reset，reset会展示validate的错误提示
    }
  }, [visible]);

  return (
    <Modal
      className="smtp-pwd-modal"
      visible={visible}
      caption={caption}
      onClose={onCancel}
    >
      <form onSubmit={handleSubmit}>
        <Modal.Body>
          <Form
          // layout="vertical"
          >
            <Form.Item
              required
              label={t('SMTP密码')} // "备注名"
              // label="昵称"
              status={getValidateStatus(passwordField.meta, validating)}
              message={
                getValidateStatus(passwordField.meta, validating) === 'error' &&
                passwordField.meta.error
              }
            >
              <Input
                {...passwordField.input}
                // placeholder={t("")}
                type="password"
                autoComplete="off"
              />
            </Form.Item>
            <Form.Item
              label="" // "备注名"
            >
              <div className="tips">
                {`1.${t(
                  '长度为10~20位，且必须包含数字、大写字母、小写字母。',
                )}`}
              </div>
              <div className="tips">
                {`2.${t(
                  '至少包含2位数字、2位大写字母和2位小写字母，并且数字和字母均不能只由单一字符重复组成。',
                )}`}
              </div>
              {/* <div className="tips">
                {t('3.不能与上一次设置密码相同。')}
              </div> */}
            </Form.Item>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            disabled={validating}
          >
            {t('提交')}
          </Button>
          <Button type="weak" htmlType="button" onClick={onCancel}>
            {t('取消')}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default PwdModal;
