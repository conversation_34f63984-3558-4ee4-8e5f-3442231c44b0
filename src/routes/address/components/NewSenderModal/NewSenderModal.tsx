import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Modal, Select } from '@tea/component';
import { t, Trans } from '@tea/app/i18n';
import { fetchDomainList } from '@src/routes/domain/actions';
import './style.less';
import { emailRules } from '@src/constants/defaultConstant';
import { useHistory } from '@tencent/tea-app';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/formFn';
import _ from 'lodash';

interface Props {
  values?: any;
  // options: any;
  visible: boolean;
  onSubmit: (values: any) => void;
  onCancel: () => void;
  validateDomain: (domain: string) => string | undefined;
  record: string[];
}

const validateName = (name) => {
  if (!name) return t('请输入发件人别名');
  if (emailRules.test(name) || name.includes('@')) {
    return t('发件人别名不允许使用邮箱格式');
  }
  return undefined;
};

const validatPrefix = ({ prefix, domain, record }) => {
  if (!prefix) return t('请输入邮箱前缀');
  const suffix = '@' + domain;
  if (record.includes(prefix + suffix)) {
    return t('当前发信地址已存在');
  }
  return undefined;
};

const INITIAL_VALUES = { domain: '', name: '', prefix: '' };

const NewSenderModal: React.FC<Props> = (props) => {
  const { visible, onCancel, onSubmit, validateDomain, record = [] } = props;
  const history = useHistory();
  const [domainOptions, setDomainOptions] = useState([
    { value: t('加载中...'), disabled: true },
  ]);

  useEffect(() => {
    fetchDomainList().then((res) => {
      const { EmailIdentities = [] } = res || {};
      const domains = EmailIdentities.filter(
        ({ SendingEnabled }) => SendingEnabled,
      ).map((one: any) => {
        const { IdentityName } = one;
        return { value: IdentityName, text: IdentityName };
      });
      console.log('domains:', domains);
      setDomainOptions(domains);
    });
  }, []);

  return (
    <Modal visible={visible} caption={t('新建发信地址')} onClose={onCancel}>
      <FinalForm
        onSubmit={onSubmit}
        initialValuesEqual={(val, oldVal) => {
          return _.isEqual(val, oldVal);
        }}
        initialValues={{
          ...INITIAL_VALUES,
          domain: domainOptions.length ? domainOptions[0].value : '',
        }}
      >
        {({ handleSubmit, validating, submitting, form }) => {
          const domain = form.getFieldState('domain')?.value;
          const prefix = form.getFieldState('prefix')?.value || '';
          const suffix = `@${domain}`;
          const address = `${prefix}${suffix}`;
          return (
            <form onSubmit={handleSubmit}>
              <Modal.Body>
                <Form>
                  <Field
                    name="domain"
                    validateFields={['prefix']}
                    validate={validateDomain}
                  >
                    {({ input, meta }) => (
                      <Form.Item
                        showStatusIcon={false}
                        suffix={t('每个域名仅支持配置10个发信地址')}
                        label={t('发信域名')}
                        status={getStatus(meta, validating)}
                        message={
                          <>
                            {!domainOptions.length ? (
                              <div>
                                <Trans>
                                  暂无发信域名，请先至
                                  <Button
                                    type="link"
                                    style={{ verticalAlign: 'baseline' }}
                                    onClick={() => {
                                      history.push('/ses/domain');
                                    }}
                                  >
                                    发信域名
                                  </Button>
                                  新建
                                </Trans>
                              </div>
                            ) : (
                              getStatus(meta, validating) === 'error' &&
                              meta.error
                            )}
                          </>
                        }
                      >
                        <Select
                          {...input}
                          type="simulate"
                          searchable
                          appearance="button"
                          options={domainOptions}
                          placeholder={t('请选择域名')}
                        />
                      </Form.Item>
                    )}
                  </Field>
                  <Field
                    name="prefix"
                    validateFields={[]}
                    validate={(prefix, { domain = '' }: { domain?: string }) =>
                      validatPrefix({ prefix, domain, record })
                    }
                  >
                    {({ input, meta }) => (
                      <Form.Item
                        suffix={suffix}
                        showStatusIcon={false}
                        label={t('邮箱前缀')}
                        status={getStatus(meta, validating)}
                        message={
                          getStatus(meta, validating) === 'error' && meta.error
                        }
                      >
                        <Input {...input} autoComplete="off" />
                      </Form.Item>
                    )}
                  </Field>
                  <Field
                    name="name"
                    validateFields={[]}
                    validate={validateName}
                  >
                    {({ input, meta }) => (
                      <Form.Item
                        showStatusIcon={false}
                        label={t('发件人别名')}
                        status={getStatus(meta, validating)}
                        message={
                          getStatus(meta, validating) === 'error' && meta.error
                        }
                      >
                        <Input {...input} autoComplete="off" />
                      </Form.Item>
                    )}
                  </Field>
                  <Form.Item label={t('发信地址预览')}>
                    <Input readonly value={address} />
                  </Form.Item>
                </Form>
                <Form.Action style={{ textAlign: 'center' }}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={submitting}
                    disabled={validating}
                  >
                    {t('提交')}
                  </Button>
                  <Button
                    type="weak"
                    htmlType="button"
                    onClick={(e) => {
                      e?.preventDefault();
                      onCancel();
                    }}
                  >
                    {t('取消')}
                  </Button>
                </Form.Action>
              </Modal.Body>
            </form>
          );
        }}
      </FinalForm>
    </Modal>
  );
};

export default NewSenderModal;
