import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { t } from '@tea/app/i18n';
import {
  Alert,
  Button,
  Card,
  Collapse,
  Justify,
  Layout,
  SearchBox,
  Table,
} from '@tencent/tea-component';
import { Modal } from '@tea/component';
import NewSenderModal from '@src/routes/address/components/NewSenderModal/NewSenderModal';
import {
  addAddress,
  deleteAddress,
  fetchAddressList,
  UpdateEmailSmtpPassWord,
} from '@src/routes/address/actions';
import { i18n } from '@tea/app';
import { DateTimeDisplay } from '@src/components/DateTimeDisplay';
import PwdModal from './components/pwdModal/PwdModal';
import { isIntl } from '@src/utils/CommonUtils';
import { SELECTED_REGION_STORAGE_KEY } from '@src/constants/defaultConstant';
import RegionSelect from '@src/components/RegionSelect/Page';
import _ from 'lodash';

const { Body, Content } = Layout;
const { pageable, autotip } = Table.addons;

// const fakeList = [
//   {
//     EmailAddress: '<EMAIL>',
//     CreatedTimestamp: 1606291839,
//   },
//   {
//     EmailAddress: '<EMAIL>',
//     CreatedTimestamp: 1606291939,
//   },
// ];

function getUrl() {
  if (isIntl) return 'sg-smtp.qcloudmail.com';
  return localStorage.getItem(SELECTED_REGION_STORAGE_KEY) === 'gz'
    ? 'gz-smtp.qcloudmail.com'
    : 'smtp.qcloudmail.com';
}

const introductionList = [
  t('每个域名仅支持创建10个发信地址。'),
  t('SMTP服务地址：{{url}}，SMTP服务端口号：{{port}}。', {
    port: '465',
    url: getUrl(),
  }),
  t('设置SMTP密码在5分钟内生效。'),
];

function validateDomain(domain: string, list: { EmailAddress: string }[]) {
  if (!domain) return t('请选择域名');
  const existAddr = list.filter((v) => v.EmailAddress.includes(`@${domain}`));
  if (existAddr.length >= 10) return t('此域名发信地址配置已达上限');
  return undefined;
}

export function AddressPage() {
  // const recordList = useMemo(() => fakeList, []);
  const [list, setList] = useState([]);
  const [newModalVisible, setNewModalVisible] = useState(false);
  const [listLoading, setListLoading] = useState(true);
  const [pwdModalVisible, setPwdModalVisible] = useState(false);
  const [type, setType] = useState(0);
  const [currentAddress, setCurrentAddress] = useState();

  const fetchList = useCallback(() => {
    setListLoading(true);
    // setDomainList([]);
    fetchAddressList()
      .then((res: any) => {
        console.log('fetchAddressList res=', res);
        const { EmailSenders = [] } = res || {};
        const newList = EmailSenders.map((one: any) => {
          return {
            ..._.pick(one, [
              'EmailAddress',
              'EmailSenderName',
              'CreatedTimestamp',
              'SmtpPwdType',
            ]),
          };
        });
        setList(newList);
      })
      .finally(() => {
        setListLoading(false);
      });
  }, []);

  useEffect(() => {
    fetchList();
  }, []);

  const onCreateSubmit = useCallback((values) => {
    console.log('onCreateSubmit', values);
    return addAddress(values).then(() => {
      setNewModalVisible(false);
      fetchList();
    });
  }, []);

  const onPwdSubmit = useCallback(
    (values) => {
      console.log('onPwdSubmit', values);
      return UpdateEmailSmtpPassWord({
        Password: values.password,
        EmailAddress: currentAddress,
      }).then(() => {
        setPwdModalVisible(false);
        fetchList();
      });
    },
    [currentAddress],
  );

  const onDeleteConfirm = async (item) => {
    const yes = await Modal.confirm({
      message: t('确认删除当前所选地址？'),
      description: t('删除后，不能再用该地址发送邮件。'),
      okText: t('删除'),
      cancelText: t('取消'),
    });
    if (yes) {
      return deleteAddress(item.EmailAddress).then(() => {
        fetchList();
      });
    }
  };

  const onSetSmtpPwd = (address, type) => {
    setCurrentAddress(address);
    setPwdModalVisible(true);
    setType(type);
  };

  // 右上角搜索框
  const [searchBox, setSearchBox] = useState('');
  const onSearchBoxChange = (value) => {
    setSearchBox(value);
  };
  const filterList = useMemo(() => {
    if (searchBox) {
      return list.filter((one) => {
        const { EmailAddress = '', EmailSenderName = '' } = one || {};
        return (
          EmailAddress.includes(searchBox) ||
          EmailSenderName.includes(searchBox)
        );
      });
    }
    return list;
  }, [list, searchBox]);

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('发信地址')} subtitle={<RegionSelect />} />
            <Content.Body>
              <Alert>
                <h4 style={{ marginBottom: 8 }}>{t('注意事项')}</h4>
                {introductionList.slice(0, 2).map((one, idx) => (
                  <p key={idx}>
                    {idx + 1}. {one}
                  </p>
                ))}
                {introductionList.length > 2 && (
                  <Collapse
                    iconPosition="right"
                    // style={{ marginTop: 8 }}
                  >
                    <Collapse.Panel
                      id="1"
                      title={(active) => (active ? t('收起') : t('展开'))}
                      position="top"
                    >
                      {introductionList.slice(2).map((one, idx) => (
                        <p key={idx}>
                          {idx + 3}. {one}
                        </p>
                      ))}
                    </Collapse.Panel>
                  </Collapse>
                )}
              </Alert>
              <Table.ActionPanel>
                <Justify
                  left={
                    <>
                      <Button
                        type="primary"
                        loading={listLoading}
                        onClick={() => {
                          setNewModalVisible(true);
                        }}
                      >
                        {t('新建')}
                      </Button>
                      {/* <Button>开机</Button>*/}
                    </>
                  }
                  right={
                    <>
                      <SearchBox onChange={onSearchBoxChange} />
                      {/* <Button icon="setting" />*/}
                      {/* <Button icon="refresh" />*/}
                      {/* <Button icon="download" />*/}
                    </>
                  }
                />
              </Table.ActionPanel>
              <Card>
                <Table
                  verticalTop
                  records={filterList}
                  recordKey="EmailAddress"
                  // rowDisabled={record => record.status === "start"}
                  // rowClassName={record => record.status}
                  // topTip={
                  //   listLoading && (
                  //     <StatusTip
                  //       status={listLoading ? 'loading' : 'found'}
                  //       // onClear={() => setStatus("loading")}
                  //       // onRetry={() => setStatus("loading")}
                  //     />
                  //   )
                  // }
                  columns={[
                    {
                      key: 'EmailAddress',
                      header: t('发信地址'),
                    },
                    {
                      key: 'EmailSenderName',
                      header: t('发件人别名'),
                    },
                    {
                      key: 'CreatedTimestamp',
                      header: t('创建时间'),
                      render: (
                        record: any,
                        rowKey: string,
                        recordIndex: number,
                        column: any,
                        // columnIndex: number
                      ) => {
                        const item = record[column.key];
                        console.log('item!=', item, 'lng=', i18n.lng);
                        return <DateTimeDisplay timestamp={item} />;
                        // const time = item && item * 1000;
                        // // 用当前语言显示时间
                        // const dateStr: string = new Date(time).toLocaleString(i18n.lng);
                        // return (
                        //   <>
                        //     {dateStr}
                        //   </>
                        // )
                      },
                    },
                    {
                      key: 'action',
                      header: t('操作'),
                      // align: "right",
                      width: 220,
                      render: (item) => (
                        <>
                          {/* <Button type="link" onClick={() => {*/}
                          {/*  onDetailClick(item, idx);*/}
                          {/* }}>{t("详情")}</Button>*/}
                          <Button
                            type="link"
                            onClick={() => {
                              onSetSmtpPwd(item.EmailAddress, item.SmtpPwdType);
                            }}
                          >
                            {item.SmtpPwdType === 1
                              ? t('修改SMTP密码')
                              : t('设置SMTP密码')}
                          </Button>
                          <Button
                            type="link"
                            onClick={() => {
                              onDeleteConfirm(item);
                            }}
                          >
                            {t('删除')}
                          </Button>
                        </>
                      ),
                    },
                  ]}
                  addons={[
                    pageable(),
                    autotip({
                      isLoading: listLoading,
                    }),
                  ]}
                />
              </Card>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      <NewSenderModal
        visible={newModalVisible}
        validateDomain={(domain) => validateDomain(domain, list)}
        onCancel={() => {
          setNewModalVisible(false);
        }}
        record={list.map((v) => v.EmailAddress)}
        onSubmit={onCreateSubmit}
      />
      <PwdModal
        visible={pwdModalVisible}
        caption={type === 1 ? t('修改SMTP密码') : t('设置SMTP密码')}
        onCancel={() => {
          setPwdModalVisible(false);
        }}
        onSubmit={onPwdSubmit}
      />
    </>
  );
}
