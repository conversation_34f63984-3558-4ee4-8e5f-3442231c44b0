/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { t, Trans } from '@tea/app/i18n';
import _ from 'lodash';
import {
  Table,
  Justify,
  Button,
  Card,
  Layout,
  Text,
  Bubble,
  Icon,
  List,
} from '@tencent/tea-component';
import { Alert, Modal } from '@tea/component';
import { createContactList, deleteContact, fetchContactList } from './actions';
import NewModal from './components/NewModal/NewModal';
import { LANGUAGE_TYPE, getCurrentLanguage } from '@src/utils/CommonUtils';
import RegionSelect from '@src/components/RegionSelect/Page';
import style from './style.module.less';
import { useBeforeDestroy, useHistory } from '@tencent/tea-app';
import { ProForm } from '@tencent/tea-material-pro-form';
const { Body, Content } = Layout;
const { pageable, autotip } = Table.addons;

enum ReceiverStatus {
  Empty = 1,
  Uploading = 2,
  Success = 3,
  Half = 4,
}

const importStatusOptions = [
  { value: '', text: t('所有') },
  { value: ReceiverStatus.Empty, text: t('待导入') },
  { value: ReceiverStatus.Uploading, text: t('处理中') },
  { value: ReceiverStatus.Success, text: t('导入完成-全部有效') },
  {
    value: ReceiverStatus.Half,
    text: t('导入完成-部分有效'),
    tooltip: function (count) {
      return t('有{{count}}个“无效”收件人，请点击编辑“修改”或者“删除”', {
        count,
      });
    },
  },
];

const introductionList = [
  { title: t('列表名称和描述将用来调用列表，因此不可重复。') },
  {
    title: t('上传文件要求：'),
    children: [
      <Trans>
        支持 CSV 格式文件 (
        <a
          href={
            // 国内国际不同csv文件
            getCurrentLanguage() === LANGUAGE_TYPE.EN
              ? // 英文版
                'https://sescache.intl.tencent-cloud.com/sample/receivers_v4_EN.csv'
              : // 中文版
                'https://sescache.intl.tencent-cloud.com/sample/receivers_v4_CH.csv'
          }
          target="_self"
        >
          点击下载模板文件
        </a>
        )；每个收件人列表最多支持上传50万个收件人地址。
      </Trans>,
      t(
        '当文件中存在“重复收件人”、“收件人为空”、“整个变量json长度超过1000字符”，上传时会直接剔除。',
      ),
    ],
  },
  {
    title: t('每个收件人列表最多支持上传50万个收件人地址，大小不超过100Mb。'),
  },
];
const searchFields = [
  {
    key: 'KeyWord',
    name: 'KeyWord',
    type: 'string',
    autoComplete: 'off',
    header: t('列表名称'),
    title: t('列表名称'),
  },
  {
    key: 'Status',
    name: 'Status',
    type: 'string',
    component: 'select',
    header: t('导入状态'),
    title: t('导入状态'),
    appearance: 'button',
    className: style['select-field'],
    options: importStatusOptions,
  },
];
export function ContactPage() {
  const history = useHistory();
  const [contactList, setContactList] = useState([]);
  const [listLoading, setListLoading] = useState(true);
  const [searchData, setSearchData] = useState(null);
  const searchDataRef = useRef(null);
  const timerIdRef = useRef(null);
  const searchFormRef = useRef(null);
  searchDataRef.current = searchData;

  const [total, setTotal] = useState(0);
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  const [newModalVisible, setNewModalVisible] = useState(false);

  const onPagingChange = (data) => {
    setPageData(data);
  };

  const fetchList = useCallback(
    (data?) => {
      //去除掉data中的所有空值 在settime调用时 形成了闭包 所以需要使用ref来转存
      const newData = _.pickBy(searchDataRef.current, (v) => v !== '');
      setListLoading(true);
      fetchContactList({
        Offset: (pageData.pageIndex - 1) * pageData.pageSize,
        Limit: pageData.pageSize,
        ...newData,
      })
        .then((res: any) => {
          const { Data = [] } = res;
          Data.forEach((data) => {
            data.Count = data.Count.toString();
          });
          setContactList(Data || []);
          setTotal(res.TotalCount || 0);
          // 有上传中的情况，继续请求
          const receiversStatusList = Data.map((item) => item.ReceiversStatus);
          if (receiversStatusList.includes(ReceiverStatus.Uploading)) {
            timerIdRef.current = setTimeout(() => {
              fetchList({ ...data });
            }, 2500);
          }
        })
        .finally(() => {
          setListLoading(false);
        });
    },
    [pageData, searchData],
  );
  // 防止内存泄漏
  useBeforeDestroy(() => {
    clearTimeout(timerIdRef.current);
  }, []);
  const onCreateSubmit = useCallback(
    (values) => {
      return createContactList(values)
        .then((res) => {
          if (res.code && res.code !== 0) {
            return false;
          }
          setNewModalVisible(false);
          fetchList();
        })
        .catch(() => {})
        .finally(() => {});
    },
    [fetchList],
  );

  const onDeleteConfirm = async (item) => {
    const yes = await Modal.confirm({
      message: t('确认删除当前收件人列表？'),
      description: t('删除后，不能再用该收件人列表发送邮件。'),
      okText: t('删除'),
      cancelText: t('取消'),
    });
    if (yes) {
      return deleteContact({
        ReceiverId: item.ReceiverId,
      })
        .then(() => {
          fetchList();
        })
        .finally(() => {});
    }
  };

  useEffect(() => {
    fetchList();
  }, [pageData, searchData]);

  const tableColumns = useMemo(
    () => [
      {
        key: 'ReceiverId',
        header: t('列表ID'),
        width: 100,
      },
      {
        key: 'ReceiversName',
        header: t('列表名称'),
      },
      {
        key: 'Desc',
        header: t('描述'),
      },

      {
        key: 'CreateTime',
        header: t('创建时间'),
      },
      {
        key: 'Count',
        header: (
          <>
            {t('总数')}
            <Bubble content={t('指的是导入有效的收件人个数。')}>
              <Icon type="info" />
            </Bubble>
          </>
        ),
        render: ({ Count, InvalidCount }) => Count - InvalidCount,
      },
      {
        key: 'ReceiversStatus',
        header: t('导入状态'),
        render: ({ ReceiversStatus, InvalidCount }) => {
          const val = _.find(
            importStatusOptions,
            (v) => v.value === ReceiversStatus,
          );
          return (
            <div className={style['center-item']}>
              <Text
                theme={ReceiversStatus === 3 ? 'success' : 'danger'}
                tooltip={val?.tooltip}
              >
                {val?.text}
              </Text>
              {val?.tooltip && (
                <Bubble content={val.tooltip(InvalidCount)}>
                  <Icon type="error" />
                </Bubble>
              )}
            </div>
          );
        },
      },
      {
        key: 'action',
        header: t('操作'),
        width: 120,
        render: (item) => (
          <>
            <Button
              type="link"
              onClick={() => {
                history.push(
                  '/ses/contact/import?ReceiverId=' + item.ReceiverId,
                );
              }}
            >
              {t('编辑')}
            </Button>
            <Button
              type="link"
              onClick={() => {
                onDeleteConfirm(item);
              }}
            >
              {t('删除')}
            </Button>
          </>
        ),
      },
    ],
    [onDeleteConfirm],
  );
  return (
    <>
      <Layout className={style['contact-page']}>
        <Body>
          <Content>
            <Content.Header
              title={t('收件人列表')}
              subtitle={<RegionSelect />}
            />
            <Content.Body>
              <Alert>
                <h4 style={{ marginBottom: 8 }}>{t('注意事项')}</h4>
                <List type="number">
                  {introductionList.map(({ title, children }, i) => (
                    <List.Item key={i}>
                      {title}
                      {children && (
                        <List type="bullet">
                          {children.map((content, index) => (
                            <List.Item key={index}>{content}</List.Item>
                          ))}
                        </List>
                      )}
                    </List.Item>
                  ))}
                </List>
              </Alert>
              <Table.ActionPanel>
                <Justify
                  className={style['contact-query']}
                  left={
                    <>
                      <Button
                        type="primary"
                        onClick={() => {
                          setNewModalVisible(true);
                        }}
                      >
                        {t('新建收件人列表')}
                      </Button>
                      <Button
                        onClick={() => {
                          fetchList();
                        }}
                      >
                        {t('刷新')}
                      </Button>
                      <ProForm
                        submitter={false}
                        layout="inline"
                        fields={searchFields as any}
                        onRef={(ref) => (searchFormRef.current = ref)}
                      />
                      <Button
                        onClick={() => {
                          setSearchData({ ...searchFormRef.current.values });
                        }}
                        type="primary"
                      >
                        {t('查询')}
                      </Button>
                    </>
                  }
                />
              </Table.ActionPanel>
              <Card>
                <Table
                  verticalTop
                  records={contactList}
                  recordKey="ReceiverId"
                  columns={tableColumns}
                  addons={[
                    pageable({
                      recordCount: total,
                      pageIndex: pageData.pageIndex,
                      pageSize: pageData.pageSize,
                      pageSizeOptions: [10, 20, 30, 50, 100],
                      onPagingChange: (query) => {
                        onPagingChange(query);
                      },
                    }),
                    autotip({
                      isLoading: listLoading,
                    }),
                  ]}
                />
              </Card>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      <NewModal
        visible={newModalVisible}
        onCancel={() => {
          setNewModalVisible(false);
        }}
        onSubmit={onCreateSubmit}
      />
    </>
  );
}
