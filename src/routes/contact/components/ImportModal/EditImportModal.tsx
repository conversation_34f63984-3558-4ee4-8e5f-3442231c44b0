import React from 'react';
import { Button, FormProps, Modal } from '@tencent/tea-component';
import { t } from '@tencent/tea-app/lib/i18n';
import DynamicForm from '@src/components/DynamicForm/index';
import { fieldList } from './AddImportModal';
import { useSearchParam } from 'react-use';
import { updateContactDetailWithData } from '../../actions';
const formProps = {
  layout: 'default',
} as FormProps;

const EditImportModal = ({
  visible,
  onCancel,
  valueRef,
  hasTemplateInput,
  onRetry,
}) => {
  const ReceiverId = useSearchParam('ReceiverId') || '';
  const onSubmit = (Datas) => {
    const { TemplateData, Email, EmailId } = Datas[0];
    return updateContactDetailWithData({
      ReceiverId,
      ReceiverDetail: [
        {
          TemplateData,
          Email,
          EmailId,
        },
      ],
    })
      .then((res) => {
        if (res.code && res.code !== 0) {
          return false;
        } else {
          onRetry?.();
          onCancel?.();
        }
      })
      .catch(() => {})
      .finally(() => {});
  };
  return (
    <Modal
      visible={visible}
      caption={t('修改{{Email}}', { Email: valueRef.current?.Email })}
      onClose={onCancel}
      // size="auto"
    >
      <Modal.Body>
        <DynamicForm
          formProps={formProps}
          fieldList={fieldList.filter(
            (item) => hasTemplateInput || item.name !== 'TemplateData',
          )}
          onSubmit={onSubmit}
          showButtons={false}
          defaultValues={[valueRef.current]}
          footerRender={({ submitting }) => {
            return (
              <Modal.Footer>
                <Button type="primary" htmlType="submit" loading={submitting}>
                  {t('确定')}
                </Button>
                <Button type="weak" htmlType="button" onClick={onCancel}>
                  {t('取消')}
                </Button>
              </Modal.Footer>
            );
          }}
        ></DynamicForm>
      </Modal.Body>
    </Modal>
  );
};
export default EditImportModal;
