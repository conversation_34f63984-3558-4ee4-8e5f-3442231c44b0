import React from 'react';
import {
  Button,
  FormProps,
  Input,
  Modal,
  TextArea,
} from '@tencent/tea-component';
import { t } from '@tencent/tea-app/lib/i18n';
import DynamicForm from '@src/components/DynamicForm/index';
import { emailReg } from '@src/utils/FormUtils';
import { isPlainObject } from '@src/utils/CommonUtils';
import { createContactDetailWithData } from '../../actions';
import { useSearchParam } from 'react-use';

const formProps = {
  layout: 'inline',
} as FormProps;
export const fieldList = [
  {
    name: 'Email',
    itemProps: { label: t('收件人'), showStatusIcon: false },
    fieldProps: { autoComplete: 'off', placeholder: t('请输入') },
    validate: (email: string): string | undefined => {
      if (!email) {
        return t('收件人邮箱地址不能为空！');
      }
      if (!emailReg.test(email)) {
        return t('邮箱地址格式输入错误！');
      }
    },
    component: Input,
  },
  {
    name: 'TemplateData',
    validate: (json) => {
      if (!json) {
        return t('变量不能为空，请输入json格式的变量！');
      }
      if (json.length > 1000) {
        return t('变量长度不能超过1000个字符，请重新输入！');
      }
      try {
        const obj = JSON.parse(json);
        if (!isPlainObject(obj)) {
          return t('JSON格式错误，请按照如上示例格式进行填写！');
        }
      } catch (e) {
        return t('JSON格式错误，请按照如上示例格式进行填写！');
      }
    },
    itemProps: {
      label: t('变量'),
      showStatusIcon: false,
      extra: ({ index }) => {
        return (
          index === 0 && (
            <>
              <p>
                {t(
                  '需在此处填写JSON格式的变量{"变量名1":"变量值","变量名2":"变量值",……}',
                )}
              </p>
              <p>{t('示例：{"Username":"张三","City":"北京"}')}</p>
            </>
          )
        );
      },
    },
    fieldProps: {
      autoComplete: 'off',
      placeholder: t(
        '{"请输入变量名1":"请输入变量值",\n"请输入变量名2":"请输入变量值"，\n……}',
      ),
      size: 'l',
    },
    component: TextArea,
  },
];

const AddImportModal = ({ visible, onCancel, hasTemplateInput, onRetry }) => {
  const ReceiverId = useSearchParam('ReceiverId') || '';
  const onSubmit = (Datas) => {
    return createContactDetailWithData({
      ReceiverId,
      Datas,
    })
      .then((res) => {
        if (res.code && res.code !== 0) {
          return false;
        } else {
          onRetry?.();
          onCancel?.();
          return true;
        }
      })
      .catch(() => {})
      .finally(() => {});
  };
  return (
    <Modal
      visible={visible}
      caption={t('添加收件人')}
      onClose={onCancel}
      size="auto"
    >
      <Modal.Body>
        <DynamicForm
          formProps={formProps}
          fieldList={fieldList.filter(
            (item) => hasTemplateInput || item.name !== 'TemplateData',
          )}
          onSubmit={onSubmit}
          footerRender={({ submitting }) => {
            return (
              <Modal.Footer>
                <Button type="primary" htmlType="submit" loading={submitting}>
                  {t('确定')}
                </Button>
                <Button type="weak" htmlType="button" onClick={onCancel}>
                  {t('取消')}
                </Button>
              </Modal.Footer>
            );
          }}
        ></DynamicForm>
      </Modal.Body>
    </Modal>
  );
};
export default AddImportModal;
