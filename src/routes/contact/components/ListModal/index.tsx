import React, { useEffect, useState } from 'react';
import { Button, Modal, StatusTip, Table, SearchBox } from '@tea/component';
import { t } from '@tea/app/i18n';
import { listReceiverDetails } from '../../actions';
import './style.less';

interface ModalProps {
  values?: any;
  // options: any;
  visible: boolean;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

const NewModal: React.FC<ModalProps> = (props) => {
  const { visible, onCancel, values = {} } = props;
  // console.log('newmodal--', props);
  const [status, setStatus] = useState('none');
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });
  const [detailDataList, setDetailDataList] = useState([]);
  const [listKeys, setListKeys] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [searchEmail, setSearchEmail] = useState('');

  const { pageable } = Table.addons;

  const getListReceiverDetails = (params) => {
    listReceiverDetails(params)
      .then((res) => {
        const { Data = [], TotalCount } = res;
        // 数据为空的情况
        if (Data.length === 0) {
          setListKeys([]);
          setDetailDataList([]);
          setStatus('empty');
          setTotalCount(TotalCount);
          return;
        }
        setTotalCount(TotalCount);
        const dataList = Data.map((item) => {
          const { Email, TemplateData } = item;
          //
          let parseTemplateData = {};
          if (TemplateData) parseTemplateData = JSON.parse(TemplateData);
          return {
            Email,
            ...parseTemplateData,
          };
        });
        // console.log('dataList--', dataList);
        setListKeys(Object.keys(dataList[0]));
        setDetailDataList(dataList);
        setStatus('none');
      })
      .catch(() => {
        setStatus('empty');
      });
  };

  useEffect(() => {
    // 改变pagedata，触发拉取数据
    setStatus('loading');
    const params = {
      ReceiverId: values.ReceiverId,
      Email: searchEmail,
      Offset: (pageData.pageIndex - 1) * pageData.pageSize,
      Limit: pageData.pageSize,
    };
    getListReceiverDetails(params);
  }, [pageData]);

  const onSearch = (value) => {
    // console.log('onSearch--', value);
    setSearchEmail(value);
    setPageData({
      pageIndex: 1,
      pageSize: 10,
    });
  };

  return (
    <Modal
      className="edit-contact-modal"
      visible={visible}
      caption={values?.ReceiversName}
      onClose={onCancel}
      size='auto'
    >
      <>
        <Modal.Body>
          <Table.ActionPanel>
            <SearchBox
              style={{ width: '60%' }}
              onSearch={onSearch} onPressEnter={onSearch}
              onClear={() => onSearch('')}
              placeholder='Email'
            />
          </Table.ActionPanel>
          <Table
            className='list-modal'
            verticalTop
            records={detailDataList}
            recordKey="email"
            style={{ width: `${listKeys.length * 100}px` }}
            // rowDisabled={record => record.status === 'stopped'}
            // rowClassName={record => record.status}
            columns={listKeys.map((key, idx) => ({
              key,
              header: idx === 0 ? t('收件人') : key,
              width: 100,
            }))}
            topTip = {
              // @ts-ignore
              status !== 'none' && <StatusTip status={status} />
            }
            addons={[pageable({
              recordCount: totalCount,
              pageSizeOptions: [10, 20, 30, 50, 100],
              pageIndex: pageData.pageIndex,
              pageSize: pageData.pageSize,
              onPagingChange: (query) => {
                const { pageIndex, pageSize } = query;
                setPageData({ pageIndex, pageSize });
              },
            })]}
          />
        </Modal.Body>
        <Modal.Footer>
          <Button type="weak" htmlType="button" onClick={onCancel}>
            {t('取消')}
          </Button>
        </Modal.Footer>
      </>
    </Modal>
  );
};

export default NewModal;
