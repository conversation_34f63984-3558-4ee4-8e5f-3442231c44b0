import React, { useEffect } from 'react';
import { Button, Form, Input, Modal } from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import { getValidateStatus } from '@src/utils/FormUtils';
import { t } from '@tea/app/i18n';
import './style.less';
import { INPUT_RULE } from '@src/constants/defaultConstant';

interface ModalProps {
  values?: any;
  // options: any;
  visible: boolean;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

const INITIAL_VALUES = { ReceiversName: '', Desc: '' };

const validInput = (val, len) => {
  if (val.length < 1 || val.length > len) return t('长度为1-{{len}}个字符', { len });
  if (!INPUT_RULE.test(val)) return t('仅支持中文字符、大小写字母（a-z、A-Z）、数字（0-9）和下划线');
  return undefined;
};

const NewModal: React.FC<ModalProps> = (props) => {
  const { visible, onCancel, onSubmit } = props;

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: INITIAL_VALUES,
    validate: ({ ReceiversName, Desc }: any) => ({
      ReceiversName: validInput(ReceiversName, 200),
      // ReceiversName.length < 1 || ReceiversName.length > 30
      //   ? t('长度为1-30个字符')
      //   : undefined,
      Desc: validInput(Desc, 300),
      // Desc.length < 1 || Desc.length > 50 ? t('长度为1-50个字符') : undefined,
    }),
  });

  const receiversNameField = useField('ReceiversName', form);
  const descField = useField('Desc', form);

  useEffect(() => {
    if (visible) {
      // 显示编辑框时重置初始值
      form.reset(INITIAL_VALUES);
      form.resetFieldState(receiversNameField.input.name);
      form.resetFieldState(descField.input.name);
    }
  }, [visible]);

  return (
    <Modal
      className="edit-contact-modal"
      visible={visible}
      caption={t('新建收件人列表')}
      onClose={onCancel}
    >
      <form onSubmit={handleSubmit}>
        <Modal.Body>
          <Form
          // layout="vertical"
          >
            <Form.Item
              label={t('列表名称')}
              required={true}
              status={getValidateStatus(receiversNameField.meta, validating)}
              message={
                getValidateStatus(receiversNameField.meta, validating)
                  === 'error' && receiversNameField.meta.error
              }
            >
              <Input
                {...receiversNameField.input}
                size="l"
                autoComplete="off"
                placeholder={t('不能重复，长度为1-200个字符')}
              />
            </Form.Item>
            <Form.Item
              label={t('描述')}
              required={true}
              status={getValidateStatus(descField.meta, validating)}
              message={
                getValidateStatus(descField.meta, validating) === 'error'
                && descField.meta.error
              }
            >
              <Input
                {...descField.input}
                size="l"
                autoComplete="off"
                placeholder={t('长度为1-{{len}}个字符', { len: 300 })}
              />
            </Form.Item>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            disabled={validating}
          >
            {t('提交')}
          </Button>
          <Button type="weak" htmlType="button" onClick={onCancel}>
            {t('取消')}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default NewModal;
