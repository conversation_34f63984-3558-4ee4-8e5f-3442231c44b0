import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';
import _ from 'lodash';

export function fetchContactList(params = {}) {
  const cmd = SES_CMD.LIST_RECEIVERS;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function createContactList(params = {}) {
  const cmd = SES_CMD.CREATE_RECEIVER;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function createContactDetail(params = {}) {
  const cmd = SES_CMD.CREATE_RECEIVER_DETAIL;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function createContactDetailWithData(params = {}) {
  const cmd = SES_CMD.CREATE_RECEIVER_DETAIL_DATA;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function updateContactDetailWithData(params = {}) {
  const cmd = SES_CMD.UPDATE_RECEIVER_DETAIL;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function uploadReceiversDetail(params = {}) {
  const cmd = SES_CMD.UPLOAD_RECEIVERS_DETAIL;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function deleteContact(params = {}) {
  const cmd = SES_CMD.DELETE_RECEIVER;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function listReceiverDetails(params = {}) {
  const cmd = SES_CMD.LIST_RECEIVER_DETAILS;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function deleteReceiverDetail(params = {}) {
  const cmd = SES_CMD.DELETE_RECEIVER_DETAIL;
  return requestApiV3(cmd, { ...params })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function uploadReceiversDetailByCOS(params = {}) {
  const cmd = SES_CMD.UPLOAD_RECEIVERS_DETAIL_BY_COS;
  return requestApiV3(
    cmd,
    { ...params },
    {},
    {
      tipLoading: false,
    },
  )
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function downloadRecipientDetail(params = {}) {
  const cmd = SES_CMD.DOWNLOAD_RECIPIENT_DETAIL;
  return requestApiV3(
    cmd,
    _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
  )
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function getCOSConf(params = {}) {
  const cmd = SES_CMD.GET_COS_CONF;
  return requestApiV3(
    cmd,
    { ...params },
    {},
    {
      tipLoading: false,
    },
  )
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}
