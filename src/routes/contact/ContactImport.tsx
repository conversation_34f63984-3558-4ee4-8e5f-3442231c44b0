import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Trans, t } from '@tea/app/i18n';
import {
  Alert,
  List,
  Layout,
  Button,
  Table,
  H5,
  Text,
  Justify,
  Upload,
  Modal,
  Bubble,
  Icon,
  message,
} from '@tencent/tea-component';
import _ from 'lodash';
import { useSearchParam } from 'react-use';
import {
  downloadFile,
  getCurrentLanguage,
  LANGUAGE_TYPE,
} from '@src/utils/CommonUtils';
import { useHistory } from '@tencent/tea-app';
import { ProTable } from '@tencent/tea-material-pro-table';
import { ProForm } from '@tencent/tea-material-pro-form';
import AddImportModal from './components/ImportModal/AddImportModal';
import EditImportModal from './components/ImportModal/EditImportModal';
import {
  deleteReceiverDetail,
  downloadRecipientDetail,
  listReceiverDetails,
  uploadReceiversDetailByCOS,
} from './actions';
import style from './style.module.less';
import { beforeUploadCheck, uploadToCOS } from './const';
const { Body, Content } = Layout;

const reminderList = [
  {
    title: t('上传文件要求：'),
    children: [
      <Trans>
        支持 CSV 格式文件 (
        <a
          href={
            // 国内国际不同csv文件
            getCurrentLanguage() === LANGUAGE_TYPE.EN
              ? // 英文版
                'https://sescache.intl.tencent-cloud.com/sample/receivers_v4_EN.csv'
              : // 中文版
                'https://sescache.intl.tencent-cloud.com/sample/receivers_v4_CH.csv'
          }
          target="_self"
        >
          点击下载模板文件
        </a>
        )；每个收件人列表最多支持上传50万个收件人地址。
      </Trans>,
      t(
        '当文件中存在“重复收件人”、“收件人为空”、“整个变量json长度超过1000字符”，上传时会直接剔除。',
      ),
    ],
  },
  {
    title: t('每个收件人列表最多支持上传50万个收件人地址，大小不超过100Mb。'),
  },
  {
    title: (
      <Trans>
        当收件人状态为“无效”时，
        <Text theme="danger">请修改或者删除“无效”收件人。</Text>
      </Trans>
    ),
  },
];
const getActualMsg = (
  totalCount,
  validCount,
  tooLongCount,
  emptyEmailCount,
) => {
  if (totalCount === validCount) {
    return '';
  }
  if (tooLongCount > 0 || emptyEmailCount > 0) {
    return t(
      '（存在“重复收件人”、“收件人为空”、“变量数据超过1000字符”，已直接剔除）',
    );
  }
  return t('（存在“重复收件人”，已直接剔除）');
};
const showModal = (actualMsg, totalCount, validCount) => {
  const modal = Modal.show({
    size: 'auto',
    children: (
      <Trans>
        原文件：<Text theme="danger">{{ totalCount }}个</Text>
        收件人（不包含空行）
        <br />
        实际上传：
        <Text theme="danger">{{ validCount }}个</Text>
        收件人
        <Text theme="danger">{{ actualMsg }}</Text>
      </Trans>
    ),
    onClose: () => modal.destroy(),
  });
};
const statusOptions = [
  {
    value: '',
    text: t('所有'),
  },
  {
    value: 1,
    text: t('有效'),
  },
  {
    value: 2,
    text: t('无效'),
    tooltip: (reason: string) => {
      return reason;
    },
  },
];
export function ContactImport() {
  const ReceiverId = useSearchParam('ReceiverId') || '';
  const history = useHistory();
  const searchFormRef = useRef(null);
  const actionRef = useRef(null);
  const idEmailMap = useRef(null);
  const currentValueRef = useRef(null);
  const [detailData, setDetailData] = useState<any>({});
  const {
    TotalCount = 0,
    ValidCount = 0,
    InvalidCount = 0,
    hasTemplateInput = true,
  } = detailData;
  const currentPageIndex = useRef(0);
  const [searchForm, setSearchForm] = useState(null);
  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [batchDeleteLoading, setBatchDeleteLoading] = useState(false);
  const handleBack = () => {
    history.push('/ses/contact');
  };

  const onDeleteConfirm = async (
    EmailIds,
    isMany = false,
    successCallback?,
  ) => {
    //用顿号分割
    const Email: string = idEmailMap.current[EmailIds];

    const yes = await Modal.confirm({
      message: isMany ? t('请确认批量删除操作') : t('请确认删除操作'),
      description: isMany
        ? t('删除后，这些收件人会从收件人列表移除。确定删除吗？')
        : t('删除后，{{Email}}会从收件人列表移除。确定删除吗？', { Email }),
      okText: t('删除'),
      cancelText: t('取消'),
    });
    if (yes) {
      setBatchDeleteLoading(true);
      return deleteReceiverDetail({
        ReceiverId: ReceiverId,
        EmailId: EmailIds,
      })
        .then(() => {
          successCallback?.();
          actionRef.current.reload();
        })
        .finally(() => {
          setBatchDeleteLoading(false);
        });
    }
  };
  const importColumns = useMemo(
    () => [
      {
        key: 'Email',
        name: 'Email',
        type: 'string',
        header: t('收件人'),
        title: t('收件人'),
      },
      {
        key: 'TemplateData',
        header: t('变量'),
      },
      {
        key: 'CreateTime',
        name: 'CreateTime',
        type: 'date',
        component: 'rangePicker',
        header: t('创建时间'),
        title: t('创建时间'),
        showTime: true,
        clearable: true,
      },
      {
        key: 'Status',
        name: 'Status',
        type: 'string',
        component: 'select',
        header: t('状态'),
        title: t('状态'),
        appearance: 'button',
        className: style['select-field'],
        options: statusOptions,
        render: ({ Status, Reason }) => {
          const val = _.find(statusOptions, (v) => v.value === Status);
          return (
            <div className={style['center-item']}>
              <Text
                theme={Status === 1 ? 'success' : 'danger'}
                tooltip={val?.tooltip}
              >
                {val?.text}
              </Text>
              {val?.tooltip && (
                <Bubble content={val.tooltip(Reason)}>
                  <Icon type="error" />
                </Bubble>
              )}
            </div>
          );
        },
      },
      {
        key: 'actions',
        header: t('操作'),
        render: (value) => {
          return (
            <>
              <Button
                type="link"
                onClick={(e) => {
                  e.stopPropagation();
                  currentValueRef.current = value;
                  setEditVisible(true);
                }}
              >
                {t('修改')}
              </Button>
              <Button
                type="link"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteConfirm([value.EmailId]);
                }}
              >
                {t('删除')}
              </Button>
            </>
          );
        },
      },
    ],
    [],
  );
  const canDelete = !(selectedKeys.length > 0);
  const searchFields: any = importColumns.filter((item) =>
    ['Email', 'CreateTime', 'Status'].includes(item.key),
  );

  const getListReceiverDetails = useCallback(
    async (params: {
      ReceiverId: string;
      Email: string;
      Status: string;
      pageSize: number;
      current: number;
      pageIndex: number;
      CreateTimeEnd: string;
      CreateTimeBegin: string;
    }) => {
      if (currentPageIndex.current !== params.pageIndex) {
        setSelectedKeys([]);
        currentPageIndex.current = params.pageIndex;
      }
      try {
        const {
          Email,
          Status,
          CreateTimeEnd,
          CreateTimeBegin,
          pageSize,
          pageIndex,
          ReceiverId,
        } = params;
        if (!ReceiverId) {
          return { success: false, data: [] };
        }
        const filterData = _.pickBy({ Email, Status }, (v) => v !== '');
        const {
          Data: data = [],
          TotalCount: total,
          ValidCount,
          InvalidCount,
        } = await listReceiverDetails({
          ...filterData,
          ReceiverId,
          CreateTimeBegin,
          CreateTimeEnd,
          Limit: pageSize,
          Offset: (pageIndex - 1) * pageSize,
        });
        //进来时有数据 可以判断是否有模版 筛选条件变动导致总数变0时 复用结果（如果是就来就是0，复用的结果也是true）
        setDetailData(({ hasTemplateInput }) => ({
          TotalCount: total,
          ValidCount,
          InvalidCount,
          hasTemplateInput:
            total > 0 ? data?.[0]?.TemplateData !== '' : hasTemplateInput,
        }));

        //映射一个id和email的map
        idEmailMap.current = data.reduce((acc, cur) => {
          acc[cur.EmailId] = cur.Email;
          return acc;
        }, {});

        return { data, success: true, total };
      } catch (error) {}
    },
    [],
  );
  //需要memo 不然会触发table数据的relaod
  const requestParams = useMemo(() => {
    const { CreateTime, ...restData } = searchForm || {};
    const [begin, end] = CreateTime || [];
    const CreateTimeBegin = begin?.format?.('YYYY-MM-DD HH:mm:ss');
    const CreateTimeEnd = end?.format?.('YYYY-MM-DD HH:mm:ss');
    return { ...restData, ReceiverId, CreateTimeBegin, CreateTimeEnd };
  }, [searchForm, ReceiverId]);

  // 校验文件并且上传
  // 主处理上传的函数
  const handleUpload = async (fileResult, isAccepted) => {
    const { hide } = message.loading({
      content: t('导入中，请勿离开当前界面...'),
      duration: 20000,
    });

    try {
      const success = await beforeUploadCheck(fileResult, isAccepted);
      if (!success) {
        return;
      }
      //文件名用原本的名字拼接时间戳
      const fileName = `${fileResult.name.split('.')[0]}_${Date.now()}.csv`;
      await uploadToCOS(fileResult, fileName);
      const { TotalCount, ValidCount, TooLongCount, EmptyEmailCount, code } =
        await uploadReceiversDetailByCOS({
          ReceiverId,
          FileName: fileName,
        });
      actionRef.current?.reload();
      if (code && code !== 0) {
        return;
      }
      const actualMsg = getActualMsg(
        TotalCount,
        ValidCount,
        TooLongCount,
        EmptyEmailCount,
      );
      showModal(actualMsg, TotalCount, ValidCount);
    } catch (error) {
      message.error({ content: t('上传失败，请重试') });
    } finally {
      hide();
    }
  };
  const downloadDetail = useCallback(() => {
    downloadRecipientDetail(requestParams).then((res) => {
      if (res.CosPath) {
        downloadFile(res.CosPath);
        message.success({
          content: t('导出成功'),
        });
      }
    });
  }, [requestParams]);
  const disableOutPut = !detailData.TotalCount;
  return (
    <>
      <Layout className={style['contact-import']}>
        <Body>
          <Content>
            <Content.Header title={t('收件人编辑')} />
            <Content.Body>
              <Alert type="info">
                <H5>{t('注意事项：')}</H5>
                <List type="number">
                  {reminderList.map(({ title, children }, i) => (
                    <List.Item key={i}>
                      {title}
                      {children && (
                        <List type="bullet">
                          {children.map((content, index) => (
                            <List.Item key={index}>{content}</List.Item>
                          ))}
                        </List>
                      )}
                    </List.Item>
                  ))}
                </List>
              </Alert>
              <div className={style['import-buttons']}>
                <Upload
                  accept=".csv"
                  beforeUpload={(file, _, isAccepted) => {
                    handleUpload(file, isAccepted);
                    return false;
                  }}
                >
                  <Button type="primary">{t('导入')}</Button>
                </Upload>
                <Button
                  type="primary"
                  onClick={downloadDetail}
                  disabled={disableOutPut}
                >
                  {t('导出')}
                </Button>
                <Button type="weak" onClick={handleBack}>
                  {t('返回')}
                </Button>
              </div>
              <Table.ActionPanel>
                <Justify
                  left={
                    <ProForm
                      submitter={false}
                      layout="inline"
                      fields={searchFields}
                      onRef={(ref) => (searchFormRef.current = ref)}
                    />
                  }
                  right={
                    <>
                      <Button
                        onClick={() => {
                          setSearchForm({ ...searchFormRef.current.values });
                        }}
                        type="primary"
                      >
                        {t('查询')}
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => setAddVisible(true)}
                      >
                        {t('添加')}
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => {
                          onDeleteConfirm(selectedKeys, true, () => {
                            setSelectedKeys([]);
                          });
                        }}
                        loading={batchDeleteLoading}
                        disabled={canDelete}
                      >
                        {t('批量删除')}
                      </Button>
                      <Button
                        type="weak"
                        onClick={() => {
                          actionRef.current.reload();
                        }}
                      >
                        {t('刷新')}
                      </Button>
                    </>
                  }
                />
                <Trans>
                  总：
                  <Text theme="danger">{{ TotalCount }} </Text>
                  个，有效：
                  <Text theme="danger">{{ ValidCount }} </Text>
                  个，无效：
                  <Text theme="danger">{{ InvalidCount }} </Text>
                  个收件人
                </Trans>
              </Table.ActionPanel>
              <ProTable
                actionRef={actionRef}
                params={requestParams}
                request={getListReceiverDetails as any}
                customLayout
                verticalTop
                columns={importColumns}
                pageable
                recordKey="EmailId"
                pageableOptions={{
                  pageSizeOptions: [10, 20, 30, 50, 100],
                }}
                addons={[
                  {
                    type: 'selectable',
                    value: selectedKeys,
                    onChange: (keys) => {
                      setSelectedKeys(keys);
                    },
                    rowSelect: true,
                  },
                ]}
              />
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      <AddImportModal
        onRetry={actionRef.current?.reload}
        visible={addVisible}
        hasTemplateInput={hasTemplateInput}
        onCancel={() => setAddVisible(false)}
      />
      <EditImportModal
        onRetry={actionRef.current?.reload}
        visible={editVisible}
        valueRef={currentValueRef}
        hasTemplateInput={hasTemplateInput}
        onCancel={() => setEditVisible(false)}
      />
    </>
  );
}
