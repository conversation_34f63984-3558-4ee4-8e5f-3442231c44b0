import { emailRules, variableRules } from '@src/constants/defaultConstant';
import { createCOSInstance, readFileAsync } from '@src/utils/CommonUtils';
import { t } from '@tencent/tea-app/lib/i18n';
import { message } from '@tencent/tea-component';
import { getCOSConf } from './actions';

const FILE_MAX_SIZE = 100 * 1024000;
const MAX_COUNT = 500000;
/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
const importantContent = [
  'A列的 email_address 下方必须填写 email 地址；不能为空和重复，否则上传时会剔除。',
  'The email address must be filled in under email_address in column A; it cannot be empty or repeated, otherwise it will be deleted when uploading.',
  'B、C列第1行的 name，gender 是示例的变量名，可根据您模板中对应的变量名自行修改、增加。变量名仅支持大小写字母（a-z、A-Z）、数字（0-9）和下划线，否则上传时会报错；所有变量值长度不能超过1000个字符，否则上传时会剔除。',
  'The name and gender in the first row of columns B and C are the variable names of the example. They can be modified or added according to the corresponding variable names in your template. Variable names only support uppercase and lowercase letters (a-z, A-Z), numbers (0-9) and underscores, otherwise an error will be reported when uploading; the length of all variable values __cannot exceed 1000 characters, otherwise they will be removed when uploading.',
  '如果您模板中没有变量，请删除B、C列的全部内容（包括 name、gender 也删除），否则上传时会报错；',
  'If there are no variables in your template, please delete all the contents of columns B and C (including name and gender), otherwise an error will be reported when uploading;',
  '上传之前请删除“重要说明”的全部内容，否则上传时会报错。',
  ' Please delete all the contents of "Important Instructions" before uploading, otherwise an error will be reported when uploading.',
];
/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
// 假设这个函数用于检查重要内容是否存在于csv内容中
function hasImportantContent(content: string): boolean {
  return importantContent.some((item) => content.includes(item));
}

// 转换每一行数据为数组
const transRowToArr = (item) => {
  if (item.includes(',')) return item.split(',');
  if (/\t/.test(item)) return item.split(/\t/);
  return [item];
};

// 抽象COS上传文件的函数
export const uploadToCOS = async (
  file: any,
  fileName: string,
): Promise<any> => {
  const { Region, Credentials, BucketName } = await getCOSConf({
    FileName: fileName,
    Type: 0,
  });
  const cos = createCOSInstance(Credentials);
  return new Promise((resolve, reject) => {
    cos.putObject(
      {
        Region,
        Body: file,
        Key: fileName,
        Bucket: BucketName,
      },
      (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      },
    );
  });
};
export const beforeUploadCheck = async (file, isAccepted) => {
  if (!isAccepted) {
    message.warning({
      content: t('仅支持导入.csv格式的文件，请重试。'),
    });
    return false;
  }
  try {
    const fileSize = file.size;
    if (fileSize > FILE_MAX_SIZE) {
      throw new Error(t('导入文件大小超过限制'));
    }

    const content = await readFileAsync(file);
    if (content.includes('�')) {
      throw new Error(t('导入文件存在乱码，请确定使用的是UTF-8编码。'));
    }
    if (hasImportantContent(content)) {
      throw new Error(t('请删除“重要说明”的全部内容。'));
    }

    const rowsData = content.split(/\r\n|\r|\n/).filter((r) => r);
    if (rowsData.length === 0) {
      throw new Error(t('“收件人列表”为空，请检查后重试。'));
    }
    if (rowsData.length > MAX_COUNT) {
      throw new Error(t('导入的收件人数量不能超过50万条。'));
    }
    if (rowsData.length === 1 && !emailRules.test(rowsData[0])) {
      throw new Error(t('“收件人列表”中的变量值存在空值，请检查后重试。'));
    }

    const keysList = transRowToArr(rowsData[0]).map((item) => item.trim());
    const validateKeysList = keysList
      .slice(1) // Skip the first item
      .filter((r) => !variableRules.test(r));
    if (validateKeysList.length > 0) {
      throw new Error(t('“收件人列表”中的变量名填写不正确，请检查后重试。'));
    }

    const keysListSet = new Set(keysList);
    if (keysListSet.size !== keysList.length) {
      throw new Error(t('“收件人列表”中存在相同的变量名，请检查后重试。'));
    }
    return true;
  } catch (error) {
    message.warning({ content: error.message });
    return false;
  }
};
