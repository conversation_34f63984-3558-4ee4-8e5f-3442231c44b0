import React, { useCallback, useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Card, Table, Alert } from '@tencent/tea-component';
import { GetEmailEventStatistic } from '@src/routes/email-event/actions';
import moment from 'moment';
import SearchQuery from '@src/routes/email-event/components/SearchQuery';
import { useForm } from 'react-final-form-hooks';
import { autotip } from '@tea/component/table/addons';

const { pageable } = Table.addons;

const initialQueryValues = {
  timeRange: [moment(), moment()],
  sendDateRange: [moment().add(-29, 'd'), moment()],
};

export default function EmailStatusTable({ eventId = '' }) {
  const [list, setList] = useState([]);
  const [total, setTotal] = useState();
  const [listLoading, setListLoading] = useState(true);
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });
  const queryValues = initialQueryValues;

  useEffect(() => {
    statsQuery(queryValues, pageData);
  }, []);

  function statsQuery(values, pageData) {
    console.log('pageData=', pageData);
    const { fromDomain = '', shortRecipientDomain = '', timeRange = [], sendDateRange } = values;
    const [BeginDate, EndDate] = timeRange.map(one => one.format('YYYY-MM-DD'));
    const [BeginSendDate, EndSendDate] = sendDateRange.map(one => one.format('YYYY-MM-DD'));
    const data = {
      EventId: eventId,
      FromDomain: fromDomain,
      ShortRecipientDomain: shortRecipientDomain,
      BeginDate,
      EndDate,
      BeginSendDate,
      EndSendDate,
    };
    setListLoading(true);
    return GetEmailEventStatistic(data)
      .then((res = {}) => {
        console.log('searchQuery Over', res);
        const { Data = [] } = res;
        setList(Data);
        setTotal(Data.length);
      })
      // .catch(() => { })
      .finally(() => {
        setListLoading(false);
      });
  }

  const onSearchSubmit = useCallback(
    (values) => {
      console.log('onSearchSubmit', values, pageData);
      return statsQuery(values, pageData);
    },
    []
  );

  const formRenderProps = useForm({
    onSubmit: onSearchSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: queryValues,
    validate: ({ timeRange, sendDateRange }: any) => ({
      fromDomain: undefined,
      shortRecipientDomain: undefined,
      timeRange: !timeRange ? t('请选择时间段') : undefined,
      sendDateRange: !sendDateRange ? t('请选择时间段') : undefined,
    }),
  });
  const { handleSubmit } = formRenderProps;

  // 翻页
  const onPagingChange = (data) => {
    setPageData(data);
  };
  useEffect(() => {
    handleSubmit();
  }, []);


  return (
    <div className="black-list-table">
      <Alert hideIcon={false}>
        <h4 style={{ marginBottom: 8 }}>{t('注意事项')}</h4>
        <p>1. {t('下载内容使用CSV格式，使用Excel等工具查看时，可能会存在数字以科学记数等形式显示。')}</p>
        <p>2. {t('下载文件中时间使用毫秒时间戳的形式，可以通过Excel的公式转为可读时间。例：=TEXT(C2/86400000+DATE(1970,1,1), "YYYY-MM-DD HH:mm:ss") 其中C2为时间戳所在的单元格，输出的时间为UTC时间。')}</p>
      </Alert>
      <Card>
        <Card.Body>
          <div className="flex-one-row">
            <SearchQuery formRenderProps={formRenderProps} eventId={eventId} needDownload={!!total} />
          </div>
        </Card.Body>
      </Card>
      <Card>
        <Table
          verticalTop
          records={list}
          // recordKey="EmailAddress"
          columns={[
            {
              key: 'StatDate',
              header: t('发信日期'),
              // render: ({ CreateTime }) => (<span>{moment(Number(CreateTime)).format('YY-MM-DD hh:mm:ss')}</span>),
            },
            {
              key: 'Count',
              header: t('次数'),
            },
          ]}
          addons={[
            pageable({
              recordCount: total,
              pageIndex: pageData.pageIndex,
              pageSize: pageData.pageSize,
              pageSizeOptions: [10, 20, 30, 50, 100],
              onPagingChange: (query) => {
                console.log('onPagingChange', query);
                // {pageIndex: 2, pageSize: 10}
                onPagingChange(query);
              },
            }),
            autotip({
              isLoading: listLoading,
            }),

          ]}
        />
      </Card>
    </div>
  );
}
