import React, { useCallback, useEffect, useState, useRef } from 'react';
import { t } from '@tea/app/i18n';
import { Card, Table, Button, Modal, message } from '@tencent/tea-component';
import {
  GetEmailEventStatistic,
  listUnsubscribeAddresses,
  deleteUnsubscribeAddress,
} from '@src/routes/email-event/actions';
import moment from 'moment';
import SearchQuery from '@src/routes/email-event/components/SearchQuery';
import { useForm } from 'react-final-form-hooks';
import { useTopTip } from '@src/routes/block-list/components/useTopTip';
import ModalBasic from '@src/routes/block-list/components/ModalBasic';

const { pageable } = Table.addons;

const initialQueryValues = {
  timeRange: [moment().add(-7, 'd'), moment()],
  FromDomain: '',
  EmailAddress: '',
};

export default function EmailStatusTable({ eventId = '' }) {
  const [list, setList] = useState([]);
  const [total, setTotal] = useState();
  const [listLoading, setListLoading] = useState(false);
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });
  const queryValues = initialQueryValues;

  useEffect(() => {
    statsQuery(queryValues, pageData);
  }, []);

  function statsQuery(values, pageData) {
    console.log('pageData=', pageData);
    const {
      // shortRecipientDomain = '',
      FromDomain = '',
      EmailAddress = '',
      timeRange = [],
      // sendDateRange,
    } = values;
    const [StartDate, EndDate] = timeRange.map((one) =>
      one.format('YYYY-MM-DD'),
    );
    const data = {
      EventId: eventId,
      StartDate,
      EndDate,
      FromDomain,
      EmailAddress,
      Limit: pageData.pageSize,
      Offset: (pageData.pageIndex - 1) * pageData.pageSize,
    };
    setListLoading(true);
    return listUnsubscribeAddresses(data)
      .then((res = {}) => {
        console.log('searchQuery Over', res);
        const { UnsubscribeAddresses = [], TotalCount } = res;
        setList(UnsubscribeAddresses);
        setTotal(TotalCount);
      })
      .catch(() => {})
      .finally(() => {
        setListLoading(false);
      });
  }

  const onSearchSubmit = useCallback(
    (values) => {
      console.log('onSearchSubmit', values, pageData);
      return statsQuery(values, pageData);
    },
    [pageData],
  );

  const formRenderProps = useForm({
    onSubmit: onSearchSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: queryValues,
    validate: ({ timeRange }: any) => ({
      // shortRecipientDomain: undefined,
      timeRange: !timeRange ? t('请选择时间段') : undefined,
      FromDomain: undefined,
      EmailAddress: undefined,
    }),
  });
  const { handleSubmit } = formRenderProps;
  const [visible, setVisible] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [dataToDeleteForShow, setDataToDeleteForShow] = useState([]);
  const dataToDeleteForShowRef = useRef([]);
  const [dataToDelete, setDataToDelete] = useState([]);
  const dataToDeleteRef = useRef([]);
  const [deleteType, setDeleteType] = useState<'unsubscribe'>('unsubscribe');

  const onDelete = useCallback((data) => {
    console.log('onDelete', data);
    const { EmailAddress, FromDomain } = data;
    setDataToDeleteForShow(EmailAddress);
    dataToDeleteForShowRef.current = EmailAddress;
    console.log('dataToDeleteForShowRef', dataToDeleteForShowRef.current);
    const apiData = [{ Address: EmailAddress, FromDomain }];
    dataToDeleteRef.current = apiData;
    setDataToDelete(apiData);
    setVisible(true);
  }, []);

  const closeModal = () => {
    setVisible(false);
  };

  const onConfirm = useCallback(() => {
    console.log('Confirm action triggered');
    setDeleteLoading(true);
    const currentData = dataToDeleteRef.current;

    console.log(deleteType);
    console.log('self delete currentData', currentData);

    return deleteUnsubscribeAddress(currentData)
      .then((res) => {
        console.log('onDelete Over', res);
        message.success({ content: t('移除成功') });
        return handleSubmit();
      })
      .catch(() => {
        message.error({ content: t('移除失败') });
      })
      .finally(() => {
        setDeleteLoading(false);
        closeModal();
      });
  }, []);

  // 翻页
  const onPagingChange = (data) => {
    setPageData(data);
  };
  useEffect(() => {
    handleSubmit();
  }, []);

  const topTip = useTopTip({
    record: list,
    loading: listLoading,
    size: 'l',
    icon: 'blank',
  });
  return (
    <div className="black-list-table">
      <Card>
        <Card.Body>
          <div className="flex-one-row">
            <SearchQuery
              formRenderProps={formRenderProps}
              eventId={eventId}
              needDownload={!!total}
            />
          </div>
        </Card.Body>
      </Card>
      <Card>
        <Table
          verticalTop
          records={list}
          recordKey="EmailAddress"
          topTip={topTip}
          columns={[
            {
              key: 'EmailAddress',
              header: t('收件邮箱地址'),
            },
            {
              key: 'FromDomain',
              header: t('被退订的发信域名'),
            },
            {
              key: 'UnsubscribeTime',
              header: t('退订时间'),
            },
            {
              key: 'action',
              width: 120,
              header: () => <>{t('操作')}</>,
              // align: "right",
              render: (item) => (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      onDelete(item);
                    }}
                  >
                    {t('移除')}
                  </Button>
                </>
              ),
            },
          ]}
          addons={[
            pageable({
              recordCount: total,
              pageIndex: pageData.pageIndex,
              pageSize: pageData.pageSize,
              pageSizeOptions: [10, 20, 30, 50, 100],
              onPagingChange: (query) => {
                console.log('onPagingChange', query);
                // {pageIndex: 2, pageSize: 10}
                onPagingChange(query);
              },
            }),
          ]}
        />
      </Card>
      <ModalBasic
        visible={visible}
        onConfirm={onConfirm}
        onCancel={closeModal}
        loading={deleteLoading}
        dataToDelete={[dataToDeleteForShowRef.current]}
        deleteType={deleteType}
      />
    </div>
  );
}
