import React, { useCallback, useEffect, useState } from 'react';
import { Button, Form, Justify, Select, message } from '@tea/component';
import { SearchBox } from '@tencent/tea-component';

import { useField, FormRenderProps } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import style from './SearchQuery.module.less';
import { RangePicker } from '@tea/component/datepicker/RangePicker';
import moment from 'moment';
import { fetchDomainList } from '@src/routes/domain/actions';
import { TO_DOMAIN_LIST } from '@src/constants/defaultConstant';
import {
  CreateEmailEventDownloadTask,
  DescribeEmailEventDownloadTask,
} from '@src/routes/email-event/actions';
import { useInterval } from 'react-use';

const DOWNLOAD_STATUS = {
  PROCESSING: 'Processing',
  COMPLETE: 'Completed',
  FAILED: 'Failed',
  EXPIRED: 'Expired',
  QUEUING: 'Queuing',
};

function disabledDate(date, start) {
  // 选择范围在今天之前，且7天
  const isAfterToday = date.isAfter(moment(), 'day');
  if (moment.isMoment(start)) {
    return (
      !isAfterToday &&
      !(
        moment(date).subtract(6, 'day').isAfter(start, 'day') ||
        moment(date).add(0, 'day').isBefore(start, 'day')
      )
    );
  }
  return !isAfterToday;
}
interface Props {
  formRenderProps: FormRenderProps<any>;
  eventId: string;
  needDownload: boolean;
}

const SearchQuery: React.FC<Props> = (props) => {
  const { formRenderProps, eventId, needDownload } = props;
  const { form, handleSubmit, validating, submitting } = formRenderProps;

  // const [domainOptions, setDomainOptions] = useState([
  //   { value: t('加载中...'), disabled: true },
  // ]);
  // useEffect(() => {
  //   fetchDomainList().then((res) => {
  //     const { EmailIdentities = [] } = res || {};
  //     let domains = EmailIdentities.filter(
  //       ({ SendingEnabled }) => SendingEnabled,
  //     ).map((one: any) => {
  //       const { IdentityName } = one;
  //       return { value: IdentityName, text: IdentityName };
  //     });
  //     domains = [{ value: '', text: t('所有') }, ...domains];
  //     setDomainOptions(domains);
  //   });
  // }, []);
  // const fromDomain = useField('fromDomain', form);
  const timeRange = useField('timeRange', form);
  const FromDomain = useField('FromDomain', form);
  const EmailAddress = useField('EmailAddress', form);
  // const sendDateRange = useField('sendDateRange', form);
  // const shortRecipientDomain = useField('shortRecipientDomain', form);

  // function disabledDate(date, start) {
  //   // 选择范围在今天之前，且选择跨度不大于x天
  //   const x = 0;
  //   const isAfterToday = date.isAfter(moment(), 'day');
  //   if (moment.isMoment(start)) {
  //     return (
  //       !isAfterToday
  //       && !(
  //         moment(date)
  //           .subtract(x, 'day')
  //           .isAfter(start, 'day')
  //         || moment(date)
  //           .add(x, 'day')
  //           .isBefore(start, 'day')
  //       )
  //     );
  //   }
  //   return !isAfterToday;
  // }

  // const [downloading, setDownloading] = useState(false);
  // const [downloadTask, setDownloadTask] = useState<any>();
  // // 获取下载地址
  // const getDownloadPath = useCallback(() => {
  //   const { TaskId, Date } = downloadTask;
  //   DescribeEmailEventDownloadTask({ TaskId, Date })
  //     .then((res) => {
  //       const { Status, CosPath } = res;
  //       if (Status === DOWNLOAD_STATUS.COMPLETE) {
  //         // console.log('CosPath--', CosPath);
  //         const link = document.createElement('a');
  //         link.style.display = 'none';
  //         link.href = CosPath;
  //         document.body.appendChild(link);
  //         link.click();
  //         document.body.removeChild(link);
  //       }
  //       if (
  //         Status !== DOWNLOAD_STATUS.PROCESSING &&
  //         Status !== DOWNLOAD_STATUS.QUEUING
  //       ) {
  //         setDownloading(false);
  //       }
  //     })
  //     .catch(() => setDownloading(false));
  // }, [downloadTask]);
  // useInterval(
  //   () => {
  //     // 循环查询下载地址是否准备好
  //     getDownloadPath();
  //   },
  //   downloading ? 5000 : null,
  // );
  // // 处理点击下载
  // const onClickDownload = () => {
  //   const { values } = form.getState();
  //   const {
  //     fromDomain = '',
  //     shortRecipientDomain = '',
  //     timeRange = [],
  //     sendDateRange,
  //   } = values;
  //   const [BeginDate, EndDate] = timeRange.map((one) =>
  //     one.format('YYYY-MM-DD'),
  //   );
  //   const [BeginSendDate, EndSendDate] = sendDateRange.map((one) =>
  //     one.format('YYYY-MM-DD'),
  //   );
  //   const data = {
  //     EventId: eventId,
  //     FromDomain: fromDomain,
  //     ShortRecipientDomain: shortRecipientDomain,
  //     BeginDate,
  //     EndDate,
  //     BeginSendDate,
  //     EndSendDate,
  //   };
  //   // console.log('data--', data);
  //   CreateEmailEventDownloadTask(data).then((res) => {
  //     message.success({ content: t('下载中，请耐心等待'), duration: 5000 });
  //     setDownloading(true);
  //     const { TaskId, Date } = res;
  //     setDownloadTask({ TaskId, Date });
  //   });
  // };

  return (
    <form onSubmit={handleSubmit} className={style['stats-search-query']}>
      {/* <Table.ActionPanel>*/}
      {/* <Justify
        bottom
        left={
          <>
            <Form layout="inline">
              <Form.Item label={t('发信域名')}>
                <Select
                  className={style['select-field']}
                  {...fromDomain.input}
                  type="simulate"
                  appearance="button"
                  options={domainOptions}
                  // value={favorite}
                  // onChange={value => setFavorite(value)}
                  placeholder={t('请选择域名')}
                />
              </Form.Item>
              <Form.Item label={t('收信域名')}>
                <Select
                  className={style['select-field']}
                  type="simulate"
                  appearance="button"
                  {...shortRecipientDomain.input}
                  options={[{ value: '', text: t('所有') }, ...TO_DOMAIN_LIST]}
                  placeholder={t('请选择域名')}
                />
              </Form.Item>
              <Form.Item label={t('日期范围')}>
                <RangePicker
                  {...timeRange.input}
                  range={[
                    moment().subtract(29, 'd').startOf('d'),
                    moment().endOf('d'),
                  ]}
                  disabledDate={disabledDate}
                />
              </Form.Item>
              <Form.Item label={t('邮件发送时间')}>
                <RangePicker
                  {...sendDateRange.input}
                  range={[
                    moment().subtract(29, 'd').startOf('d'),
                    moment().endOf('d'),
                  ]}
                  // disabledDate={disabledDate}
                />
              </Form.Item>
            </Form>
          </>
        }
        right={
          <div className={style['right-search-btn-field']}>
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              disabled={validating}
            >
              {t('查询')}
            </Button>
            <Button
              htmlType="button"
              onClick={onClickDownload}
              loading={downloading}
              disabled={!needDownload}
            >
              {t('下载')}
            </Button>
          </div>
        }
      /> */}
      <>
        <Form layout="inline">
          <Form.Item label={t('日期范围')}>
            <RangePicker
              {...timeRange.input}
              range={[
                moment().subtract(29, 'd').startOf('d'),
                moment().endOf('d'),
              ]}
              disabledDate={disabledDate}
            />
          </Form.Item>
          <Form.Item label={t('收件邮箱地址')} style={{ width: '380px' }}>
            <SearchBox
              hideButton={false}
              size="l"
              style={{ width: '300px' }}
              {...EmailAddress.input}
              placeholder={t('<EMAIL>')}
              onSearch={(value) => {
                handleSubmit();
              }}
            />
          </Form.Item>
          <Form.Item label={t('被退订的发信域名')} style={{ width: '400px' }}>
            <SearchBox
              hideButton={false}
              size="l"
              style={{ width: '300px' }}
              {...FromDomain.input}
              placeholder={t('<EMAIL>')}
              onSearch={(value) => {
                console.log('value', value);
                handleSubmit();
              }}
            />
          </Form.Item>
        </Form>
      </>
    </form>
  );
};

export default SearchQuery;
