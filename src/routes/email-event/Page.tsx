import React from 'react';
import { t } from '@tea/app/i18n';
import { Layout } from '@tencent/tea-component';
import { TabPanel, Tabs } from '@tea/component';
import EmailStatusTable from '@src/routes/email-event/components/Table';
import './style.less';
import { STATISTIC_PATH } from '@src/constants/defaultConstant';
import _ from 'lodash';
import RegionSelect from '@src/components/RegionSelect/Page';

const { Body, Content } = Layout;

export function EmailEventPage() {
  const tabs = [
    { id: 'open', label: t('打开') },
    { id: 'click', label: t('点击') },
  ];
  const { pathname } = window.location;
  // console.log('pathname--', pathname);
  const path = _.find(STATISTIC_PATH, o => o.pathname === pathname);
  const isOpenClick = path?.pathname === '/ses/open-click';
  if (!path) return null;
  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={path.pageText} subtitle={<RegionSelect />} />
            {!isOpenClick && (
              <Content.Body>
                <EmailStatusTable eventId={path.value} />
              </Content.Body>
            )}
            {isOpenClick && (
              <Content.Body>
                <Tabs ceiling animated={false} tabs={tabs}>
                  <TabPanel id="open">
                    <EmailStatusTable eventId={path.openValue} />
                  </TabPanel>
                  <TabPanel id="click">
                    <EmailStatusTable eventId={path.clickValue} />
                  </TabPanel>
                </Tabs>
              </Content.Body>
            )}
          </Content>
        </Body>
      </Layout>
    </>
  );
}
