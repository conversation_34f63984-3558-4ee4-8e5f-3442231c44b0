import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';

export function GetEmailEventStatistic(data: any) {
  console.log('searchQuery start data=', data);
  const cmd = SES_CMD.GET_EMAIL_EVENT_STATISTIC;
  return requestApiV3(cmd, { ...data }).then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      handleRequestError(e);
    });
}

export function CreateEmailEventDownloadTask(data: any) {
  const cmd = SES_CMD.CREATE_EMAIL_EVENT_DOWNLOAD_TESK;
  return requestApiV3(cmd, { ...data }).then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function DescribeEmailEventDownloadTask(data: any) {
  const cmd = SES_CMD.DES_EMAIL_EVENT_DOWNLOAD_TESK;
  return requestApiV3(cmd, { ...data }).then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function listUnsubscribeAddresses(data: any) {
  console.log('LIST_UNSUBSCRIBE_ADDRESSES start data=', data);
  const cmd = SES_CMD.LIST_UNSUBSCRIBE_ADDRESSES;
  const {
    StartDate,
    EndDate,
    EmailAddress,
    Offset = 0,
    Limit = 100,
    FromDomain,
  } = data;
  const postData = {
    StartDate,
    EndDate,
    EmailAddress: EmailAddress.trim(),
    Offset,
    Limit,
    FromDomain: FromDomain.trim(),
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`LIST_UNSUBSCRIBE_ADDRESSES request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function deleteUnsubscribeAddress(data: any) {
  console.log('DeleteUnsubscribeAddress start data=', data);
  const postData = {
    UnsubscribeData: data,
  };
  const cmd = SES_CMD.DELETE_UNSUBSCRIBE_ADDRESS;

  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}
