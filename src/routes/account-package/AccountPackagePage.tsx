import React, { useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Card, Layout, Table } from '@tencent/tea-component';
import { Bubble, Icon, StatusTip } from '@tea/component';
import { accountPackageQuery } from '@src/routes/account-package/actions';
import './style.less';
import RegionSelect from '@src/components/RegionSelect/Page';

const { Body, Content } = Layout;
const { pageable } = Table.addons;

export function AccountPackagePage() {
  const [list, setList] = useState([]);
  const [listLoading, setListLoading] = useState(true);

  useEffect(() => {
    queryPackage();
  }, []);

  function queryPackage() {
    setListLoading(true);
    return accountPackageQuery({})
      .then((res = {}) => {
        console.log('accountPackageQuery Over', res);
        const { AccountPackages = [] } = res;
        setList(AccountPackages);
      })
      .finally(() => {
        setListLoading(false);
      });
  }

  const statusNameMap = {
    0: t('正常'),
    1: t('隔离中'),
    2: t('已隔离'),
    3: t('已销毁'),
    4: t('已过期'),
  };

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header
              title={t('套餐包管理')}
              subtitle={<RegionSelect />}
            />
            <Content.Body className="account-package-page">
              <Card>
                <Table
                  verticalTop
                  records={list}
                  recordKey="PackageID"
                  // rowDisabled={record => record.status === "start"}
                  // rowClassName={record => record.status}
                  topTip={
                    listLoading && (
                      <StatusTip
                        status={listLoading ? 'loading' : 'found'}
                        // onClear={() => setStatus("loading")}
                        // onRetry={() => setStatus("loading")}
                      />
                    )
                  }
                  columns={[
                    {
                      key: 'PackageID',
                      width: 250,
                      header: t('套餐包ID'),
                      render: (item) => {
                        const id = item.PackageID;
                        return <Bubble content={id}>{id}</Bubble>;
                      },
                    },
                    {
                      key: 'PackageQuota',
                      header: () => (
                        <>
                          {t('额度（封）')}
                          <Bubble content={t('套餐包内包含多少封邮件')}>
                            <Icon type="info" />
                          </Bubble>
                        </>
                      ),
                    },
                    {
                      key: 'AvailableQuota',
                      header: t('剩余额度（封）'),
                    },
                    {
                      key: 'UsedQuota',
                      // header: t("打开数"),
                      header: () => (
                        <>
                          {t('已用额度（封）')}
                          {/* <Bubble content={t('打开邮件的用户数量，根据收件人去重')}>*/}
                          {/*  <Icon type="info" />*/}
                          {/* </Bubble>*/}
                        </>
                      ),
                    },
                    {
                      key: 'PackageStatus',
                      header: t('状态'),
                      render: (item) => (
                        <div
                          className={`package-status package-status-${item.PackageStatus}`}
                        >
                          {statusNameMap[item.PackageStatus]}
                        </div>
                      ),
                    },
                    {
                      key: 'CreateTime',
                      header: t('创建时间'),
                    },
                    {
                      key: 'ExpireTime',
                      header: t('过期时间'),
                    },
                    // {
                    //   key: "LastUpdateTime",
                    //   header: t('最后更新时间'),
                    // },
                  ]}
                  addons={[pageable()]}
                />
              </Card>
              {/* <Card>*/}
              {/*  <Card.Body>*/}
              {/*    {JSON.stringify(data)}*/}
              {/*  </Card.Body>*/}
              {/* </Card>*/}
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
}
