import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';

export function accountPackageQuery(data: any) {
  console.log('accountPackageQuery start data=', data);
  const cmd = SES_CMD.ACCOUNT_PACKAGE;
  const postData = {};
  return requestApiV3(cmd, postData).then((res: any) => {
    console.log(`request ${cmd}, res=`, res);
    return res;
  })
    .catch((e) => {
      handleRequestError(e);
    });
}
