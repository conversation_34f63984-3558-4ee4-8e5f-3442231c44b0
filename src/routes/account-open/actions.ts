import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { OTHER_CMD, SES_CMD } from '@src/api/constants';
import {
  GetAccountCountryResponse,
  GetSecuritySurveyAnswersResponse,
} from './types';

export function openAccount({
  AuthType,
  ResourceType = 0,
}: {
  AuthType: number;
  ResourceType?: 0 | 1;
}) {
  console.log('openAccount start data=');
  const cmd = SES_CMD.OPEN_ACCOUNT;
  const postData = { AuthType, ResourceType };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

// 调其他服务的接口获取用户的企业/个人
export function getRealAccountInfo() {
  console.log('getRealAccountInfo start data=');
  const cmd = OTHER_CMD.DescribeRealNameAuthInfo;
  const postData = {
    Version: '2018-12-25',
  };
  return requestApiV3(cmd, postData, {
    // regionId: 5,
    serviceType: 'account',
  })
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      handleRequestError(e);
      // throw e;
    });
}

// 获取账户国家信息
export function getAccountCountry(): Promise<GetAccountCountryResponse> {
  console.log('getAccountCountry start');
  const cmd = SES_CMD.GET_ACCOUNT_COUNTRY;
  return requestApiV3(cmd, {})
    .then((res: GetAccountCountryResponse) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => handleRequestError(e));
}

// 获取安全调查问卷答案
export function getSecuritySurveyAnswers(): Promise<GetSecuritySurveyAnswersResponse> {
  console.log('getSecuritySurveyAnswers start');
  const cmd = SES_CMD.GET_SECURITY_SURVEY_ANSWERS;
  return requestApiV3(cmd, {})
    .then((res: GetSecuritySurveyAnswersResponse) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => handleRequestError(e));
}

// 保存安全调查问卷答案
export function saveSecuritySurveyAnswers(answers: boolean[]) {
  console.log('saveSecuritySurveyAnswers start, answers=', answers);
  const cmd = SES_CMD.SAVE_SECURITY_SURVEY_ANSWERS;
  return requestApiV3(cmd, { Answers: answers })
    .then((res) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => handleRequestError(e));
}
