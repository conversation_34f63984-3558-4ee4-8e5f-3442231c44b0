import React, { useCallback, useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Button, Layout, Status } from '@tencent/tea-component';
import { Card, message } from '@tea/component';
import './style.less';
import {
  getRealAccountInfo,
  openAccount,
  getAccountCountry,
  getSecuritySurveyAnswers,
  saveSecuritySurveyAnswers,
} from '@src/routes/account-open/actions';
import { shouldShowPreCheck } from '@src/routes/account-open/types';
import OpenAccountForm from '@src/routes/account-open/components/OpenAccountForm/OpenAccountForm';
// import logo from "/assets/images/ses-logo.png"
import RegionSelect from '@src/components/RegionSelect/Page';
import { isIntl } from '@src/utils/CommonUtils';

import logo from '../../assets/images/ses-logo.png';
import PreOpenCheck from './components/PreOpenCheck/PreOpenCheck';

const { Body, Content } = Layout;
const ContentWrapper = ({ children }) => (
  <Layout className="">
    <Body>
      <Content>
        <Content.Header title={t('开通服务')} subtitle={<RegionSelect />} />
        <Content.Body>{children}</Content.Body>
      </Content>
    </Body>
  </Layout>
);

export const AccountOpenPage = () => {
  // const recordList = useMemo(() => fakeList, []);
  const [list, setList] = useState({
    AuthType: 2,
  });
  const [listLoading, setListLoading] = useState(true);
  const [accountLoading, setAccountLoading] = useState(true);
  const [showPreCheck, setShowPreCheck] = useState(false);
  const fetchList = useCallback(() => {
    setListLoading(true);
    getRealAccountInfo()
      .then((res) => {
        console.log('fetchList', res);
        // AuthArea: 0
        // AuthMethod: 1
        // AuthName: "**飞"
        // AuthStatus: 3
        // AuthType: 0
        // IDCard: "4****************2"

        // AuthType 0个人 1企业
        if (res?.AuthType) {
          setList(res);
        }
      })
      .finally(() => {
        setListLoading(false);
      });
  }, []);

  // 检查账户状态和国家信息
  const checkAccountStatus = useCallback(async () => {
    setAccountLoading(true);
    try {
      // 国内站不需要显示 PreOpenCheck
      if (!isIntl) {
        return;
      }
      const countryRes = await getAccountCountry();
      const countryCode = countryRes?.CountryData?.Country || '';
      if (shouldShowPreCheck(countryCode)) {
        // 加载已保存的答案
        const answersRes = await getSecuritySurveyAnswers();
        if (answersRes.Answers.length) {
          const [answer1, answer2] = answersRes.Answers;
          const hasCompletedCheck =
            !answer1 || // 路径1: 第一题选否 [false]
            (answer1 && answer2); // 路径2: 第一题选是，第二题选是 [true, true]
          if (hasCompletedCheck) {
            return;
          }
        }
        setShowPreCheck(true);
      }
    } finally {
      setAccountLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchList();
    checkAccountStatus();
  }, [fetchList, checkAccountStatus]);

  const handlePreCheckComplete = () => {
    setShowPreCheck(false);
  };

  const onCreateSubmit = useCallback(
    (values) => {
      console.log('onCreateSubmit', values, list);
      const { AuthType = 2 } = list;
      return openAccount({
        AuthType,
      })
        .then((res) => {
          console.log('onCreateSubmit Over', res);
          message.success({ content: t('开通成功') });
          // fetchList();
          // history.push('/ses');
          window.location.href = '/ses';
        })
        .catch((e) => {
          console.log('open account catch', e);
          const { code } = e || {};
          if (code === 'InvalidParameterValue.RepeatOpenAccount') {
            window.location.href = '/ses';
          }
        });
    },
    [list],
  );

  // const onDeleteConfirm = async (item, idx) => {
  //   const yes = await Modal.confirm({
  //     message: t("确认删除当前所选模板？"),
  //     description: t("删除后，不能再用该模板发送邮件。"),
  //     okText: t("删除"),
  //     cancelText: t("取消"),
  //   });
  //   if (yes) {
  //     return deleteTask(item.TaskID).then(() => {
  //       fetchList();
  //     }).finally(() => {
  //     })
  //   }
  //   // setAnswer(yes ? "已删除" : "未删除");
  // };
  if (accountLoading) {
    return (
      <ContentWrapper>
        <Card>
          <Status icon="loading"></Status>
        </Card>
      </ContentWrapper>
    );
  }

  // 如果需要显示PreOpenCheck，则显示该组件
  if (showPreCheck) {
    return (
      <ContentWrapper>
        <PreOpenCheck
          onComplete={handlePreCheckComplete}
          className="account-open-page"
        />
      </ContentWrapper>
    );
  }

  return (
    <>
      <Layout className="">
        <Body>
          <Content>
            <Content.Header title={t('开通服务')} subtitle={<RegionSelect />} />
            <Content.Body>
              {/* 内容区域一般使用 Card 组件显示内容 */}
              <Card className="account-open-page">
                <Card.Body>
                  <div className="my-flex-box">
                    <div className="my-flex-logo">
                      <img src={logo} alt=""></img>
                    </div>
                    <div className="my-flex-content">
                      <OpenAccountForm
                        className="send-form"
                        onSubmit={onCreateSubmit}
                        disabled={listLoading}
                      />
                    </div>
                  </div>
                </Card.Body>
              </Card>
              {/* <Table.ActionPanel>*/}
              {/*  <Justify*/}
              {/*    left={*/}
              {/*      <>*/}
              {/*        <Button type="primary" onClick={() => {*/}
              {/*          setCreateModalVisible(true);*/}
              {/*        }}>{t("新建发送任务")}</Button>*/}
              {/*        /!*<Button>刷新</Button>*!/*/}
              {/*      </>*/}
              {/*    }*/}
              {/*    // right={*/}
              {/*    //   <>*/}
              {/*    //     <SearchBox />*/}
              {/*    //     /!*<Button icon="setting" />*!/*/}
              {/*    //     /!*<Button icon="refresh" />*!/*/}
              {/*    //     /!*<Button icon="download" />*!/*/}
              {/*    //   </>*/}
              {/*    // }*/}
              {/*  />*/}
              {/* </Table.ActionPanel>*/}
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      {/* <NewTemplateModal*/}
      {/*  visible={newModalVisible}*/}
      {/*  onCancel={() => {*/}
      {/*    setCreateModalVisible(false);*/}
      {/*  }}*/}
      {/*  onSubmit={onCreateSubmit}*/}
      {/* />*/}
    </>
  );
};
