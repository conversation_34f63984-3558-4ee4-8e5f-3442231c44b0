import React, { useState } from 'react';
import {
  Button,
  Card,
  Form,
  Radio,
  ExternalLink,
  Text,
  message,
} from '@tencent/tea-component';
import './style.less';
import { t, Trans } from '@tea/app/i18n';
import { saveSecuritySurveyAnswers } from '../../actions';

interface Props {
  onComplete: () => void;
  onBack?: () => void;
  className?: string;
  initialAnswers?: boolean[]; // 初始答案数组
}

enum StepType {
  STEP_1 = 'step1',
  STEP_2A = 'step2a',
  STEP_2B = 'step2b',
  INFO_PAGE = 'info',
  BLOCKED = 'blocked',
}

const PreOpenCheck: React.FC<Props> = ({ onComplete, onBack, className }) => {
  const [currentStep, setCurrentStep] = useState<StepType>(StepType.STEP_1);

  const [answers, setAnswers] = useState<boolean[]>([]);

  const handleStep1Submit = async () => {
    if (!answers[0]) {
      // 第一个问题回答 false (不涉及受限制数据)
      setCurrentStep(StepType.STEP_2A);
    } else {
      // 第一个问题回答 true (涉及受限制数据)
      setCurrentStep(StepType.STEP_2B);
    }
  };

  const handleStep2Submit = async () => {
    if (answers[1]) {
      // 第二个问题回答 true (能够遵守要求)
      setCurrentStep(StepType.STEP_2A);
    } else {
      // 第二个问题回答 false (无法遵守要求)
      setCurrentStep(StepType.BLOCKED);
    }
  };

  const handleInfoPageClose = () => {
    saveSecuritySurveyAnswers(answers);
    onComplete?.();
  };

  const renderStep1 = () => (
    <div className="pre-open-check-step">
      <div className="step-content">
        <p>
          <Trans>
            为确保符合美国不断更新的监管标准，请确认您当前及预期通过腾讯云服务存储或处理的数据性质。更多信息请参阅
            <ExternalLink href={`//${window.QCMAIN_HOST}/render/fdre`}>
              链接
            </ExternalLink>
            。
          </Trans>
        </p>

        <p style={{ marginTop: 16, marginBottom: 16 }}>
          {t('请回答以下问题：')}
        </p>

        <p>
          <Trans>
            贵方是否会提交、上传、传输、展示或指示腾讯云及所选腾讯云服务处理美国敏感个人数据及/或美国政府相关数据（定义见美国司法部《数据安全计划》（《联邦法规》第28章第202部分））（以下简称"受限制数据"）？若涉及此类数据，贵方使用所选腾讯云服务时必须配置为符合美国网络安全和基础设施安全局（CISA）数据级及系统与组织安全要求（详见
            <ExternalLink href="https://www.cisa.gov/sites/default/files/2025-01/Security_Requirements_for_Restricted_Transaction-EO_14117_Implementation508.pdf">
              此处
            </ExternalLink>
            ）。
          </Trans>
        </p>
      </div>

      <Form layout="fixed" style={{ marginTop: 24 }}>
        <Radio.Group
          value={
            answers[0] === true
              ? 'yes'
              : answers[0] === false
              ? 'no'
              : undefined
          }
          onChange={(value) => {
            const newAnswers = [...answers];
            newAnswers[0] = value === 'yes';
            setAnswers(newAnswers);
          }}
        >
          <Radio name="yes" style={{ display: 'block', marginBottom: 12 }}>
            {t(
              '是，我方预计存储或处理的数据可能包含受限制数据，且尚未完全符合CISA安全要求。',
            )}
          </Radio>
          <Radio name="no" style={{ display: 'block', marginBottom: 12 }}>
            {t('我方使用场景不涉及受限制数据，或已符合CISA安全要求。')}
          </Radio>
        </Radio.Group>

        <div className="button-group center">
          <Button
            type="primary"
            onClick={handleStep1Submit}
            disabled={answers.length === 0}
          >
            {t('提交')}
          </Button>
        </div>
      </Form>
    </div>
  );

  const renderStep2A = () => (
    <div className="pre-open-check-step">
      <div className="step-content">
        <p>{t('感谢您提供的信息。我们已将此记录在案。')}</p>

        <p style={{ marginTop: 16 }}>
          <Trans>
            若贵方后续出现以下情形： <br />
            (a) 发现使用腾讯云服务涉及存储或处理受限制数据；
            <br />
            (b) 拟扩展或变更腾讯云服务用途以存储或处理受限制数据；和/或
            <br />
            (c) 受限制数据的存储不符合适用的CISA安全要求，
            <Text theme="strong" style={{ fontWeight: 'bold' }}>
              请立即通知我方
            </Text>
            ，以便双方可共同或分别评估并履行《数据安全计划》合规义务。
          </Trans>
        </p>
      </div>

      <div className="button-group center">
        <Button type="primary" onClick={handleInfoPageClose}>
          {t('关闭')}
        </Button>
      </div>
    </div>
  );

  const renderStep2B = () => (
    <div className="pre-open-check-step">
      <div className="step-content">
        <p>
          {t(
            '感谢您的回复。若贵方数据包含受限制数据（贵方应最为了解），贵方是否能够调整腾讯云使用方式以避免使用或存储任何受限制数据，和/或贵方是否已实施并是否能够遵守与腾讯云使用相关的CISA安全要求？',
          )}
        </p>
      </div>

      <Form layout="fixed" style={{ marginTop: 24 }}>
        <Radio.Group
          value={
            answers[1] === true
              ? 'yes'
              : answers[1] === false
              ? 'no'
              : undefined
          }
          onChange={(value) => {
            const newAnswers = [...answers];
            newAnswers[1] = value === 'yes';
            setAnswers(newAnswers);
          }}
        >
          <Radio name="yes" style={{ display: 'block', marginBottom: 12 }}>
            <Trans>
              是，我方承诺：
              <br />
              (a)
              不提交、上传、传输、展示或指示腾讯云及所选腾讯云服务处理任何受限制数据；和/或
              <br />
              (b) 确认将即刻实施并遵守与腾讯云使用相关的CISA安全要求。
            </Trans>
          </Radio>
          <Radio name="no" style={{ display: 'block', marginBottom: 12 }}>
            {t('否，我方必须上传受限制数据且预期无法遵守CISA安全要求。')}
          </Radio>
        </Radio.Group>

        <div className="button-group center">
          <Button onClick={() => setCurrentStep(StepType.STEP_1)}>
            {t('返回')}
          </Button>
          <Button
            type="primary"
            onClick={handleStep2Submit}
            disabled={answers.length < 2}
          >
            {t('提交')}
          </Button>
        </div>
      </Form>
    </div>
  );

  const renderInfoPage = () => (
    <div className="pre-open-check-step">
      <div className="step-content">
        <p>
          {t(
            '此步骤旨在帮助我方了解贵方的预期使用行为是否可能涉及受限制数据，即：',
          )}
        </p>

        <ul style={{ marginTop: 16, marginBottom: 16 }}>
          <li>
            {t(
              '与美国个人健康、基因、地理位置、财务或类似敏感类别相关的受监管数据集；或',
            )}
          </li>
          <li>
            <Trans>
              根据美国司法部最新法规（《联邦法规》第28章第202部分，2025年4月生效）可能被认定为"美国政府相关数据"或"美国敏感个人数据"的内容——详见情况说明：
              <ExternalLink href="https://www.justice.gov/archives/opa/media/1382526/dl">
                此处
              </ExternalLink>
              。
            </Trans>
          </li>
        </ul>

        <h4>{t('重要性说明：')}</h4>
        <p>
          {t(
            '美国政府已颁布《数据安全计划》，旨在规范包括中国在内的特定受关注国家获取某些受限制数据的行为。因此，对此类数据的存储、传输或访问方式可能适用额外的安全或其他要求——特别是当涉及具有国际关联的基础设施处理时。我们的目标是通过预先了解贵方需求，协助贵方实现合规要求。',
          )}
        </p>

        <p style={{ marginTop: 16 }}>
          <Trans>
            如需了解该法规（《联邦法规》第28章第202部分）详情，请查阅最终规则全文：
            <ExternalLink href="https://www.justice.gov/nsd/media/1382521/dl">
              此处
            </ExternalLink>
            。
          </Trans>
        </p>

        <p style={{ marginTop: 16 }}>
          <Trans>
            针对法规中定义的"受限交易"类别，美国网络安全和基础设施安全局（CISA）制定了安全要求，以降低通过受限交易向受关注国家或受限制主体共享美国敏感个人数据或美国政府相关数据的风险。请查阅CISA安全要求：
            <ExternalLink href="https://www.cisa.gov/sites/default/files/2025-01/Security_Requirements_for_Restricted_Transaction-EO_14117_Implementation508.pdf">
              此处
            </ExternalLink>
            。
          </Trans>
        </p>
      </div>

      <div className="button-group center">
        <Button onClick={() => setCurrentStep(StepType.STEP_1)}>
          {t('返回')}
        </Button>
        <div></div>
      </div>
    </div>
  );

  const renderBlocked = () => (
    <div className="pre-open-check-step">
      <div className="step-content">
        <Trans>
          <p>
            感谢您的回复。我们目前
            <Text style={{ fontWeight: 'bold' }}>无法同意</Text>
            向贵方提供所选的腾讯云服务。
          </p>
        </Trans>

        <p style={{ marginTop: 16 }}>
          {t(
            '我们随时准备支持贵方对可能涉及处理受限制数据的服务使用计划进行必要调整。贵方需重点审查现有架构和支持途径，审视调整数据存储位置或调整支持层级的方案，或修改合同计划下的服务模式以符合新的《数据安全计划》框架要求。',
          )}
        </p>

        <p style={{ marginTop: 16 }}>
          {t('我们致力于协助新客户规划最佳实施方案。')}
        </p>

        <p style={{ marginTop: 16 }}>
          {t('如需就该产品方案进行商讨，请联系我们。')}
        </p>
      </div>

      <div className="button-group center">
        <Button
          type="primary"
          onClick={() => {
            window.open(
              `//${window.QCCONSOLE_HOST}/workorder/category`,
              '_blank',
            );
          }}
        >
          {t('联系腾讯')}
        </Button>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case StepType.STEP_1:
        return renderStep1();
      case StepType.STEP_2A:
        return renderStep2A();
      case StepType.STEP_2B:
        return renderStep2B();
      case StepType.INFO_PAGE:
        return renderInfoPage();
      case StepType.BLOCKED:
        return renderBlocked();
      default:
        return renderStep1();
    }
  };

  return (
    <div className={`pre-open-check ${className || ''}`}>
      <Card>
        <Card.Body>
          <div className="pre-open-check-container">{renderCurrentStep()}</div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default PreOpenCheck;
