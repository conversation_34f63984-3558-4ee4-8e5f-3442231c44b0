.pre-open-check {
  .pre-open-check-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
  }

  .pre-open-check-step {
    display: flex;
    flex-direction: column;

    .step-content {
      line-height: 1.6;

      p {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      ul {
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          line-height: 1.6;
        }
      }

      h4 {
        margin: 20px 0 12px 0;
        font-weight: 600;
        color: #333;
      }
    }
  }

  .tea-radio {
    align-items: flex-start;

    .tea-radio__label {
      line-height: 1.6;
      margin-top: 2px;
    }
  }

  .button-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;

    &.center {
      justify-content: center;
    }

    .tea-btn+.tea-btn {
      margin-left: 16px;
    }
  }
}