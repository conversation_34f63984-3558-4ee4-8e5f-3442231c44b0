import React, { useState } from 'react';
import { Button, Checkbox, ExternalLink, Form, Text } from '@tea/component';
import { useForm } from 'react-final-form-hooks';
import { t, Trans } from '@tea/app/i18n';
import './style.less';
import { getLinkUrl, LINK_URL_NAME } from '@src/constants/urlConfig';
import { isMainLand } from '@src/utils/CommonUtils';

interface Props {
  values?: any;
  loading?: boolean;
  disabled?: boolean;
  onSubmit: (values: any) => void;
  className?: string;
}

const OpenAccountForm: React.FC<Props> = (props) => {
  const { onSubmit, className, loading, disabled } = props;

  const [checked, setChecked] = useState(isMainLand ? [] : ['agree']);

  const { handleSubmit, validating, submitting } = useForm({
    onSubmit,
    initialValuesEqual: () => true,
    initialValues: { name: '' },
    validate: () => ({}),
  });

  const payDoc = getLinkUrl(LINK_URL_NAME.payDoc);

  return (
    <form onSubmit={handleSubmit} className={`open-account-form ${className}`}>
      <Form layout="fixed">
        <Trans>
          <p>欢迎使用邮件推送SES！</p>
          <p>
            在开始使用前，请认真阅读该产品的
            <a href={payDoc} target="_blank" rel="noreferrer">
              计费规则
            </a>
          </p>
          <p>点击确认开通，即可开始使用我们的产品。</p>
        </Trans>
      </Form>
      <div className="send-submit-action">
        {isMainLand ? (
          <div style={{ paddingTop: '6px' }}>
            <Checkbox.Group
              value={checked}
              onChange={(value) => setChecked(value)}
            >
              <Checkbox name="agree">
                <div>
                  <Text>
                    {
                      // eslint-disable-next-line @tencent/tea-i18n/no-bare-zh-in-js
                      '我已阅读并同意'
                    }
                  </Text>
                  <Text>
                    <ExternalLink
                      href={`//${window.QCMAIN_HOST}/document/product/1288/67367`}
                    >
                      {
                        // eslint-disable-next-line @tencent/tea-i18n/no-bare-zh-in-js
                        '邮件推送服务协议'
                      }
                    </ExternalLink>
                  </Text>
                </div>
              </Checkbox>
            </Checkbox.Group>
          </div>
        ) : null}
        <div className="send-submit-btns">
          <Button
            className="send-submit-btn"
            type="primary"
            htmlType="submit"
            loading={submitting || loading}
            disabled={validating || disabled || !checked.length}
          >
            {t('确认开通')}
          </Button>
        </div>
      </div>
    </form>
  );
};

export default OpenAccountForm;
