// 账户国家信息接口类型定义
export interface CountryData {
  Country: string; // 国家标识
  CountryName: string; // 国家名称
}

export interface GetAccountCountryResponse {
  CountryData: CountryData;
}

// 安全调查问卷答案接口类型定义
export interface GetSecuritySurveyAnswersResponse {
  Answers: boolean[]; // 问卷答复数组
}

export interface SaveSecuritySurveyAnswersRequest {
  Answers: boolean[]; // 问卷答复数组
}

// 工具函数：判断是否需要进行 precheck
export function shouldShowPreCheck(countryCode: string): boolean {
  const countriesRequiringPreCheck = ['US', 'USA', 'CA', 'CAN', 'MX', 'MEX']; // 美国、加拿大、墨西哥
  return countriesRequiringPreCheck.includes(countryCode.toUpperCase());
}
