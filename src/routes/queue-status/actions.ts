import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';


export function getRealTimeStatistic(data: any) {
  const cmd = SES_CMD.GET_REALTIME_STATISTIC;
  const postData = { ...data };
  return requestApiV3(cmd, postData).then((res: any) => {
    console.log(`request ${cmd}, res=`, res);
    return res;
  })
    .catch((e) => {
      throw handleRequestError(e);
    });
}
