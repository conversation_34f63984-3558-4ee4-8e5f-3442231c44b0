import React from 'react';
import { t } from '@tea/app/i18n';
import { Layout } from '@tencent/tea-component';
// import { TabPanel, Tabs } from '@tea/component';
import StatusTable from '@src/routes/queue-status/components/Table';
import './style.less';
import RegionSelect from '@src/components/RegionSelect/Page';

const { Body, Content } = Layout;

export function QueueStatusPage() {
  // const tabs = [
  //   { id: 'blacklist', label: t('黑名单列表') },
  // ];

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('队列实时状态')} subtitle={<RegionSelect />} />
            <Content.Body>
              <StatusTable />
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
}
