import React, { useEffect, useState } from 'react';
import { Button, Form, Justify, Select } from '@tea/component';
import { useField, FormRenderProps } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import './SearchQuery.less';
import { fetchDomainList } from '@src/routes/domain/actions';
import { TO_DOMAIN_LIST } from '@src/constants/defaultConstant';

interface Props {
  formRenderProps: FormRenderProps<any>;
}

const SearchQuery: React.FC<Props> = (props) => {
  const { formRenderProps } = props;
  const { form, handleSubmit, validating, submitting } = formRenderProps;

  const fromDomain = useField('fromDomain', form);
  const shortRecipientDomain = useField('shortRecipientDomain', form);

  const [domainOptions, setDomainOptions] = useState([
    { value: t('加载中...'), disabled: true },
  ]);

  useEffect(() => {
    fetchDomainList().then((res) => {
      const { EmailIdentities = [] } = res || {};
      let domains = EmailIdentities.filter(
        ({ SendingEnabled }) => SendingEnabled,
      ).map((one: any) => {
        const { IdentityName } = one;
        return { value: IdentityName, text: IdentityName };
      });
      domains = [{ value: '', text: t('所有') }, ...domains];
      setDomainOptions(domains);
    });
  }, []);

  return (
    <form onSubmit={handleSubmit} className="stats-search-query">
      {/* <Table.ActionPanel>*/}
      <Justify
        left={
          <>
            <Form layout="inline">
              <Form.Item label={t('发信域名')}>
                <Select
                  className="select-field"
                  {...fromDomain.input}
                  type="simulate"
                  appearance="button"
                  options={domainOptions}
                  // value={favorite}
                  // onChange={value => setFavorite(value)}
                  placeholder={t('请选择域名')}
                />
              </Form.Item>
              <Form.Item label={t('收信域名')}>
                <Select
                  className="select-field"
                  type="simulate"
                  appearance="button"
                  {...shortRecipientDomain.input}
                  options={[{ value: '', text: t('所有') }, ...TO_DOMAIN_LIST]}
                  placeholder={t('请选择域名')}
                />
              </Form.Item>
            </Form>
          </>
        }
        right={
          <div className="right-search-btn-field">
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              disabled={validating}
            >
              {t('查询')}
            </Button>
          </div>
        }
      />
    </form>
  );
};

export default SearchQuery;
