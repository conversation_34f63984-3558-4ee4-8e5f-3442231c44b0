import React, { useCallback, useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import { Card, Table, Alert } from '@tencent/tea-component';
// import { Bubble, Button, Icon, message } from '@tea/component';
import { getRealTimeStatistic } from '@src/routes/queue-status/actions';
import SearchQuery from '@src/routes/queue-status/components/SearchQuery';
import { useForm } from 'react-final-form-hooks';
import { autotip } from '@tea/component/table/addons';
import moment from 'moment';
import { BasicLine } from '@tencent/tea-chart/lib';
import './Table.less';

const { pageable } = Table.addons;

const initialQueryValues = {
  fromDomain: '',
  shortRecipientDomain: '',
};

export default function StatusTable() {
  const [list, setList] = useState([]);
  const [total, setTotal] = useState();
  const [listLoading, setListLoading] = useState(true);
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });
  const [lineData, setLineData] = useState([]);
  const queryValues = initialQueryValues;

  useEffect(() => {
    statsQuery(queryValues);
  }, []);

  function statsQuery(values) {
    const { fromDomain, shortRecipientDomain } = values;
    const data = {
      FromDomain: fromDomain,
      ShortRecipientDomain: shortRecipientDomain,
    };
    setListLoading(true);
    return getRealTimeStatistic(data)
      .then((res) => {
        // console.log('searchQuery Over', res);
        const { Data = [] } = res;
        const currentLineData = [].concat(Data.map(item => ({ time: moment(Number(item.Time)).format('HH:mm:ss'), value: item.SendCount, lineType: t('发信数量') })))
          .concat(Data.map(item => ({ time: moment(Number(item.Time)).format('HH:mm:ss'), value: item.DeliveredCount, lineType: t('送达数量') })))
          .concat(Data.map(item => ({ time: moment(Number(item.Time)).format('HH:mm:ss'), value: item.BounceCount, lineType: t('退信数量') })))
          .concat(Data.map(item => ({ time: moment(Number(item.Time)).format('HH:mm:ss'), value: item.DroppedCount, lineType: t('拒绝发送数量') })));
        setLineData(currentLineData);
        setList(Data);
        setTotal(Data.length);
      })
      .catch(() => { })
      .finally(() => {
        setListLoading(false);
      });
  }

  const onSearchSubmit = useCallback(
    (values) => {
      // console.log('onSearchSubmit', values, pageData);
      return statsQuery(values);
    },
    []
  );

  const formRenderProps = useForm({
    onSubmit: onSearchSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: queryValues,
    validate: () => ({
      fromDomain: undefined,
      shortRecipientDomain: undefined,
    }),
  });
  const { handleSubmit } = formRenderProps;

  // const [deleteLoading, setDeleteLoading] = useState(false);

  // 翻页
  const onPagingChange = (data) => {
    setPageData(data);
  };
  useEffect(() => {
    handleSubmit();
  }, []);

  return (
    <div className="black-list-table">
      <Alert hideIcon={true}>
        {/* <h4 style={{ marginBottom: 8 }}>{t('注意事项')}</h4> */}
        <p>{t('队列实时状态仅展示最近15分钟的状态，每10秒钟聚合一次。')}</p>
      </Alert>
      {/* 搜索栏 */}
      <Card>
        <Card.Body>
          <div className="flex-one-row">
            <SearchQuery formRenderProps={formRenderProps} />
          </div>
        </Card.Body>
      </Card>
      {/* 折线图 */}
      <Card>
        <div className='queue-status__line'>
          <BasicLine
            height={250}
            position='time*value'
            dataSource={lineData}
            color="lineType"
            canvasMode
          />
        </div>
      </Card>
      {/* 列表 */}
      <Card>
        <Table
          verticalTop
          records={list}
          columns={[
            {
              key: 'Time',
              header: t('时间'),
              render: ({ Time }) => {
                const time = moment(Number(Time)).format('HH:mm:ss');
                return <span>{time}</span>;
              },
            },
            {
              key: 'SendCount',
              header: t('发信数量'),
            },
            {
              key: 'DeliveredCount',
              header: t('送达数量'),
            },
            {
              key: 'BounceCount',
              header: t('退信数量'),
            },
            {
              key: 'DroppedCount',
              header: t('拒绝发送数量'),
            },
          ]}
          addons={[
            pageable({
              recordCount: total,
              pageIndex: pageData.pageIndex,
              pageSize: pageData.pageSize,
              pageSizeOptions: [10, 20, 30, 50, 100],
              onPagingChange: (query) => {
                // console.log('onPagingChange', query);
                // {pageIndex: 2, pageSize: 10}
                onPagingChange(query);
              },
            }),
            autotip({
              isLoading: listLoading,
            }),
          ]}
        />
      </Card>
    </div>
  );
}
