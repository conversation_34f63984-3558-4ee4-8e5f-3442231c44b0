import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';

export interface ListEmailIdentitiesParams {
  TagList?: { TagKey: string; TagValue: string }[];
  Limit?: number;
  Offset?: number;
}

export interface ListEmailIdentitiesResponse {
  EmailIdentities: {
    IdentityName: string;
    SendingEnabled: boolean;
    CurrentReputationLevel: number;
    DailyQuota: number;
    SendIp: string[];
  }[];
  MaxReputationLevel: number;
  MaxDailyQuota: number;
}

export function fetchDomainList(params?: ListEmailIdentitiesParams) {
  const _params = params || {};
  const cmd = SES_CMD.LIST_DOMAIN;
  if (!_params?.TagList) _params.TagList = [];
  const postData = {
    Version: '2020-10-02',
    ...params,
  };
  return requestApiV3(cmd, postData, {}, { tipLoading: false })
    .then((res: ListEmailIdentitiesResponse) => res)
    .catch((e) => handleRequestError(e));
}

export function addDomain(params: {
  EmailIdentity: string;
  TagList?: { tagKey: string; tagValue: string }[];
}) {
  console.log('addDomain start');
  const cmd = SES_CMD.ADD_DOMAIN;
  return requestApiV3(cmd, { Version: '2020-10-02', ...params })
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => handleRequestError(e));
}

export function getDomainInfo(domain: string) {
  const cmd = SES_CMD.GET_DOMAIN;
  return requestApiV3(cmd, { EmailIdentity: domain })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function verifyDomain(domain: string) {
  const cmd = SES_CMD.VERIFY_DOMAIN;
  return requestApiV3(cmd, { EmailIdentity: domain })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function deleteDomain(domain: string) {
  const cmd = SES_CMD.DELETE_DOMAIN;
  return requestApiV3(cmd, { EmailIdentity: domain })
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}
