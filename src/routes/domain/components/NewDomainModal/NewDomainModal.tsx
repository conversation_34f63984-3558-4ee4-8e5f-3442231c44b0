import React, { useEffect } from 'react';
import { Button, Form, Input, Modal } from '@tea/component';
import { useForm, useField } from 'react-final-form-hooks';
import { getValidateStatus } from '@src/utils/FormUtils';
import { t } from '@tea/app/i18n';
import './style.less';
import { SesFormItem } from '@src/components/SesFormItem';
import { InfoTip, TagSelectPanel } from '@src/components/TagComponents';

interface NewDomainModalProps {
  values?: any;
  // options: any;
  visible: boolean;
  onSubmit: (values: any) => void;
  onCancel: () => void;
}

function getDomainValid(domain: string) {
  const reg =
    /^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
  if (!domain) {
    return t('请输入域名');
  }
  if (domain.length < 4) {
    return t('域名太短了');
  }
  if (!reg.test(domain)) {
    return t('名称不符合格式要求');
  }
  return undefined;
}
function getTagValid(tagList: { TagKey: string; TagValue: string }[]) {
  if (tagList?.some?.((item) => !item.TagKey || !item.TagValue)) {
    return t('标签名和标签值不能为空');
  }
  return undefined;
}

const INITIAL_VALUES = { domain: '', tagList: [] };

const NewDomainModal: React.FC<NewDomainModalProps> = (props) => {
  const { visible, onCancel, onSubmit } = props;

  const { form, handleSubmit, validating, submitting } = useForm({
    onSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: INITIAL_VALUES,
    validate: ({ domain }: any) => ({
      domain: getDomainValid(domain),
      tagList: getTagValid(tagList),
    }),
  });

  const domainField = useField('domain', form);
  const tagList = useField('tagList', form);

  useEffect(() => {
    if (visible) {
      // 显示编辑框时重置初始值
      form.reset(INITIAL_VALUES);
      form.resetFieldState(domainField.input.name);
    }
  }, [visible]);

  return (
    <Modal
      className="edit-domain-modal"
      visible={visible}
      caption={t('新建发信域名')}
      onClose={onCancel}
      size={600}
    >
      <form onSubmit={handleSubmit}>
        <Modal.Body>
          <Form
          // layout="vertical"
          >
            <SesFormItem
              label={t('域名')} // "备注名"
              // label="昵称"
              suffix={t(
                '不可使用企业邮箱域名，以免产生SPF、MX记录的冲突，可以在已有企业邮箱域名的情况下创建并使用二级域名。',
              )}
              required
              status={getValidateStatus(domainField.meta, validating)}
              message={
                getValidateStatus(domainField.meta, validating) === 'error' &&
                domainField.meta.error
              }
            >
              <Input
                {...domainField.input}
                size="l"
                placeholder={'abc.def.com'}
                autoComplete="off"
              />
            </SesFormItem>
            <SesFormItem
              label={
                <>
                  {t('标签')}
                  {InfoTip && <InfoTip />}
                </>
              }
            >
              <TagSelectPanel
                serviceType="ses"
                resourcePrefix="senderdomain"
                max={10}
                {...tagList.input}
                onChange={(tags) => {
                  tagList.input.onChange(
                    tags.map((item) => ({
                      TagKey: item.tagKey,
                      TagValue: item.tagValue,
                    })),
                  );
                }}
              />
            </SesFormItem>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            disabled={validating}
          >
            {t('提交')}
          </Button>
          <Button type="weak" htmlType="button" onClick={onCancel}>
            {t('取消')}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default NewDomainModal;
