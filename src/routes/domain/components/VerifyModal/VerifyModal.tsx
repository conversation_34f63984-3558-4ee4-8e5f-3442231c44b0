import React, { useCallback, useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  message,
  Modal,
  StatusTip,
  Table,
  Text,
} from '@tea/component';
import get from 'lodash/get';
import { t } from '@tea/app/i18n';
import { getDomainInfo, verifyDomain } from '@src/routes/domain/actions';
// import { AREA_TYPE, getCurrentUserArea } from '@src/utils/CommonUtils';
import './style.less';
import _ from 'lodash';
import { getLinkUrl, LINK_URL_NAME } from '@src/constants/urlConfig';

interface NewDomainModalProps {
  current: any;
  visible: boolean;
  onCancel: () => void;
}

// const spfRecords = [
//   {
//     type: 'TXT',
//     domain: 'wanglala.com',
//     record: 'v=spf1 include:qcloudmail.com ~all',
//     status: t('待验证'),
//   },
// ];

const defaultRecordData = [{ label: 'spf', required: true, records: [] }];

const VerifyModal: React.FC<NewDomainModalProps> = (props) => {
  const { visible, onCancel, current = {} } = props;
  const currentDomain = current?.domain;
  const [data, setData] = useState(defaultRecordData);
  const [domainInfoLoading, setDomainInfoLoading] = useState(true);
  const [verifyLoading, setVerifyLoading] = useState(false);

  const saveDomainInfo = useCallback((res) => {
    const { Attributes = [] } = res;
    const attrs = Attributes.map((one: any) => {
      const { CurrentValue, ExpectedValue, SendDomain, Status, Type } = one;
      let statusText = t('待验证');
      if (Status) statusText = t('已验证');
      else if (CurrentValue) statusText = t('验证失败');
      return {
        type: Type,
        domain: SendDomain,
        record: ExpectedValue,
        current: CurrentValue,
        status: statusText,
        isVerify: !!Status,
      };
    });
    // CurrentValue: ""
    // ExpectedValue: "v=spf1 include:qcloudmail.com ~all"
    // SendDomain: "<EMAIL>"
    // Status: false
    // Type: "TXT"
    // const {CurrentValue, ExpectedValue, SendDomain, Status, Type}
    const recordNameMap: any = {};
    attrs.forEach((attr: any) => {
      const { record, type, domain } = attr;
      // 匹配记录值
      let recordName = '';
      if (domain?.includes('_dmarc.')) {
        recordName = 'DMARC';
      } else {
        const match = record.match(/v=(?<recordName>[a-zA-Z]+)1/);
        recordName = get(match, 'groups.recordName') || type;
      }
      if (!recordNameMap[recordName]) recordNameMap[recordName] = [];
      recordNameMap[recordName].push(attr);
    });
    console.log('recordNameMap=', recordNameMap);
    const recordNameArr = Object.keys(recordNameMap).map((name: string) => {
      const attrsArr = recordNameMap[name];
      return {
        label: name,
        required: true,
        records: attrsArr,
      };
    });
    setData(recordNameArr);
  }, []);

  const fetchDomainInfo = useCallback(() => {
    if (currentDomain) {
      setDomainInfoLoading(true);
      setData(defaultRecordData);
      getDomainInfo(currentDomain)
        .then((res) => {
          console.log('getDomainInfo res=', res);
          saveDomainInfo(res);
        })
        .finally(() => {
          setDomainInfoLoading(false);
        });
    }
  }, [currentDomain]);

  useEffect(() => {
    if (visible) fetchDomainInfo();
  }, [currentDomain, visible]);

  const handleSubmit = () => {
    console.log('onFormSubmit', currentDomain);
    setVerifyLoading(true);
    verifyDomain(currentDomain)
      .then((res) => {
        console.log('verifyDomain res=', res);
        // fetchDomainInfo();
        saveDomainInfo(res);
        const { VerifiedForSendingStatus } = res;
        if (VerifiedForSendingStatus) onCancel();
        else {
          message.error({ content: t('验证未通过') });
        }
      })
      .catch((e) => {
        console.log('domain verify catch', e);
      })
      .finally(() => {
        setVerifyLoading(false);
      });
    return false;
  };

  // const { form, handleSubmit, validating, submitting } = useForm({
  //   onSubmit: onFormSubmit,
  //   /**
  //    * 默认为 shallowEqual
  //    * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
  //    * useEffect(() => form.initialize({ }), []);
  //    */
  //   initialValuesEqual: () => true,
  //   initialValues: current,
  //   validate: ({ domain }: any) => ({}),
  // });
  // const domain = useField("domain", form);

  return (
    <Modal
      className="verify-domain-modal"
      visible={visible}
      // 帮助文档链接
      caption={
        <span>
          {t('发信域名配置')}&nbsp;&nbsp;
          <a
            href={getLinkUrl(LINK_URL_NAME.verifyDoc)}
            target="_blank"
            rel="noreferrer"
          >
            {t('帮助文档')}
          </a>
        </span>
      }
      // caption={t('发信域名配置')}
      onClose={onCancel}
      size="xl"
    >
      <Modal.Body>
        {/* <form onSubmit={handleSubmit} className="domain-info-form">*/}
        <div className="domain-info-cards">
          {data.map((recordData) => {
            const { records, label, required } = recordData;
            let tipsText = '';
            // if (label === 'DMARC') {
            //   const noVerify = _.find(records, o => !o.isVerify);
            //   if (noVerify) tipsText = `, ${t('<EMAIL> 应替换为任意您可用于接收伪造报告的邮件地址。')}`;
            // };
            let tipsText2 = '';
            if (label === 'MX') {
              tipsText2 = t(
                '如果域名本身提供了邮件服务(已存在MX记录)，无需配置“mxbiz1.qq.com”，详情请参照“帮助文档”。',
              );
              const noVerify = _.find(records, (o) => !o.isVerify);
              if (noVerify) tipsText = `, ${t('该记录值末尾需要包含“.”。')}`;
            }
            const verifyLabel = t('{{type}}验证', { type: label });
            const requiredLabel = t('必须');
            const noRequiredLabel = t('可选');
            return (
              <>
                <Card bordered>
                  <Card.Body
                    title={
                      <p>
                        {`${verifyLabel}（${
                          required ? requiredLabel : noRequiredLabel
                        }）${tipsText}`}
                        {tipsText2 && <br />}
                        {tipsText2}
                      </p>
                    }
                  >
                    <Table
                      verticalTop
                      records={records}
                      disableTextOverflow={true} // true则不省略，显示全部
                      recordKey="domain"
                      topTip={
                        domainInfoLoading && (
                          <StatusTip
                            status={domainInfoLoading ? 'loading' : 'found'}
                            // onClear={() => setStatus("loading")}
                            // onRetry={() => setStatus("loading")}
                          />
                        )
                      }
                      // rowDisabled={record => record.status === "stopped"}
                      // rowClassName={record => record.status}
                      columns={[
                        {
                          key: 'type',
                          header: t('类型'),
                          width: 60,
                        },
                        {
                          key: 'domain',
                          header: t('主机记录'),
                          width: '20%',
                        },
                        {
                          key: 'record',
                          header: t('记录值'),
                          width: '30%',
                          render: (row) => (
                            <Text
                              copyable={row.record}
                              style={{ backgroundColor: '#ebeef2' }}
                            >
                              {row.record}
                            </Text>
                          ),
                        },
                        {
                          key: 'current',
                          header: t('当前值'),
                          width: '30%',
                          render: (row) => (
                            <Text
                              copyable={row.current}
                              style={{ backgroundColor: '#ebeef2' }}
                            >
                              {row.current}
                            </Text>
                          ),
                        },
                        {
                          key: 'status',
                          header: t('状态'),
                          align: 'right',
                          width: 80,
                        },
                      ]}
                    />
                  </Card.Body>
                </Card>
              </>
            );
          })}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          type="primary"
          // htmlType="submit"
          onClick={handleSubmit}
          loading={domainInfoLoading || verifyLoading}
          disabled={verifyLoading}
        >
          {t('提交验证')}
        </Button>
        <Button type="weak" htmlType="button" onClick={onCancel}>
          {t('取消')}
        </Button>
      </Modal.Footer>

      {/* </form>*/}
    </Modal>
  );
};

export default VerifyModal;
