import React, { useState, useEffect } from 'react';
import { t, Trans } from '@tea/app/i18n';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import {
  Modal,
  Text,
  Button,
  message,
  SelectMultiple,
  ExternalLink,
  Icon,
} from '@tencent/tea-component';
import { getConfigurableIpDomainMapping } from '@src/routes/dedicated-ip/actions';
import styles from './style.module.less';
import { JumpIcon } from '@tencent/tea-icons-react';
import { useHistory } from '@tencent/tea-app';
import { useAsyncRetry } from 'react-use';
import { useOptionsTips } from '@src/hooks/useOptionsTips';

interface DialogValue {
  domain: string;
}

interface DialogProps {
  dialogRef: DialogRef<DialogValue>;
  onConfirm: (value: { domain: string; ips: string[] }) => Promise<void>;
}

const AddIPDialog: React.FC<DialogProps> = ({ dialogRef, onConfirm }) => {
  const [visible, setShowState, value] = useDialog<DialogValue>(dialogRef);
  const [selectedIPs, setSelectedIPs] = useState<string[]>([]);

  const {
    value: ipOptions = [],
    loading: refreshing,
    retry: fetchIPOptions,
  } = useAsyncRetry(async () => {
    if (!value?.domain) return [];

    try {
      const res = await getConfigurableIpDomainMapping({
        SendDomain: value.domain,
        QueryType: 1,
      });

      return res.ConfigurableItems.filter((item) => item.Status === 0).map(
        (item) => ({
          value: item.Item,
          text: item.Item,
        }),
      );
    } catch (error) {
      message.error({ content: t('获取IP列表失败，请稍后重试') });
      return [];
    }
  }, [value?.domain, visible]);

  useEffect(() => {
    if (!visible) {
      setSelectedIPs([]);
    }
  }, [visible]);

  const handleConfirm = async () => {
    if (!value || !selectedIPs.length) return;
    try {
      await onConfirm({
        domain: value.domain,
        ips: selectedIPs,
      });
      setShowState(false);
      message.success({ content: t('添加独立IP成功') });
    } catch (error) {
      message.error({ content: t('添加独立IP失败，请稍后重试。') });
    }
  };

  const optionsTips = useOptionsTips({
    loading: refreshing,
    dataLength: ipOptions.length,
    emptyText: t('暂无可添加独立IP'),
  });

  return (
    <Modal
      visible={visible}
      caption={t('添加独立IP')}
      onClose={() => setShowState(false)}
      size="m"
    >
      <Modal.Body>
        <div style={{ marginBottom: 16 }}>
          <Text theme="label" style={{ display: 'inline-block', width: 70 }}>
            {t('发信域名')}
          </Text>
          <Text>{value?.domain}</Text>
        </div>
        <div>
          <Text theme="label" style={{ display: 'inline-block', width: 70 }}>
            {t('独立IP')}
          </Text>
          <SelectMultiple
            size="l"
            footer={
              <div className={styles.footer}>
                <Trans>
                  找不到独立IP？
                  <Button
                    type="link"
                    onClick={fetchIPOptions}
                    loading={refreshing}
                  >
                    刷新
                  </Button>
                  或
                  <Button
                    type="link"
                    onClick={() =>
                      window.open(
                        '/ses/dedicated-ip?type=openNewDialog',
                        '_blank',
                      )
                    }
                  >
                    申请独立IP
                    <JumpIcon />
                  </Button>
                </Trans>
              </div>
            }
            listHeight={343}
            staging={false}
            appearance="button"
            options={ipOptions}
            value={selectedIPs}
            onChange={setSelectedIPs}
            tips={optionsTips}
            placeholder={t('请选择独立IP')}
          />
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          type="primary"
          loading={refreshing}
          onClick={handleConfirm}
          disabled={!selectedIPs.length}
        >
          {t('添加')}
        </Button>
        <Button onClick={() => setShowState(false)}>{t('取消')}</Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AddIPDialog;
