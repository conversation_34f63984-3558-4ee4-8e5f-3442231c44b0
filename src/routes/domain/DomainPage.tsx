import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from 'react';
import { t, Trans } from '@tea/app/i18n';
import { app } from '@tea/app';
import {
  Table,
  Justify,
  Button,
  SearchBox,
  Card,
  Layout,
  Bubble,
  Icon,
  Text,
  List,
  Dropdown,
  message,
  ExternalLink,
} from '@tencent/tea-component';
import { <PERSON><PERSON>, Collapse, Modal } from '@tea/component';
import { useDialogRef } from '@src/routes/global-components/useDialog';
import NewDomainModal from '@src/routes/domain/components/NewDomainModal/NewDomainModal';
import VerifyModal from '@src/routes/domain/components/VerifyModal/VerifyModal';
import { AREA_TYPE, getCurrentUserArea } from '@src/utils/CommonUtils';
import {
  addDomain,
  deleteDomain,
  fetchDomainList,
  ListEmailIdentitiesResponse,
} from '@src/routes/domain/actions';
import { fetchAccount } from '../ses-index/actions';
import { getLinkUrl, LINK_URL_NAME } from '@src/constants/urlConfig';
import RegionSelect from '@src/components/RegionSelect/Page';
import AddIPDialog from './components/AddIPDialog/index';
import { AccountOpenStatus } from '../dedicated-ip/const';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  InfoCircleIcon,
  JumpIcon,
} from '@tencent/tea-icons-react';
import { createDomainSendIp } from '../dedicated-ip/actions';
import { useHistory } from '@tencent/tea-app';
import { useUrlParamTrigger } from '@src/hooks/useUrlParamTrigger';
import { TagDisplayIcon, useLoadTagSDK } from '@src/components/TagComponents';
import EditTagModal from '@src/components/EditTagModal';

const { Body, Content } = Layout;
const { pageable, autotip } = Table.addons;

const getIntroductionList = () => {
  let introductionList = [];
  const defaultIntroductionList = [
    // t('您必须对发信域名拥有管理权限'),
    // t(
    //   '域名配置后，可能需要5分钟-2小时的时间同步，如果不能立即验证通过，请耐心等待'
    // ),
    // t(
    //   '域名验证完成后，请不要删除和修改已配置的SPF、MX记录，否则会导致发信异常'
    // ),
    <Alert.Strong>
      {t(
        '发信域名配置完成并验证通过后，请一直保持MX、SPF、DKIM、DMARC的正确配置，否则会导致发信异常。',
      )}
    </Alert.Strong>,
    t('不可使用企业邮箱域名，以免产生配置信息的冲突。'),
    t(
      '域名配置后，可能需要5分钟-2小时的时间同步，如果不能立即验证通过，请耐心等待。',
    ),
    t('每个腾讯云账户可配置最多10个域名。'),
  ];

  const cnsUrl = `https://${
    window.QCCONSOLE_HOST || 'console.tencentcloud.com'
  }/cns`;

  const cnExtraPos2 = (
    <Trans>
      如果您的域名托管在腾讯云，请进入
      <ExternalLink href={cnsUrl}>DNSPod控制台</ExternalLink>
      配置域名验证信息；如果您的域名托管在其它域名服务商，请按照域名配置详情信息自行配置。
    </Trans>
  );
  const enExtraPos = t(
    '请在您的域名托管服务商提供的配置界面中，按照域名配置详情信息配置域名验证信息。',
  );

  const area = getCurrentUserArea();
  if (area === AREA_TYPE.MAINLAND) {
    introductionList = [
      // defaultIntroductionList[0],
      cnExtraPos2,
      ...defaultIntroductionList,
    ];
  } else {
    introductionList = [enExtraPos, ...defaultIntroductionList];
  }
  return introductionList;
};

export function DomainPage() {
  const [domainList, setDomainList] = useState([]);
  const [domainListLoading, setDomainListLoading] = useState(true);
  const [reputationPolicyId, setReputationPolicyId] = useState(0);
  const history = useHistory();
  const { tagFilterable, tagConstants } = useLoadTagSDK();
  const [filteringTags, setFilteringTags] = useState([{}]);
  const editTagModalRef = useDialogRef();

  const fetchList = useCallback(
    (tagList?: { TagKey: string; TagValue: string }[]) => {
      setDomainListLoading(true);
      // setDomainList([]);
      fetchDomainList({
        TagList: tagList,
      })
        .then((res: ListEmailIdentitiesResponse) => {
          console.log('fetchDomainList res=', res);
          const { EmailIdentities = [] } = res || {};
          const domains = EmailIdentities.map((one: any) => {
            const {
              IdentityName,
              SendingEnabled,
              CurrentReputationLevel,
              DailyQuota,
              SendIp,
              TagList = [],
            } = one;
            return {
              domain: IdentityName,
              status: SendingEnabled ? 'verified' : 'start',
              CurrentReputationLevel,
              DailyQuota,
              SendIp,
              TagList: TagList.map((item) => ({
                tagKey: item.TagKey,
                tagValue: item.TagValue,
              })),
            };
          });
          setDomainList(domains);
        })
        .finally(() => {
          setDomainListLoading(false);
        });
    },
    [],
  );

  useEffect(() => {
    fetchAccount().then((res) => {
      // console.log('fetchAccount--', res);
      const { ReputationPolicyId = 0 } = res || {};
      setReputationPolicyId(ReputationPolicyId);
      fetchList();
    });
  }, []);
  const [newModalVisible, setNewModalVisible] = useState(false);
  const [verifyModalVisible, setVerifyModalVisible] = useState(false);
  const [currentDomain, setCurrentDomain] = useState(domainList[0]);
  const addIPDialogRef = useDialogRef();

  const onVerifyClick = useCallback((item) => {
    setCurrentDomain(item);
    setVerifyModalVisible(true);
  }, []);

  const onCreateSubmit = useCallback((values) => {
    console.log('onCreateSubmit', values);
    return addDomain({
      EmailIdentity: values.domain,
      TagList: values.tagList,
    }).then(() => {
      setNewModalVisible(false);
      fetchList();
    });
  }, []);

  const onDeleteConfirm = async (item) => {
    const yes = await Modal.confirm({
      message: t('确认删除当前所选域名？'),
      description: t('删除后，不能再用该域名发送邮件。'),
      okText: t('删除'),
      cancelText: t('取消'),
    });
    if (yes) {
      return deleteDomain(item.domain).then(() => {
        fetchList();
      });
    }
    // setAnswer(yes ? "已删除" : "未删除");
  };

  // 右上角搜索框
  const [searchBox, setSearchBox] = useState('');
  const onSearchBoxChange = (value) => {
    setSearchBox(value);
  };
  const filterList = useMemo(() => {
    if (searchBox) {
      return domainList.filter((one) => {
        const { domain = '' } = one || {};
        return domain.includes(searchBox);
      });
    }
    return domainList;
  }, [domainList, searchBox]);

  const introductionList = getIntroductionList();

  const handleAddIp = async (item) => {
    try {
      const accountInfo = await fetchAccount();
      const isOpened = AccountOpenStatus.includes(accountInfo?.StatusOfIp);
      if (!isOpened) {
        const yes = await Modal.confirm({
          message: (
            <>
              <Icon type="error" size={32} style={{ marginRight: 8 }} />
              {t('您未开通【独立IP增值服务】')}
            </>
          ),
          onOk: () => {
            window.open('/ses/dedicated-ip', '_blank');
          },
          description: t('暂不可添加独立IP，请先开通【独立IP增值服务】'),
          okText: (
            <>
              {t('前往开通')}
              <JumpIcon />
            </>
          ),
          cancelText: t('取消'),
        });

        if (yes) {
          const shouldRefresh = await Modal.confirm({
            message: t('提示'),
            description: t(
              '如已开通【独立IP增值服务】，请您刷新页面后再尝试添加独立IP。',
            ),
            okText: t('刷新'),
            cancelText: t('取消'),
          });

          if (shouldRefresh) {
            window.location.reload();
          }
        }
        return;
      }

      addIPDialogRef.current.open({
        domain: item.domain,
      });
    } catch (error) {
      message.error({ content: t('获取账户信息失败，请稍后重试') });
    }
  };

  const handleAddIPConfirm = async (value: {
    domain: string;
    ips: string[];
  }) => {
    try {
      await createDomainSendIp({
        IpDomainConfigs: value.ips.map((ip) => ({
          Ip: ip,
          Domain: [value.domain],
        })),
      });
      message.success({ content: t('添加独立IP成功') });
      fetchList();
    } catch (error) {
      message.error({ content: t('添加独立IP失败，请稍后重试。') });
    }
  };

  // 编辑标签
  const onEditTag = (item) => {
    editTagModalRef.current.open({
      resourceType: 'senderdomain',
      resourceIds: [item.domain],
      resourceNames: [item.domain],
      currentTags: item.TagList || [],
    });
  };

  useUrlParamTrigger({
    paramName: 'type',
    triggerValue: 'openNewDialog',
    onTrigger: (value) => setNewModalVisible(true),
  });
  console.log('FilteringTags', filteringTags);

  const tagFilterableAddon = !!tagFilterable
    ? [
        tagFilterable({
          serviceType: 'ses',
          resourcePrefix: 'senderdomain',
          column: 'TagList',
          value: filteringTags,
          onChange: (_tags) => {
            const tags = _tags.map((item) => ({
              TagKey: item.tagKey,
              TagValue: item.tagValue,
            }));
            setFilteringTags(_tags);
            // 当标签筛选条件改变时，重新获取域名列表
            fetchList(tags);
          },
          max: 3,
          disableUnset: true,
        }),
      ]
    : [];

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('发信域名')} subtitle={<RegionSelect />} />
            <Content.Body>
              <Alert>
                <h4 style={{ marginBottom: 8 }}>{t('注意事项')}</h4>
                {introductionList.slice(0, 2).map((one, idx) => (
                  <p key={idx}>
                    {idx + 1}. {one}
                  </p>
                ))}
                {/* <p>1. {t('您必须对发信域名拥有管理权限')}</p>*/}
                {/* <p>2. <Trans>域名配置。如果您的域名托管在腾讯云，请进入 <a href='https://console.tencentcloud.com/cns'*/}
                {/*                                      target="_blank">https://console.tencentcloud.com/cns</a> 配置。如果您的域名托管在其它域名服务商，请自行按照清单详情来配置</Trans>*/}
                {/* </p>*/}
                {introductionList.length > 2 && (
                  <Collapse
                    iconPosition="right"
                    // style={{ marginTop: 8 }}
                    icon
                  >
                    <Collapse.Panel
                      id="1"
                      title={(active) => (
                        <>
                          <Text theme="primary">
                            {active ? t('收起') : t('展开')}
                          </Text>
                          {active ? (
                            <ChevronUpIcon color="var(--tea-color-text-brand-default)" />
                          ) : (
                            <ChevronDownIcon color="var(--tea-color-text-brand-default)" />
                          )}
                        </>
                      )}
                      position="top"
                    >
                      {introductionList.slice(2).map((one, idx) => (
                        <p key={idx}>
                          {idx + 3}. {one}
                        </p>
                      ))}
                      {/* <p>3. {t('域名配置后，可能需要5分钟-2小时的时间同步，如果不能立即验证通过，请耐心等待')}</p>*/}
                      {/* <p>4. {t('域名验证完成后，请不要删除和修改已配置的SPF、MX记录，否则会导致发信异常')}</p>*/}
                      {/* <p>5. {t('不可使用企业邮箱域名，以免产生SPF、MX记录的冲突')}</p>*/}
                      {/* <p>6. {t('每个腾讯云账户可配置最多10个域名')}</p>*/}
                    </Collapse.Panel>
                  </Collapse>
                )}
              </Alert>
              <Table.ActionPanel>
                <Justify
                  left={
                    <>
                      <Button
                        type="primary"
                        onClick={() => {
                          setNewModalVisible(true);
                        }}
                      >
                        {t('新建发信域名')}
                      </Button>
                      {/* <Button>开机</Button>*/}
                    </>
                  }
                  right={
                    <>
                      <SearchBox
                        onChange={onSearchBoxChange}
                        placeholder={t('请输入发信域名搜索')}
                      />
                      {/* <Button icon="setting" />*/}
                      {/* <Button icon="refresh" />*/}
                      {/* <Button icon="download" />*/}
                    </>
                  }
                />
              </Table.ActionPanel>
              <Card>
                <Table
                  verticalTop
                  records={filterList}
                  recordKey="domain"
                  // rowDisabled={record => record.status === "start"}
                  rowClassName={(record) => record.status}
                  // topTip={
                  //   domainListLoading && (
                  //     <StatusTip
                  //       status={domainListLoading ? 'loading' : 'found'}
                  //       // onClear={() => setStatus("loading")}
                  //       // onRetry={() => setStatus("loading")}
                  //     />
                  //   )
                  // }
                  columns={[
                    {
                      key: 'domain',
                      header: t('发信域名'),
                      // render: cvm => (
                      //   <>
                      //     <p>
                      //       <a>{cvm.domain}</a>
                      //     </p>
                      //   </>
                      // ),
                    },
                    {
                      key: 'status',
                      header: () => (
                        <>
                          {t('状态')}
                          <Bubble
                            content={t(
                              '域名状态，验证通过才可以通过此域名发送邮件',
                            )}
                          >
                            <Icon type="info" />
                          </Bubble>
                        </>
                      ),
                      // width: 100,
                      render: (cvm) => {
                        if (cvm.status === 'verified') {
                          return (
                            <span style={{ color: 'green' }}>
                              {t('验证通过')}
                            </span>
                          );
                        }
                        if (cvm.status === 'start') {
                          return (
                            <span style={{ color: 'red' }}>{t('待验证')}</span>
                          );
                        }
                        return cvm.status;
                      },
                    },
                    reputationPolicyId > 0 && {
                      key: 'CurrentReputationLevel',
                      header: t('信誉等级'),
                      render: (item) => (
                        <span>
                          {item.status !== 'verified'
                            ? '-'
                            : item.CurrentReputationLevel}
                        </span>
                      ),
                    },
                    reputationPolicyId > 0 && {
                      key: 'DailyQuota',
                      header: () => (
                        <p>
                          <span>{t('单日最高发信量')}</span>
                          <Bubble
                            className="app-ses-alert"
                            style={{ marginBottom: 0 }}
                            content={
                              <ExternalLink
                                href={getLinkUrl(LINK_URL_NAME.reputationDoc)}
                              >
                                {t('提升规则')}
                              </ExternalLink>
                            }
                          >
                            <InfoCircleIcon />
                          </Bubble>
                        </p>
                      ),
                      render: (item) => (
                        <span>
                          {item.status !== 'verified' ? '-' : item.DailyQuota}
                        </span>
                      ),
                    },
                    {
                      key: 'SendIp',
                      header: t('发信IP'),
                      render: ({ SendIp = [] }) => {
                        // const SendIp = ['sdfsdfsdf', 'bfdggndfgndf'].filter(
                        //   (ip) => ip !== '',
                        // );
                        if (SendIp.length > 0) {
                          return (
                            <Dropdown
                              button={
                                <>
                                  <Text>{t('独立IP')}</Text>
                                  <Text theme="primary">
                                    {' ' + SendIp.length + ' '}
                                  </Text>
                                  <Text>{t('个')}</Text>
                                </>
                              }
                              trigger="hover"
                              appearance="pure"
                            >
                              <List type="option">
                                {SendIp.map((ip) => (
                                  <List.Item
                                    onClick={() => {
                                      history.push(
                                        `/ses/dedicated-ip?ip=${ip}`,
                                      );
                                    }}
                                    key={ip}
                                  >
                                    <Text theme="primary">{ip}</Text>
                                  </List.Item>
                                ))}
                              </List>
                            </Dropdown>
                          );
                        }
                        return <Text>{t('共享IP')}</Text>;
                      },
                    },
                    // {
                    //   key: "createTime",
                    //   header: t("创建时间"),
                    // },
                    {
                      key: 'TagList',
                      header: t('标签'),
                      render: (item) => {
                        console.log('item.TagList', item, item.TagList);
                        if (item.TagList && item.TagList.length > 0) {
                          return <TagDisplayIcon tags={item.TagList} />;
                        }
                        return '-';
                      },
                    },
                    {
                      key: 'action',
                      header: t('操作'),
                      // align: "right",
                      width: 230,
                      render: (item) => (
                        <>
                          <Button
                            type="link"
                            onClick={() => {
                              onVerifyClick(item);
                            }}
                          >
                            {item.status !== 'verified' ? t('验证') : t('详情')}
                          </Button>
                          <Bubble
                            content={
                              item.status !== 'verified'
                                ? t('验证通过的发信域名才能配置独立IP')
                                : undefined
                            }
                          >
                            <Button
                              type="link"
                              disabled={item.status !== 'verified'}
                              onClick={() => handleAddIp(item)}
                            >
                              {t('添加独立IP')}
                            </Button>
                          </Bubble>
                          <Button
                            type="link"
                            onClick={() => {
                              onEditTag(item);
                            }}
                          >
                            {t('编辑标签')}
                          </Button>
                          <Button
                            type="link"
                            onClick={() => {
                              onDeleteConfirm(item);
                            }}
                          >
                            {t('删除')}
                          </Button>
                        </>
                      ),
                    },
                  ].filter((r) => r)}
                  addons={[
                    pageable(),
                    autotip({
                      isLoading: domainListLoading,
                    }),
                    ...tagFilterableAddon,
                  ]}
                />
              </Card>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
      <NewDomainModal
        visible={newModalVisible}
        onCancel={() => {
          setNewModalVisible(false);
        }}
        onSubmit={onCreateSubmit}
      />
      <VerifyModal
        visible={verifyModalVisible}
        current={currentDomain}
        onCancel={() => {
          setVerifyModalVisible(false);
          fetchList();
        }}
        // onSubmit={onVerifySubmit}
      />
      <AddIPDialog dialogRef={addIPDialogRef} onConfirm={handleAddIPConfirm} />
      <EditTagModal dialogRef={editTagModalRef} onConfirm={fetchList} />
    </>
  );
}
