import React from 'react';
import { t } from '@tea/app/i18n';
import { Layout, TabPanel, Tabs, Icon, Bubble } from '@tencent/tea-component';
// import { TabPanel, Tabs } from '@tea/component';
import { BlackListTable } from '@src/routes/block-list/components/BlockListTable';
import './style.less';
import RegionSelect from '@src/components/RegionSelect/Page';
import { CustomizeBlockList } from './components/CustomizeBlockList';
import { RejectBlockList } from './components/RejectBlockList';

const { Body, Content } = Layout;

export function BlockListPage() {
  const tabs = [
    {
      id: 'global',
      label: (
        <>
          {t('无效地址拦截')}
          {/* <Bubble
            content={t(
              '平台会对实际不存在、不可用或者长期不活跃的无效地址主动进行拦截，180天后释放。注意：无效地址率过高，会被判定为“盲目群发垃圾邮件”，影响邮件到达率和发信域名信誉度。',
            )}
          >
            <Icon type="info" style={{ marginLeft: '3px' }} />
          </Bubble> */}
        </>
      ),
    },
    { id: 'refuse', label: t('拒收拦截') },
    { id: 'customize', label: t('自定义拦截') },
  ];

  return (
    <>
      <Layout>
        <Body>
          <Content>
            <Content.Header title={t('拦截')} subtitle={<RegionSelect />} />
            <Content.Body>
              <Tabs ceiling animated={false} tabs={tabs}>
                <TabPanel id="global">
                  <BlackListTable />
                </TabPanel>
                <TabPanel id="refuse">
                  <RejectBlockList />
                </TabPanel>
                <TabPanel id="customize">
                  <CustomizeBlockList />
                </TabPanel>
              </Tabs>
            </Content.Body>
          </Content>
        </Body>
      </Layout>
    </>
  );
}
