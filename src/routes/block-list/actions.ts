import { handleRequestError } from '@src/utils/RequestUtils';
import { requestApiV3 } from '@src/api';
import { SES_CMD } from '@src/api/constants';
import { pickBy, isNil } from 'lodash';

interface CustomBlockList {
  Data: Array<{
    Id: number;
    Email: string;
    CreateTime: string;
    ExpireDate: string;
    Status: number;
  }>;
  TotalCount: number;
}

export function searchQuery(data: any) {
  console.log('searchQuery start data=', data);
  const cmd = SES_CMD.GET_STATISTICS_REPORT;
  const { StartDate, EndDate, Domain, ReceivingMailboxType } = data;
  const postData = {
    StartDate,
    EndDate,
    Domain,
    ReceivingMailboxType,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function searchBlackList(data: any) {
  console.log('searchBlackList start data=', data);
  const cmd = SES_CMD.LIST_BLACKLIST;
  const {
    StartDate,
    EndDate,
    EmailAddress,
    Offset = 0,
    Limit = 10000,
    TaskID,
  } = data;
  const postData = {
    StartDate,
    EndDate,
    EmailAddress,
    Offset,
    Limit,
    TaskID,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function deleteBlackList(data: any) {
  console.log('deleteBlackList start data=', data);
  const cmd = SES_CMD.DELETE_BLACKLIST;
  const postData = {
    EmailAddressList: data,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function getCustomBlockList(data: any) {
  const cmd = SES_CMD.LIST_CUSTOM_BLACKLIST;
  const postData = {
    ...pickBy(data, (v) => !isNil(v) && v !== ''),
    Version: '2020-10-02',
  };
  return requestApiV3(cmd, { ...postData })
    .then((res: CustomBlockList) => {
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function updateCustomBlackList(data: any) {
  const cmd = SES_CMD.UPDATE_CUSTOM_BLACKLIST;
  const postData = {
    ...pickBy(data, (v) => !isNil(v) && v !== ''),
    Version: '2020-10-02',
  };
  return requestApiV3(cmd, { ...postData })
    .then((res) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function deleteCustomBlackList(data: any) {
  const cmd = SES_CMD.DELETE_CUSTOM_BLACKLIST;
  return requestApiV3(cmd, {
    ...data,
    Version: '2020-10-02',
  })
    .then((res) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function uploadCustomBlacklist(data: any) {
  const cmd = SES_CMD.UPLOAD_CUSTOM_BLACKLIST;
  return requestApiV3(cmd, {
    ...pickBy(data, (v) => !isNil(v) && v !== ''),
    Version: '2020-10-02',
  })
    .then((res) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function createCustomBlacklist(data: any) {
  const cmd = SES_CMD.CREATE_CUSTOM_BLACKLIST;
  return requestApiV3(cmd, {
    ...pickBy(data, (v) => !isNil(v) && v !== ''),
    Version: '2020-10-02',
  })
    .then((res) => {
      console.log(`request ${cmd}, res=`, res);
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function getCOSConf(params = {}) {
  const cmd = SES_CMD.GET_COS_CONF;
  return requestApiV3(
    cmd,
    { ...params },
    {},
    {
      tipLoading: false,
    },
  )
    .then((res: any) => res)
    .catch((e) => handleRequestError(e));
}

export function uploadCustomBlacklistByCOS(params = {}) {
  const cmd = SES_CMD.UPLOAD_CUSTOM_BLACKLIST_BY_COS;
  return requestApiV3(
    cmd,
    { ...params },
    {},
    {
      tipLoading: false,
    },
  )
    .then((res: any) => res)
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function getGlobalBlacklistAddress(data: any) {
  console.log('GET_GLOBAL_BLACKLIST_ADDRESS start data=', data);
  const cmd = SES_CMD.GET_GLOBAL_BLACKLIST_ADDRESS;
  const { EmailAddress } = data;
  const postData = {
    EmailAddress: EmailAddress.trim(),
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`GET_GLOBAL_BLACKLIST_ADDRESS request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}
// 加白
export function createWhitelistFromBlacklist(data: any) {
  console.log('CREATE_WHITELIST_FROM_BLACKLIST start data=', data);
  const cmd = SES_CMD.CREATE_WHITELIST_FROM_BLACKLIST;
  const postData = {
    Address: data[0],
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`CREATE_WHITELIST_FROM_BLACKLIST request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

// 拒收
export function listRejectedRecipients(data: any) {
  console.log('LIST_REJECTED_RECIPIENTS start data=', data);
  const cmd = SES_CMD.LIST_REJECTED_RECIPIENTS;
  const { StartDate, EndDate, EmailAddress, Offset = 0, Limit = 10000 } = data;
  const postData = {
    StartDate,
    EndDate,
    EmailAddress: EmailAddress.trim(),
    Offset,
    Limit,
  };
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`LIST_REJECTED_RECIPIENTS request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}

export function getAccountCustomBlacklistStatus() {
  const cmd = SES_CMD.GET_ACCOUNT_CUSTOM_BLACKLIST_STATUS;
  const postData = {};
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(`GetAccountCustomBlacklistStatus request ${cmd}, res=`, res);
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}
export function updateAccountCustomBlacklistStatus(data: any) {
  const cmd = SES_CMD.UPDATE_ACCOUNT_CUSTOM_BLACKLIST_STATUS;
  const { CustomBlacklistStatus } = data;
  const postData = {
    CustomBlacklistStatus,
  };
  console.log('requestApiV3', postData);
  return requestApiV3(cmd, postData)
    .then((res: any) => {
      console.log(
        `UpdateAccountCustomBlacklistStatus request ${cmd}, res=`,
        res,
      );
      return res;
    })
    .catch((e) => {
      throw handleRequestError(e);
    });
}
