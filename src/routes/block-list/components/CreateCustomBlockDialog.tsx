import { useEffect, useState } from 'react';
import { useForm, useField } from 'react-final-form-hooks';
import {
  Modal,
  Form,
  Input,
  Button,
  Segment,
  DatePicker,
  Radio,
  Text,
  message,
} from '@tencent/tea-component';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import { validateEmail } from '@src/utils/FormUtils';
import {
  createCustomBlacklist,
  getCOSConf,
  uploadCustomBlacklistByCOS,
} from '../actions';
import { app } from '@tencent/tea-app';
import { t, Trans, Slot } from '@tea/app/i18n';
import React from 'react';
import { getStatus } from '@src/utils/formFn';
import _ from 'lodash';
import { disabledDate, validateDate } from './const';
import {
  createCOSInstance,
  getCurrentLanguage,
  LANGUAGE_TYPE,
} from '@src/utils/CommonUtils';
import { SingleFileUpload } from './SingleFileUpload';
import moment from 'moment';

interface FormType {
  emails: string;
  isPermanent: string;
  expireDate: string;
  method: string;
}

const initialValues = {
  emails: '',
  isPermanent: '0',
  expireDate: '',
  method: '0',
};

const validateEmails = (emails: string, method) => {
  if (method === '0') {
    if (!emails) {
      return t('收件邮箱地址为必填');
    }
    return;
  }
  if (!emails?.length) {
    return t('收件邮箱地址为必填');
  }
  const _formatArr = _.compact(emails.split('\n'));
  const errIndex = _.findIndex(_formatArr, (v) => validateEmail(v));
  if (errIndex !== -1) {
    return t('第{{errIndex}}个邮箱地址格式错误', {
      errIndex: errIndex + 1,
    });
  }
};

const CreateCustomBlockDialog = ({
  onSuccess,
  dialogRef,
}: {
  onSuccess: () => void;
  dialogRef: DialogRef;
}) => {
  const [visible, setShowState] = useDialog(dialogRef);
  const [fileValidateInfo, setFileValidateInfo] = useState<{
    [K in string]: number;
  }>({});
  const { form, handleSubmit, validating, submitting } = useForm<FormType>({
    onSubmit: async (values: FormType) => {
      try {
        if (values.method === '0') {
          await uploadCustomBlacklistByCos(values);
        } else {
          await createCustomBlacklist({
            Emails: _.compact(values.emails.split('\n')),
            ExpireDate:
              values.isPermanent === '1'
                ? ''
                : moment(values.expireDate).format('YYYY-MM-DD'),
          });
        }
        app.tips.success(t('操作成功'));
        setShowState(false);
        onSuccess();
      } catch (error) {
        console.log(error);
      }
    },
    initialValues,
    validate: (values) => {
      return {
        emails: validateEmails(values.emails, values.method),
        expireDate: validateDate(values.expireDate, values.isPermanent),
      };
    },
  });
  const uploadCustomBlacklistByCos = async (values: any) => {
    const { hide } = message.loading({
      content: t('导入中...'),
    });
    const file = values.emails;
    const fileName = `${file.name.split('.')[0]}_${Date.now()}.csv`;
    try {
      const { Region, Credentials, BucketName } = await getCOSConf({
        FileName: fileName,
        Type: 1,
      });

      const cos = createCOSInstance(Credentials);
      return cos
        .putObject({
          Region,
          Body: file,
          Key: fileName,
          Bucket: BucketName,
        })
        .then(() =>
          uploadCustomBlacklistByCOS({
            FileName: fileName,
            ExpireDate:
              values.isPermanent === '1'
                ? ''
                : moment(values.expireDate).format('YYYY-MM-DD'),
          }),
        );
    } catch (error) {
    } finally {
      hide();
    }
  };
  const methodField = useField('method', form);
  const emailField = useField('emails', form);
  const expireDateField = useField('expireDate', form);
  const isPermanentField = useField('isPermanent', form);

  const count =
    methodField.input.value === '0'
      ? 0
      : _.compact(emailField.input.value.split('\n')).length;

  useEffect(() => {
    if (visible) {
      setFileValidateInfo(null);
      form.restart({
        method: '0',
        emails: '',
        isPermanent: '0',
      });
    }
  }, [form, visible]);

  const onClose = () => {
    setShowState(false);
  };

  return (
    <Modal
      visible={visible}
      size="500"
      onClose={onClose}
      caption={t('新增自定义黑名单')}
    >
      <Modal.Body>
        <Form>
          <Form.Item
            label={t('录入方式')}
            showStatusIcon={false}
            status={getStatus(methodField.meta, validating)}
            message={
              getStatus(methodField.meta, validating) === 'error' &&
              methodField.meta.error
            }
          >
            <Radio.Group
              {...methodField.input}
              onChange={(v) => {
                methodField.input.onChange(v);
                form.change('emails', '');
              }}
            >
              <Radio name="0">{t('批量上传')}</Radio>
              <Radio name="1">{t('手动输入')}</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label={
              methodField.input.value === '0' ? t('文件') : t('收件邮箱地址')
            }
            showStatusIcon={false}
            status={getStatus(emailField.meta, validating)}
            message={
              getStatus(emailField.meta, validating) === 'error' &&
              emailField.meta.error
            }
            extra={
              <>
                {methodField.input.value === '1' ? (
                  <>
                    <div>
                      <Trans>
                        使用回车键换行，一行视为一个收件邮箱地址，最多可输入100个，共输入了
                        <Text theme="danger">
                          <Slot content={count} />
                        </Text>
                        个邮箱地址。
                      </Trans>
                    </div>
                    <div>{t('收件邮箱地址示例：<EMAIL>')}</div>
                  </>
                ) : (
                  <div>
                    {fileValidateInfo && (
                      <div style={{ display: 'flex', flexDirection: 'column' }}>
                        <Text theme="danger">
                          {t(
                            '原文件：{{TotalCount}}个收件邮箱地址（不包含空行）',
                            { TotalCount: fileValidateInfo.TotalCount },
                          )}
                        </Text>
                        <Text theme="danger">
                          {t(
                            '实际上传：{{ValidCount}}个收件邮箱地址（存在“重复邮箱”、“邮箱地址格式不对”，已剔除）',
                            { ValidCount: fileValidateInfo.ValidCount },
                          )}
                        </Text>
                      </div>
                    )}
                    <Trans>
                      单次支持上传50万个邮箱，请上传csv格式文件。请根据模板要求填入收件邮箱地址
                      <a
                        href={
                          getCurrentLanguage() === LANGUAGE_TYPE.EN
                            ? 'https://sescache.intl.tencent-cloud.com/sample/custom_blacklist_sample_EN.csv'
                            : 'https://sescache.intl.tencent-cloud.com/sample/custom_blacklist_sample.csv'
                        }
                        target="_self"
                      >
                        下载标准模板
                      </a>
                    </Trans>
                  </div>
                )}
              </>
            }
          >
            {methodField.input.value === '1' && (
              <Input.TextArea {...emailField.input} />
            )}
            {methodField.input.value === '0' && (
              <SingleFileUpload
                onChange={(v, validateInfo) => {
                  setFileValidateInfo(validateInfo);
                  emailField.input.onChange(v);
                }}
                maxSize={20 * 1024 * 1024}
              />
            )}
          </Form.Item>
          <Form.Item
            showStatusIcon={false}
            label={t('是否永久有效')}
            status={getStatus(isPermanentField.meta, validating)}
            message={
              getStatus(isPermanentField.meta, validating) === 'error' &&
              isPermanentField.meta.error
            }
          >
            <Segment
              {...isPermanentField.input}
              options={[
                { text: t('是'), value: '1' },
                { text: t('否'), value: '0' },
              ]}
            />
          </Form.Item>
          {isPermanentField.input.value === '0' && (
            <Form.Item
              showStatusIcon={false}
              label={t('失效日期')}
              status={getStatus(expireDateField.meta, validating)}
              message={
                getStatus(expireDateField.meta, validating) === 'error' &&
                expireDateField.meta.error
              }
            >
              <DatePicker
                {...expireDateField.input}
                onChange={(v) => expireDateField.input.onChange(moment(v))}
                format="YYYY-MM-DD"
                disabledDate={disabledDate}
              />
            </Form.Item>
          )}
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          loading={submitting}
          type="primary"
          disabled={validating}
          onClick={() => {
            handleSubmit();
          }}
        >
          {t('确定')}
        </Button>
        <Button type="weak" htmlType="button" onClick={onClose}>
          {t('取消')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateCustomBlockDialog;
