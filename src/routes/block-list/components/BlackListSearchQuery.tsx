import React, { useEffect } from 'react';
import { Button, Form, Justify } from '@tea/component';
import { useField, FormRenderProps } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import './style.less';
import { SearchBox } from '@tencent/tea-component';
import { RangePicker } from '@tea/component/datepicker/RangePicker';

interface Props {
  formRenderProps: FormRenderProps<any>;
  activeId: string;
  receive?: boolean;
}

const BlackListSearchQuery: React.FC<Props> = (props) => {
  const { formRenderProps, activeId, receive = false } = props;
  const { form, handleSubmit, validating, submitting } = formRenderProps;
  const { initialValues } = formRenderProps;
  const timeRange = useField('timeRange', form);
  const receiveEmailAddress = useField('emailAddress', form);

  return (
    <form className="stats-search-query">
      {/* <Form
        layout="inline"
        style={{
          backgroundColor: 'blue',
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        {activeId === 'self' && (
          <Form.Item label={t('日期范围')}>
            <RangePicker {...timeRange.input} />
          </Form.Item>
        )}
        <Form.Item
          label={receive ? t('收件邮箱地址') : t('邮箱地址')}
          style={{
            backgroundColor: 'red',
          }}
        >
          <SearchBox
            hideButton={false}
            size="l"
            {...receiveEmailAddress.input}
            placeholder={
              activeId === 'self'
                ? t('请输入邮箱地址搜索')
                : t('请输入完整的邮箱地址搜索')
            }
            onSearch={(value) => {
              handleSubmit();
            }}
          />
        </Form.Item>
      </Form> */}
      <div
        style={{
          display: 'flex',
          gap: '20px',
        }}
      >
        {activeId === 'self' && (
          <div
            style={{
              display: 'flex',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <div>{t('日期范围')}</div>
            <RangePicker {...timeRange.input} />
          </div>
        )}
        <div
          style={{
            display: 'flex',
            gap: '10px',
            alignItems: 'center',
          }}
        >
          <div>{receive ? t('收件邮箱地址') : t('邮箱地址')} </div>

          <SearchBox
            hideButton={false}
            size="l"
            {...receiveEmailAddress.input}
            placeholder={
              activeId === 'self'
                ? t('请输入邮箱地址搜索')
                : t('请输入完整的邮箱地址搜索')
            }
            onSearch={(value) => {
              handleSubmit();
            }}
          />
        </div>
      </div>
    </form>
  );
};

export default BlackListSearchQuery;
