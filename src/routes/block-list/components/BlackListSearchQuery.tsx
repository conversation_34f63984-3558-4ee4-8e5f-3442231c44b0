import React from 'react';
import { useField, FormRenderProps } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import './style.less';
import { SearchBox } from '@tencent/tea-component';
import { RangePicker } from '@tea/component/datepicker/RangePicker';
import moment from 'moment';

interface Props {
  formRenderProps: FormRenderProps<any>;
  activeId: string;
  receive?: boolean;
}

const BlackListSearchQuery: React.FC<Props> = (props) => {
  const { formRenderProps, activeId, receive = false } = props;
  const { form, handleSubmit } = formRenderProps;
  const timeRange = useField('timeRange', form);
  const receiveEmailAddress = useField('emailAddress', form);

  // 当activeId变化时，强制清空搜索框
  React.useEffect(() => {
    receiveEmailAddress.input.onChange('');
    timeRange.input.onChange([moment().add(-15, 'd'), moment()]);
  }, [activeId, receiveEmailAddress.input, timeRange.input]);





  return (
    <form className="stats-search-query">
      <div
        style={{
          display: 'flex',
          gap: '20px',
        }}
      >
        {activeId === 'self' && (
          <div
            style={{
              display: 'flex',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <div>{t('日期范围')}</div>
            <RangePicker
              {...timeRange.input}
            />
          </div>
        )}
        <div
          style={{
            display: 'flex',
            gap: '10px',
            alignItems: 'center',
          }}
        >
          <div>{receive ? t('收件邮箱地址') : t('邮箱地址')} </div>

          <SearchBox
            hideButton={false}
            size="l"
            {...receiveEmailAddress.input}
            placeholder={
              activeId === 'self'
                ? t('请输入邮箱地址搜索')
                : t('请输入完整的邮箱地址搜索')
            }
            onSearch={() => {
              // 只在点击搜索按钮时触发搜索
              handleSubmit();
            }}
            onClear={() => {
              // 清空搜索框
              receiveEmailAddress.input.onChange('');
            }}
          />
        </div>
      </div>
    </form>
  );
};

export default BlackListSearchQuery;
