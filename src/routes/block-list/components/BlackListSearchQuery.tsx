import React from 'react';
import { useField, FormRenderProps } from 'react-final-form-hooks';
import { t } from '@tea/app/i18n';
import './style.less';
import { SearchBox } from '@tencent/tea-component';
import { RangePicker } from '@tea/component/datepicker/RangePicker';

interface Props {
  formRenderProps: FormRenderProps<any>;
  activeId: string;
  receive?: boolean;
  onSearch?: (searchValue: string) => void; // 添加搜索回调，参照独立IP
  onTimeRangeChange?: (timeRange: any[]) => void; // 添加时间范围变化回调
}

const BlackListSearchQuery: React.FC<Props> = (props) => {
  const { formRenderProps, activeId, receive = false, onSearch, onTimeRangeChange } = props;
  const { form, handleSubmit } = formRenderProps;
  const timeRange = useField('timeRange', form);
  const receiveEmailAddress = useField('emailAddress', form);

  // 处理搜索，参照独立IP的实现
  const handleSearchSubmit = (value: string) => {
    // 更新表单字段值
    receiveEmailAddress.input.onChange(value);

    // 如果有外部搜索处理函数，使用它（这会重置页码）
    if (onSearch) {
      onSearch(value);
    } else {
      // 否则使用原有的表单提交逻辑
      handleSubmit();
    }
  };

  // 处理时间范围变化，也需要重置页码
  const handleTimeRangeChange = (value: any[]) => {
    // 更新表单字段值
    timeRange.input.onChange(value);

    // 如果有外部时间范围变化处理函数，使用它（这会重置页码）
    if (onTimeRangeChange) {
      onTimeRangeChange(value);
    } else {
      // 否则使用原有的表单提交逻辑
      handleSubmit();
    }
  };

  return (
    <form className="stats-search-query">
      <div
        style={{
          display: 'flex',
          gap: '20px',
        }}
      >
        {activeId === 'self' && (
          <div
            style={{
              display: 'flex',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <div>{t('日期范围')}</div>
            <RangePicker
              {...timeRange.input}
              onChange={handleTimeRangeChange}
            />
          </div>
        )}
        <div
          style={{
            display: 'flex',
            gap: '10px',
            alignItems: 'center',
          }}
        >
          <div>{receive ? t('收件邮箱地址') : t('邮箱地址')} </div>

          <SearchBox
            hideButton={false}
            size="l"
            {...receiveEmailAddress.input}
            placeholder={
              activeId === 'self'
                ? t('请输入邮箱地址搜索')
                : t('请输入完整的邮箱地址搜索')
            }
            onSearch={(value) => {
              handleSubmit();
            }}
          />
        </div>
      </div>
    </form>
  );
};

export default BlackListSearchQuery;
