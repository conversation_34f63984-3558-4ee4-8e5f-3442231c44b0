import React from 'react';
import { t } from '@tea/app/i18n';
import { Modal, Button } from '@tencent/tea-component';

interface ModalProps {
  visible: boolean;
  deleteType?: 'delete' | 'add' | 'unsubscribe';
  onConfirm: () => void;
  onCancel: () => void;
  loading: boolean;
  dataToDelete: Array<any>;
}
const ModalBasic: React.FC<ModalProps> = ({
  visible,
  deleteType,
  onConfirm,
  onCancel,
  loading,
  dataToDelete,
}) => {
  const nums = dataToDelete.length;
  const emailAddress = dataToDelete[0];
  console.log('hahahaha', dataToDelete);
  let content = '';
  if (nums === 1) {
    content =
      deleteType === 'delete'
        ? t('确认移除邮箱地址') + emailAddress + '?'
        : deleteType === 'add'
        ? t('确认添加邮箱地址') + emailAddress + t('至白名单？')
        : t('确认移除收件邮箱地址') + emailAddress + '?';
  } else {
    content =
      deleteType === 'delete'
        ? t('确认移除已选的') + nums + t('个邮箱地址？')
        : '';
    // 'add' 和 'unsubscribe' 没有多选
  }
  return (
    <Modal visible={visible} onClose={onCancel} caption={content}>
      <Modal.Body style={{ color: 'gray' }}>
        {deleteType === 'delete'
          ? t(
              '移除后，平台将恢复向该邮箱发送邮件。请务必确认邮箱有效，避免因无效地址增多导致发信退信率上升，影响投递率和域名信誉。请谨慎操作！',
            )
          : deleteType === 'add'
          ? t(
              '添加至白名单后，平台将恢复向该邮箱发送邮件。请务必确认邮箱有效，避免因无效地址增多导致发信退信率上升，影响投递率和域名信誉。请谨慎操作！',
            )
          : t('移除后，平台将恢复向该邮箱发送邮件。请谨慎操作！')}
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={onConfirm} loading={loading}>
          {deleteType === 'add' ? t('添加') : t('移除')}
        </Button>
        <Button type="weak" onClick={onCancel}>
          {t('取消')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ModalBasic;
