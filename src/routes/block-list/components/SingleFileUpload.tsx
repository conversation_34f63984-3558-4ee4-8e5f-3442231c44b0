import React, { useState } from 'react';
import {
  Upload,
  Button,
  Form,
  UploadProps,
  message,
  Text,
} from '@tencent/tea-component';
import { t } from '@tea/app/i18n';
import _ from 'lodash';
import { useCss } from 'react-use';
import { emailRules } from '@src/constants/defaultConstant';
import { readFileAsync } from '@src/utils/CommonUtils';

const MAX_COUNT = 50 * 10000;
type Props = Pick<UploadProps, 'maxSize'> & {
  value?: string;
  onChange: (value: string, validateInfo?: { [K in string]: number }) => void;
  maxSize: number;
};

export const SingleFileUpload = (props: Props) => {
  const { onChange, maxSize } = props;
  const [status, setStatus] = useState<
    'error' | 'validating' | 'success' | undefined
  >();
  const [filename, setFileName] = useState<string>('');

  const className = useCss({
    '.app-ses-form-upload-drag .app-ses-form-upload__inner': {
      minWidth: '250px',
    },
  });

  const beforeUpload = async (file, _fileList, isAccepted) => {
    try {
      if (file.size > maxSize) {
        throw new Error(t('导入文件大小超过限制'));
      }
      if (!isAccepted) {
        throw new Error(t('仅支持导入.csv格式的文件，请重试。'));
      }
      const content = await readFileAsync(file);
      const rowsData = content.split(/\r\n|\r|\n/).filter((r) => r);

      if (rowsData.length === 1 && !emailRules.test(rowsData[0])) {
        throw new Error(t('导入文件无有效邮箱'));
      }

      if (rowsData.length > MAX_COUNT + 1) {
        throw new Error(t('导入的邮箱数量不能超过50万条。'));
      }
      const deleteTitleRowsData = rowsData
        .slice(1)
        .filter((r) => r.trim() !== '');
      const emailSet = new Set(deleteTitleRowsData);
      const validateInfo = {
        TotalCount: deleteTitleRowsData.length,
        RepeatCount: deleteTitleRowsData.length - emailSet.size,
        InvalidCount: 0,
        TooLongCount: 0,
        ValidCount: 0,
      };
      emailSet.forEach((email) => {
        if (email.length > 300) {
          validateInfo.TooLongCount += 1;
        } else if (!emailRules.test(email)) {
          validateInfo.InvalidCount += 1;
        } else {
          validateInfo.ValidCount += 1;
        }
      });

      onChange(file, validateInfo);
      setFileName(file.name);
      setStatus('success');
    } catch (error) {
      setStatus('error');
      message.warning({ content: error.message });
    }
    return false;
  };

  const handleAbort = () => {
    onChange('');
    setFileName('');
    setStatus(undefined);
  };

  return (
    <Form.Control status={status} showStatusIcon={false}>
      <Upload
        action={'upload'}
        accept=".csv"
        maxSize={maxSize}
        beforeUpload={beforeUpload}
        className={className}
      >
        {({ open }) => (
          <Upload.Dragger
            filename={filename}
            button={
              status === 'validating' ? (
                <Button type="link" onClick={handleAbort}>
                  {t('取消上传')}
                </Button>
              ) : (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      handleAbort();
                      open();
                    }}
                  >
                    {t('重新上传')}
                  </Button>
                  <Button
                    type="link"
                    style={{ marginLeft: 8 }}
                    onClick={handleAbort}
                  >
                    {t('删除')}
                  </Button>
                </>
              )
            }
          >
            <Button onClick={open} type="link">
              {t('点击上传')}
            </Button>
            <Text theme="weak">{t('/拖拽到此区域')}</Text>
          </Upload.Dragger>
        )}
      </Upload>
    </Form.Control>
  );
};
