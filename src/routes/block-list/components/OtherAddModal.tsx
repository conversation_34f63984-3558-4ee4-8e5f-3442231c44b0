import React from 'react';
import { t } from '@tea/app/i18n';
import { Modal, Button, Text } from '@tencent/tea-component';

interface ModalProps {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  loading: boolean;
  dataToDelete: Array<any>;
}
const OtherAddModal: React.FC<ModalProps> = ({
  visible,
  onConfirm,
  onCancel,
  loading,
  dataToDelete,
}) => {
  const nums = dataToDelete.length;
  const emailAddress = dataToDelete[0];
  console.log('hahahaha', dataToDelete);
  let content =
    nums === 1
      ? t('确认添加邮箱地址 {{email}} 至白名单？', { email: emailAddress })
      : '';

  return (
    <Modal visible={visible} onClose={onCancel} caption={content}>
      <Modal.Body>
        <Text theme="label">
          {t(
            '添加至白名单后，平台将恢复向该邮箱发送邮件。请务必确认邮箱有效，避免因无效地址增多导致发信退信率上升，影响投递率和域名信誉。请谨慎操作！',
          )}
        </Text>
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={onConfirm} loading={loading}>
          {t('添加')}
        </Button>
        <Button type="weak" onClick={onCancel}>
          {t('取消')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default OtherAddModal;
