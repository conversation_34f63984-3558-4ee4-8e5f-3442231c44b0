import React, { useCallback, useEffect, useState, useRef } from 'react';
import { t } from '@tea/app/i18n';
import {
  Card,
  Table,
  Tabs,
  TabPanel,
  Alert,
  Modal,
  Justify,
} from '@tencent/tea-component';
import { Bubble, Button, Icon, message } from '@tea/component';
import {
  deleteBlackList,
  getGlobalBlacklistAddress,
  searchBlackList,
  createWhitelistFromBlacklist,
} from '@src/routes/block-list/actions';
import moment from 'moment';
import BlackListSearchQuery from '@src/routes/block-list/components/BlackListSearchQuery';
import { useTopTip } from './useTopTip';
import { useForm } from 'react-final-form-hooks';
import { selectable } from '@tea/component/table/addons';

import ModalBasic from './ModalBasic';

const { pageable } = Table.addons;

const initialQueryValues = {
  emailAddress: '',
  timeRange: [moment().add(-2, 'w'), moment()],
};

export function BlackListTable() {
  const [listSelf, setListSelf] = useState([]);
  const [listOther, setListOther] = useState([]);
  const [total, setTotal] = useState(0);
  const [listLoading, setListLoading] = useState(true);
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });
  const queryValues = initialQueryValues;
  const subTab = [
    { id: 'other', label: t('拦截查询') },
    { id: 'self', label: t('我的触发拦截') },
  ];
  const [activeId, setActiveId] = useState('other');
  const activeIdRef = useRef(activeId);
  useEffect(() => {
    activeIdRef.current = activeId;
    setListSelf([]);
    setListOther([]);
    setTotal(0);
    formRenderProps.form.initialize(queryValues);
    handleSubmit();
  }, [activeId]);

  const topTip = useTopTip({
    record: activeIdRef.current === 'self' ? listSelf : listOther,
    loading: listLoading,
    size: 'l',
    isDesc: true,
    icon: activeIdRef.current === 'self' ? 'blank' : 'search',
    title: activeIdRef.current === 'self' ? t('暂无数据') : t('暂无查询结果'),
    description:
      activeIdRef.current === 'self'
        ? t('默认展示近15天数据，更多数据请调整日期范围后重新搜索')
        : t('该邮箱地址不在平台无效地址列表内，请重新搜索'),
  });
  function statsQuery(values, pageData) {
    const currentActiveId = activeIdRef.current;
    setListSelf([]);
    setListOther([]);
    console.log('activeId=', currentActiveId);
    console.log('pageData=', pageData);
    const { domain, timeRange = [], emailAddress } = values;
    const [StartDate, EndDate] = timeRange.map((one) =>
      one.format('YYYY-MM-DD'),
    );
    const data = {
      StartDate,
      EndDate,
      Domain: domain,
      EmailAddress: emailAddress,
      Limit: pageData.pageSize,
      Offset: (pageData.pageIndex - 1) * pageData.pageSize,
    };
    setListLoading(true);
    if (currentActiveId === 'self') {
      console.log(currentActiveId);
      console.log('self submit');
      return searchBlackList(data)
        .then((res) => {
          if (!res) {
            setListLoading(false);
            return;
          }
          console.log('searchQuery Over', res);
          const { BlackList = [], TotalCount } = res;

          setListSelf(BlackList);
          setTotal(TotalCount);
        })
        .catch((e) => {
          console.log('searchQuery error', e);
        })
        .finally(() => {
          setListLoading(false);
        });
    } else if (currentActiveId === 'other') {
      console.log(currentActiveId);
      console.log('other submit');
      return getGlobalBlacklistAddress(data)
        .then((res) => {
          console.log('getGlobalBlacklistAddress Over', res);
          const { BlackList } = res; // 这个接口不返回totalCount，并且blacklist不是数组
          if (!BlackList.EmailAddress) {
            return;
          }
          setListOther([BlackList]);
        })
        .catch((e) => {
          // if (e.code === 'OperationDenied.ReceiverNotExist') {
          //   return;
          // }
        })
        .finally(() => {
          setListLoading(false);
        });
    }
  }

  const onSearchSubmit = useCallback(
    (values) => {
      console.log('onSearchSubmit', values, pageData);
      return statsQuery(values, pageData);
    },
    [pageData],
  );

  const formRenderProps = useForm({
    onSubmit: onSearchSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: queryValues,
    validate: ({ timeRange }: any) => ({
      receiveEmailAddress: undefined,
      timeRange: !timeRange ? t('请选择时间段') : undefined,
    }),
  });
  const { handleSubmit } = formRenderProps;

  const [visible, setVisible] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [dataToDelete, setDataToDelete] = useState([]);
  const dataToDeleteRef = useRef([]);
  const [deleteType, setDeleteType] = useState<'delete' | 'add'>('delete');
  const onDelete = useCallback((data = []) => {
    console.log('onDelete', data);
    dataToDeleteRef.current = data;
    setDataToDelete(data);
    setVisible(true);
  }, []);

  const closeModal = () => {
    setVisible(false);
  };

  const onConfirm = useCallback(() => {
    console.log('Confirm action triggered', deleteType);
    setDeleteLoading(true);
    if (deleteType === 'delete') {
      const currentData = dataToDeleteRef.current;
      console.log(deleteType);
      console.log('self delete dataToDelete', currentData);

      return deleteBlackList(currentData)
        .then((res) => {
          console.log('onDelete Over', res);
          message.success({ content: t('移除成功') });
          return handleSubmit();
        })
        .catch(() => {})
        .finally(() => {
          setDeleteLoading(false);
          closeModal();
        });
    } else if (deleteType === 'add') {
      const currentData = dataToDeleteRef.current;
      console.log(deleteType);
      console.log('self delete dataToDelete', currentData);

      return createWhitelistFromBlacklist(currentData)
        .then((res) => {
          console.log('onDelete Over', res);
          message.success({ content: t('加入白名单成功') });
          return handleSubmit();
        })
        .catch(() => {
          message.error({ content: t('加入白名单失败') });
        })
        .finally(() => {
          setDeleteLoading(false);
          closeModal();
        });
    }
  }, [deleteType]);

  // 翻页
  const onPagingChange = (data) => {
    setPageData(data);
  };
  useEffect(() => {
    handleSubmit();
  }, [pageData]);

  const [selectedKeys, setSelectedKeys] = useState([]);
  const deleteBtnDisabled = selectedKeys.length === 0;
  const deleteMany = useCallback(() => {
    setDeleteType('delete');
    onDelete(selectedKeys);
  }, [selectedKeys]);
  return (
    <div className="black-list-table">
      <div
        className="flex-one-row"
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'start',
        }}
      >
        <div style={{ display: 'flex', gap: '10px' }}>
          <div>
            <Tabs
              tabs={subTab}
              onActive={(tab) => setActiveId(tab.id)}
              variant="segment"
            >
              <TabPanel id="self"></TabPanel>
              <TabPanel id="other"></TabPanel>
            </Tabs>
          </div>
          {activeId === 'self' && (
            <Button
              // className="my-btn"
              // type="primary"
              // htmlType="submit"
              disabled={deleteBtnDisabled}
              onClick={deleteMany}
              loading={deleteLoading}
            >
              {t('批量删除')}
            </Button>
          )}
        </div>
        <div>
          <BlackListSearchQuery
            formRenderProps={formRenderProps}
            activeId={activeId}
          />
        </div>

        {/* </Col>*/}
        {/* <Col>*/}
        {/* </Col>*/}
      </div>
      {activeId === 'other' && (
        <Alert type="white">
          <div>
            {t(
              '本账号下发信触发的无效地址拦截支持"移除"；其他用户账号触发的无效地址拦截只能“加白”。',
            )}
          </div>
        </Alert>
      )}
      {activeId === 'self' && (
        <Card>
          <Table
            verticalTop
            records={listSelf}
            recordKey="EmailAddress"
            topTip={topTip}
            columns={[
              {
                key: 'EmailAddress',
                header: t('邮箱地址'),
              },
              {
                key: 'BounceTime',
                header: t('弹回时间'),
              },
              {
                key: 'IspDesc',
                header: t('备注'),
              },
              {
                key: 'action',
                width: 140,
                header: t('操作'),
                render: (item) => (
                  <>
                    <Button
                      type="link"
                      onClick={() => {
                        setDeleteType('delete');
                        onDelete([item.EmailAddress]);
                      }}
                    >
                      {t('移除')}
                    </Button>
                  </>
                ),
              },
            ]}
            addons={[
              pageable({
                recordCount: total,
                pageIndex: pageData.pageIndex,
                pageSize: pageData.pageSize,
                pageSizeOptions: [10, 20, 30, 50, 100],
                onPagingChange: (query) => {
                  console.log('onPagingChange', query);
                  // {pageIndex: 2, pageSize: 10}
                  onPagingChange(query);
                },
              }),
              selectable({
                // 选框放在「消息类型」列上
                // targetColumnKey: "name",
                // // 提供子孙关系
                // relations,
                // // 禁用全选
                // all: false,
                // // 已选中的键值
                // value: selectedKeys,
                // 选中键值发生变更
                onChange: (value) => {
                  console.log('selectable value onChange', value);
                  setSelectedKeys(value);
                },
              }),
              // scrollable({ maxHeight: 480, minHeight: 480 }),
            ]}
          />
        </Card>
      )}
      {activeId === 'other' && (
        <Card>
          <Table
            verticalTop
            records={listOther}
            recordKey="EmailAddress"
            topTip={topTip}
            columns={[
              {
                key: 'EmailAddress',
                header: t('邮箱地址'),
              },
              {
                key: 'BounceTime',
                header: t('弹回时间'),
              },
              {
                key: 'IspDesc',
                header: t('备注'),
              },
              {
                key: 'action',
                width: 140,
                header: t('操作'),
                render: (item) => (
                  <>
                    <Button
                      type="link"
                      onClick={() => {
                        const type =
                          item.IsSelfReported === 0 ? 'add' : 'delete';
                        setDeleteType(type);
                        onDelete([item.EmailAddress]);
                      }}
                    >
                      {item.IsSelfReported === 0 ? t('加白') : t('移除')}
                    </Button>
                  </>
                ),
              },
            ]}
          />
        </Card>
      )}
      <ModalBasic
        visible={visible}
        onConfirm={onConfirm}
        onCancel={closeModal}
        loading={deleteLoading}
        dataToDelete={dataToDelete}
        deleteType={deleteType}
      />
    </div>
  );
}
