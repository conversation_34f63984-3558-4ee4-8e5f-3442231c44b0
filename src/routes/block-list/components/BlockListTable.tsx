import React, { useCallback, useEffect, useState, useRef } from 'react';
import { t } from '@tea/app/i18n';
import { Card, Table, Tabs, TabPanel, Alert } from '@tencent/tea-component';
import { Button, message } from '@tea/component';
import {
  deleteBlackList,
  getGlobalBlacklistAddress,
  searchBlackList,
  createWhitelistFromBlacklist,
} from '@src/routes/block-list/actions';
import moment from 'moment';
import BlackListSearchQuery from '@src/routes/block-list/components/BlackListSearchQuery';
import { useTopTip } from './useTopTip';
import { useForm } from 'react-final-form-hooks';
import { selectable } from '@tea/component/table/addons';

import SelfDeleteModal from './SelfDeleteModal';
import OtherAddModal from './OtherAddModal';

const { pageable } = Table.addons;

const initialQueryValues = {
  emailAddress: '',
  timeRange: [moment().add(-15, 'd'), moment()],
};

export function BlackListTable() {
  const [listSelf, setListSelf] = useState([]);
  const [listOther, setListOther] = useState([]);
  const [total, setTotal] = useState(0);
  const [listLoading, setListLoading] = useState(true);
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 1,
  });
  const queryValues = initialQueryValues;
  const subTab = [
    { id: 'other', label: t('拦截查询') },
    { id: 'self', label: t('我的触发拦截') },
  ];
  const [activeId, setActiveId] = useState('other');
  useEffect(() => {
    setListSelf([]);
    setListOther([]);
    setTotal(0);
    formRenderProps.form.initialize(queryValues);
    handleSubmit();
  }, [activeId]);
  const [otherTipTitle, setOtherTipTitle] = useState(t('请输入精准的邮箱地址'));
  const [selfTipDescription, setSelfTipDescription] = useState(
    t('默认展示近15天数据，更多数据请调整日期范围后重新搜索'),
  );
  const [otherTipDescription, setOtherTipDescription] = useState(
    t('可查询该邮箱地址是否被平台判定为无效地址'),
  );
  const topTip = useTopTip({
    record: activeId === 'self' ? listSelf : listOther,
    loading: listLoading,
    size: 'l',
    isDesc: true,
    icon: activeId === 'self' ? 'blank' : 'search',
    title: activeId === 'self' ? t('暂无数据') : otherTipTitle,
    description: activeId === 'self' ? selfTipDescription : otherTipDescription,
  });
  const statsQuery = useCallback(
    (values) => {
      setListSelf([]);
      setListOther([]);
      console.log('activeId=', activeId);
      console.log('pageData=', pageData);
      const { domain, timeRange = [], emailAddress } = values;
      console.log('timeRange=', timeRange);
      const [startMoment, endMoment] = timeRange;
      const diffDays =
        startMoment && endMoment ? endMoment.diff(startMoment, 'days') : 0;
      if (diffDays > 15) {
        setSelfTipDescription(
          t('查询成功，当前查询条件暂无数据，请修改查询条件'),
        );
      } else {
        setSelfTipDescription(
          t('默认展示近15天数据，更多数据请调整日期范围后重新搜索'),
        );
      }
      const [StartDate, EndDate] = timeRange.map((date) =>
        date.format('YYYY-MM-DD'),
      );
      const data = {
        StartDate,
        EndDate,
        Domain: domain,
        EmailAddress: emailAddress,
        Limit: pageData.pageSize,
        Offset: (pageData.pageIndex - 1) * pageData.pageSize,
      };
      setListLoading(true);
      if (activeId === 'self') {
        console.log(activeId);
        console.log('self submit', data);
        return searchBlackList(data)
          .then((res) => {
            if (!res) {
              setListLoading(false);
              return;
            }
            console.log('searchQuery Over', res);
            const { BlackList = [], TotalCount } = res;

            setListSelf(BlackList);
            setTotal(TotalCount);
          })
          .catch((e) => {
            console.log('searchQuery error', e);
          })
          .finally(() => {
            setListLoading(false);
          });
      } else if (activeId === 'other') {
        console.log(activeId);
        console.log('other submit');
        if (emailAddress.trim() === '') {
          setOtherTipTitle(t('请输入精准的邮箱地址'));
          setOtherTipDescription(t('可查询该邮箱地址是否被平台判定为无效地址'));
        } else {
          setOtherTipTitle(t('暂无查询结果'));
          setOtherTipDescription(
            t('该邮箱地址不在平台无效地址列表内，请重新搜索'),
          );
        }
        return getGlobalBlacklistAddress(data)
          .then((res) => {
            console.log('getGlobalBlacklistAddress Over', res);
            const { BlackList } = res; // 这个接口不返回totalCount，并且blacklist不是数组
            if (!BlackList.EmailAddress) {
              return;
            }
            setListOther([BlackList]);
          })
          .catch((e) => {
            // if (e.code === 'OperationDenied.ReceiverNotExist') {
            //   return;
            // }
          })
          .finally(() => {
            setListLoading(false);
          });
      }
    },
    [activeId, pageData],
  );

  // const onSearchSubmit = useCallback(
  //   (values) => {
  //     console.log('onSearchSubmit', values, pageData);
  //     return statsQuery(values);
  //   },
  //   [pageData, activeId],
  // );
  const onSearchSubmit = useCallback(
    (values) => {
      setPageData({
        pageIndex: 1,
        pageSize: pageData.pageSize,
        value: values,
      });
      console.log('onSearchSubmit', pageData);
      return statsQuery(values);
    },
    [pageData, setPageData],
  );

  const formRenderProps = useForm({
    onSubmit: onSearchSubmit,
    /**
     * 默认为 shallowEqual
     * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
     * useEffect(() => form.initialize({ }), []);
     */
    initialValuesEqual: () => true,
    initialValues: queryValues,
    validate: ({ timeRange }: any) => ({
      receiveEmailAddress: undefined,
      timeRange: !timeRange ? t('请选择时间段') : undefined,
    }),
  });
  const { handleSubmit } = formRenderProps;

  const [visibleSelf, setVisibleSelf] = useState(false);
  const [visibleOther, setVisibleOther] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [dataToDelete, setDataToDelete] = useState([]);
  const [deleteType, setDeleteType] = useState<'delete' | 'add'>('delete');
  const deleteTypeRef = useRef(deleteType);
  const onDelete = useCallback((data = []) => {
    setDataToDelete(data);
    if (deleteTypeRef.current === 'delete') {
      setVisibleSelf(true);
    } else if (deleteTypeRef.current === 'add') {
      setVisibleOther(true);
    }
  }, []);

  const onConfirm = useCallback(() => {
    console.log('Confirm action triggered', deleteType);
    setDeleteLoading(true);
    if (deleteType === 'delete') {
      console.log(deleteType);
      console.log('self delete dataToDelete', dataToDelete);

      return deleteBlackList(dataToDelete)
        .then((res) => {
          console.log('onDelete Over', res);
          message.success({ content: t('移除成功') });
          return handleSubmit();
        })
        .catch(() => {})
        .finally(() => {
          setDeleteLoading(false);
          setVisibleSelf(false);
        });
    } else if (deleteType === 'add') {
      console.log(deleteType);
      console.log('self delete dataToDelete', dataToDelete);

      return createWhitelistFromBlacklist(dataToDelete)
        .then((res) => {
          console.log('onDelete Over', res);
          message.success({ content: t('加入白名单成功') });
          return handleSubmit();
        })
        .catch(() => {
          message.error({ content: t('加入白名单失败') });
        })
        .finally(() => {
          setDeleteLoading(false);
          setVisibleSelf(false);
        });
    }
  }, [deleteType]);

  // 翻页
  const onPagingChange = (data) => {
    setPageData(data);
  };
  useEffect(() => {
    handleSubmit();
  }, [pageData]);

  const [selectedKeys, setSelectedKeys] = useState([]);
  const deleteBtnDisabled = selectedKeys.length === 0;
  const deleteMany = useCallback(() => {
    setDeleteType('delete');
    deleteTypeRef.current = 'delete';
    onDelete(selectedKeys);
  }, [selectedKeys]);
  return (
    <div>
      <Alert>
        <div>
          {t(
            '1、平台会对实际不存在、不可用或者长期不活跃的无效地址主动进行拦截，180天后释放。',
          )}
        </div>
        <div>
          {t(
            '2、若您确认邮箱地址有效，本账号下发信触发的无效地址拦截支持"移除"；其他用户账号触发的无效地址拦截只能“加白”。',
          )}
        </div>
        <div>
          {t(
            '注意：无效地址率过高会被认为“盲目群发垃圾邮件”，影响邮件到达率和发信域名信誉度，请谨慎操作。',
          )}
        </div>
      </Alert>
      <div
        className="flex-one-row"
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'start',
        }}
      >
        <div style={{ display: 'flex', gap: '10px' }}>
          <div>
            <Tabs
              tabs={subTab}
              onActive={(tab) => setActiveId(tab.id)}
              variant="segment"
            >
              <TabPanel id="self"></TabPanel>
              <TabPanel id="other"></TabPanel>
            </Tabs>
          </div>
          {activeId === 'self' && (
            <Button
              disabled={deleteBtnDisabled}
              onClick={deleteMany}
              loading={deleteLoading}
            >
              {t('批量删除')}
            </Button>
          )}
        </div>
        <div>
          <BlackListSearchQuery
            formRenderProps={formRenderProps}
            activeId={activeId}
          />
        </div>

        {/* </Col>*/}
        {/* <Col>*/}
        {/* </Col>*/}
      </div>
      {activeId === 'other' && (
        <Alert type="white">
          <div>
            {t(
              '本账号下发信触发的无效地址拦截支持"移除"；其他用户账号触发的无效地址拦截只能“加白”。',
            )}
          </div>
        </Alert>
      )}
      {activeId === 'self' && (
        <Card>
          <Table
            verticalTop
            records={listSelf}
            recordKey="EmailAddress"
            topTip={topTip}
            columns={[
              {
                key: 'EmailAddress',
                header: t('邮箱地址'),
              },
              {
                key: 'BounceTime',
                header: t('弹回时间'),
              },
              {
                key: 'IspDesc',
                header: t('备注'),
              },
              {
                key: 'action',
                width: 140,
                header: t('操作'),
                render: (item) => (
                  <>
                    <Button
                      type="link"
                      onClick={() => {
                        setDeleteType('delete');
                        deleteTypeRef.current = 'delete';
                        onDelete([item.EmailAddress]);
                      }}
                    >
                      {t('移除')}
                    </Button>
                  </>
                ),
              },
            ]}
            addons={[
              pageable({
                recordCount: total,
                pageIndex: pageData.pageIndex,
                pageSize: pageData.pageSize,
                pageSizeOptions: [10, 20, 30, 50, 100],
                onPagingChange: (query) => {
                  console.log('onPagingChange', query);
                  // {pageIndex: 2, pageSize: 10}
                  onPagingChange(query);
                },
              }),
              selectable({
                // 选框放在「消息类型」列上
                // targetColumnKey: "name",
                // // 提供子孙关系
                // relations,
                // // 禁用全选
                // all: false,
                // // 已选中的键值
                // value: selectedKeys,
                // 选中键值发生变更
                onChange: (value) => {
                  console.log('selectable value onChange', value);
                  setSelectedKeys(value);
                },
              }),
              // scrollable({ maxHeight: 480, minHeight: 480 }),
            ]}
          />
        </Card>
      )}
      {activeId === 'other' && (
        <Card>
          <Table
            verticalTop
            records={listOther}
            recordKey="EmailAddress"
            topTip={topTip}
            columns={[
              {
                key: 'EmailAddress',
                header: t('邮箱地址'),
              },
              {
                key: 'BounceTime',
                header: t('弹回时间'),
              },
              {
                key: 'IspDesc',
                header: t('备注'),
              },
              {
                key: 'action',
                width: 140,
                header: t('操作'),
                render: (item) => (
                  <>
                    <Button
                      type="link"
                      onClick={() => {
                        const type =
                          item.IsSelfReported === 0 ? 'add' : 'delete';
                        setDeleteType(type);
                        deleteTypeRef.current = type;
                        onDelete([item.EmailAddress]);
                      }}
                    >
                      {item.IsSelfReported === 0 ? t('加白') : t('移除')}
                    </Button>
                  </>
                ),
              },
            ]}
          />
        </Card>
      )}
      <SelfDeleteModal
        visible={visibleSelf}
        onConfirm={onConfirm}
        onCancel={() => setVisibleSelf(false)}
        loading={deleteLoading}
        dataToDelete={dataToDelete}
      />
      <OtherAddModal
        visible={visibleOther}
        onConfirm={onConfirm}
        onCancel={() => setVisibleOther(false)}
        loading={deleteLoading}
        dataToDelete={dataToDelete}
      />
    </div>
  );
}
