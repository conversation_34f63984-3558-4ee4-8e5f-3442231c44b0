import { useForm, useField } from 'react-final-form-hooks';
import { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Segment,
  DatePicker,
} from '@tencent/tea-component';
import { DialogRef, useDialog } from '@src/routes/global-components/useDialog';
import { validateEmail } from '@src/utils/FormUtils';
import { updateCustomBlackList } from '../actions';
import { app } from '@tencent/tea-app';
import { t } from '@tea/app/i18n';
import React from 'react';
import { getStatus } from '@src/utils/formFn';
import moment, { Moment } from 'moment';
import { disabledDate, validateDate } from './const';

interface FormType {
  id: string;
  email: string;
  isPermanent: string;
  expireDate?: Moment;
}

const initialValues = {
  id: '',
  email: '',
  isPermanent: '',
};

const EditCustomBlockDialog = ({
  onSuccess,
  dialogRef,
}: {
  onSuccess: () => void;
  dialogRef: DialogRef;
}) => {
  const [visible, setShowState, defaultVal] = useDialog<FormType>(dialogRef);
  const { email, expireDate, id } = defaultVal;
  const isPermanent = expireDate ? '0' : '1';

  const { form, handleSubmit, validating, submitting } = useForm<FormType>({
    onSubmit: async (values: FormType, _form) => {
      try {
        await updateCustomBlackList({
          Id: id,
          Email: values.email,
          ExpireDate:
            values.isPermanent === '1'
              ? ''
              : moment(values.expireDate).format('YYYY-MM-DD'),
        });
        app.tips.success(t('操作成功'));
        setShowState(false);
        onSuccess();
      } catch (error) {
        console.log(error);
      }
    },
    initialValues,
    validate: (values) => {
      return {
        email: validateEmail(values.email),
        expireDate: validateDate(values.expireDate, values.isPermanent),
      };
    },
  });

  const emailField = useField('email', form);
  const expireDateField = useField('expireDate', form);
  const isPermanentField = useField('isPermanent', form);

  useEffect(() => {
    if (visible) {
      form.restart({
        id,
        email,
        isPermanent,
        expireDate: expireDate ? moment(expireDate) : null,
      });
    }
  }, [email, expireDate, form, id, isPermanent, visible]);

  const onClose = () => {
    setShowState(false);
  };

  return (
    <Modal
      visible={visible}
      size="s"
      onClose={onClose}
      caption={t('修改{{email}}', { email })}
    >
      <Modal.Body>
        <Form>
          <Form.Item
            label={t('收件邮箱地址')}
            showStatusIcon={false}
            status={getStatus(emailField.meta, validating)}
            message={
              getStatus(emailField.meta, validating) === 'error' &&
              emailField.meta.error
            }
          >
            <Input {...emailField.input} />
          </Form.Item>
          <Form.Item
            showStatusIcon={false}
            label={t('是否永久有效')}
            status={getStatus(isPermanentField.meta, validating)}
            message={
              getStatus(isPermanentField.meta, validating) === 'error' &&
              isPermanentField.meta.error
            }
          >
            <Segment
              {...isPermanentField.input}
              options={[
                { text: t('是'), value: '1' },
                { text: t('否'), value: '0' },
              ]}
            />
          </Form.Item>
          {isPermanentField.input.value === '0' && (
            <Form.Item
              showStatusIcon={false}
              label={t('失效日期')}
              status={getStatus(expireDateField.meta, validating)}
              message={
                getStatus(expireDateField.meta, validating) === 'error' &&
                expireDateField.meta.error
              }
            >
              <DatePicker
                {...expireDateField.input}
                onChange={(v) => expireDateField.input.onChange(moment(v))}
                format="YYYY-MM-DD"
                disabledDate={disabledDate}
              />
            </Form.Item>
          )}
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          loading={submitting}
          type="primary"
          disabled={validating}
          onClick={() => {
            handleSubmit();
          }}
        >
          {t('确定')}
        </Button>
        <Button type="weak" htmlType="button" onClick={onClose}>
          {t('取消')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default EditCustomBlockDialog;
