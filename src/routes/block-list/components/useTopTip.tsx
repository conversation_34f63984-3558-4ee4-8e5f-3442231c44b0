import { t } from '@tea/app/i18n';
import { StatusTip, Status } from '@tencent/tea-component';
import React, { useMemo } from 'react';

interface Props {
  record?: Array<any>;
  loading?: boolean;
  emptyTip?: React.ReactNode;
  loadingTip?: React.ReactNode;
  size?: 'xs' | 's' | 'm' | 'l';
  isDesc?: boolean;
  icon?: 'blank' | 'loading' | 'search';
  title?: string;
  description?: string;
}

export const useTopTip = (props: Props) => {
  const {
    record,
    loading,
    emptyTip,
    loadingTip,
    size,
    isDesc = false,
    icon,
    title = `${t('暂无数据')}`,
    description,
  } = props;
  const needSize = size || 'xs';
  const isLoading = loading === undefined ? record === undefined : loading;

  const isEmpty = record && record.length === 0;

  const tableTopTip = useMemo(() => {
    if (isLoading) {
      return <StatusTip loadingText={loadingTip} status="loading" />;
    }
    if (isEmpty) {
      return (
        <StatusTip
          hideIcon={true}
          emptyText={
            <Status
              size={needSize}
              icon={icon}
              title={title}
              description={description}
            />
          }
          status="empty"
        />
      );
    }
    return false;
  }, [
    isLoading,
    isEmpty,
    loadingTip,
    emptyTip,
    description,
    title,
    icon,
    needSize,
    isDesc,
  ]);

  return tableTopTip;
};
