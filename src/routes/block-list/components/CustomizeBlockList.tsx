import React, { useEffect, useState } from 'react';
import { t } from '@tea/app/i18n';
import {
  Card,
  Form,
  Input,
  Modal,
  Select,
  Table,
  Text,
  message,
} from '@tencent/tea-component';
import { SearchBox, Switch } from '@tencent/tea-component';
import { But<PERSON> } from '@tea/component';
import { selectable } from '@tea/component/table/addons';
import { isEqual } from 'lodash';
import {
  useAsyncRetry,
  useMountedState,
  useSetState,
  useToggle,
} from 'react-use';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/formFn';
// import { useTableTopTip } from '@src/routes/global-components/table-top-tip/useTableTopTip';
import { useTopTip } from './useTopTip';

import {
  deleteCustomBlackList,
  getCustomBlockList,
  getAccountCustomBlacklistStatus,
  updateAccountCustomBlacklistStatus,
} from '../actions';
import { useDialogRef } from '@src/routes/global-components/useDialog';
import EditCustomBlockDialog from './EditCustomBlockDialog';
import { app } from '@tencent/tea-app';
import { find } from 'lodash';
import CreateCustomBlockDialog from './CreateCustomBlockDialog';

const { pageable } = Table.addons;

const initialValues = {
  status: '2',
  email: '',
};

const options = [
  { value: '2', text: t('全部') },
  { value: '0', text: t('已失效') },
  { value: '1', text: t('生效中') },
];

export function CustomizeBlockList() {
  const isMounted = useMountedState();
  const dialogRef = useDialogRef();
  const createDialogRef = useDialogRef();
  const [deleteLoading, setDelLoading] = useToggle(false);
  const [switchLoading, setSwitchLoading] = useToggle(false);
  const [params, setParams] = useSetState<{
    status: string;
    email?: string;
    pageIndex: number;
    pageSize: number;
  }>({
    pageIndex: 1,
    pageSize: 10,
    status: '2',
  });

  const { value, loading, retry } = useAsyncRetry(async () => {
    const { pageIndex, pageSize, status, email } = params;
    const res = await getCustomBlockList({
      Status: +status,
      Email: email,
      Offset: (pageIndex - 1) * pageSize,
      Limit: pageSize,
    });
    return res;
  }, [params]);

  const list = value?.Data ?? [];
  const total = value?.TotalCount ?? 0;

  const topTip = useTopTip({
    record: list,
    loading,
    size: 'l',
  });

  const [selectedKeys, setSelectedKeys] = useState([]);

  const deleteBtnDisabled = selectedKeys.length === 0;

  useEffect(() => {
    isMounted() && loading && setSelectedKeys([]);
  }, [isMounted, loading]);

  const onDelete = (emails) => {
    Modal.confirm({
      message: t('确认删除{{email}}', { email: emails.join('、') }),
      description: t('删除后，该收件邮箱地址将不会被拦截，可接收邮件'),
      onOk: async () => {
        try {
          setDelLoading(true);
          await deleteCustomBlackList({
            Emails: emails,
          });
          setDelLoading(false);
          app.tips.success(t('删除成功'));
          retry();
        } catch (error) {
          setDelLoading(false);
        }
      },
    });
  };
  const [autoRejectRule, setAutoRejectRule] = useState(0);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getAccountCustomBlacklistStatus();
        const customBlacklistStatus = res.CustomBlacklistStatus;
        setAutoRejectRule(customBlacklistStatus);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);
  const switchAutoRejectRule = async (v) => {
    try {
      setSwitchLoading(true);
      if (v) {
        await updateAccountCustomBlacklistStatus({
          CustomBlacklistStatus: 1,
        });
        setSwitchLoading(false);
        message.success({ content: t('自动拦截-收件箱空间满已经开启！') });
        setAutoRejectRule(1);
      } else {
        await updateAccountCustomBlacklistStatus({
          CustomBlacklistStatus: 0,
        });
        setSwitchLoading(false);
        message.success({ content: t('自动拦截-收件箱空间满已关闭！') });
        setAutoRejectRule(0);
      }
    } catch (e) {
      setSwitchLoading(false);
      message.error({ content: t('操作失败。') });
    }
  };
  return (
    <>
      <Card>
        <Card.Body title={t('自动拦截规则')}>
          <div style={{ display: 'flex', gap: '10px' }}>
            <div>{t('收件箱空间满拦截')}</div>
            <div
              style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}
            >
              <Switch
                loading={switchLoading}
                disabled={switchLoading}
                value={autoRejectRule === 1}
                onChange={(v) => switchAutoRejectRule(v)}
              ></Switch>
              <div style={{ color: 'gray' }}>
                {t('建议开启，长期发送到“满箱邮箱”可能被认为是“骚扰性发送”')}
              </div>
            </div>
          </div>
        </Card.Body>
      </Card>
      <Card>
        <Card.Body title={t('拦截列表')}>
          <div>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                gap: '10px',
              }}
            >
              <div style={{ display: 'flex', gap: '10px' }}>
                <Button
                  type="primary"
                  onClick={() => {
                    createDialogRef.current.open();
                  }}
                >
                  {t('新增')}
                </Button>
                <Button
                  disabled={deleteBtnDisabled}
                  onClick={() => {
                    const emails = selectedKeys.map(
                      (v) => find(list, (item) => `${item.Id}` === v)?.Email,
                    );
                    onDelete(emails);
                  }}
                  loading={deleteLoading}
                >
                  {t('批量删除')}
                </Button>
              </div>
              <div>
                <FinalForm
                  onSubmit={(values) => {
                    console.log('cccccc Form values:', values);
                    setParams({ ...initialValues, ...values, pageIndex: 1 });
                  }}
                  initialValuesEqual={(v1, v2) => isEqual(v1, v2)}
                  initialValues={initialValues}
                >
                  {({ handleSubmit, validating, submitting }) => {
                    return (
                      <form onSubmit={handleSubmit}>
                        <Form layout="inline">
                          <Field name="status" validateFields={[]}>
                            {({ input, meta }) => (
                              <Form.Item
                                showStatusIcon={false}
                                label={t('状态')}
                                message={
                                  getStatus(meta, validating) === 'error' &&
                                  meta.error
                                }
                              >
                                <Select
                                  {...input}
                                  options={options}
                                  size="s"
                                  appearance="button"
                                  disabled={submitting}
                                />
                              </Form.Item>
                            )}
                          </Field>
                          <Field name="email" validateFields={[]}>
                            {({ input, meta }) => (
                              <Form.Item
                                showStatusIcon={false}
                                label={t('收件邮箱地址')}
                                status={getStatus(meta, validating)}
                                message={
                                  getStatus(meta, validating) === 'error' &&
                                  meta.error
                                }
                              >
                                <SearchBox
                                  hideButton={false}
                                  {...input}
                                  size="l"
                                  placeholder={t('<EMAIL>')}
                                  disabled={submitting}
                                  onSearch={(value) => {
                                    handleSubmit();
                                  }}
                                />
                              </Form.Item>
                            )}
                          </Field>
                        </Form>
                      </form>
                    );
                  }}
                </FinalForm>
              </div>
            </div>
          </div>
          <Table
            verticalTop
            topTip={topTip}
            records={list}
            recordKey="Id"
            columns={[
              {
                key: 'Email',
                header: t('收件邮箱地址'),
              },
              {
                key: 'ErrType',
                header: t('拦截类型'),
              },
              {
                key: 'ISPDesc',
                header: t('备注'),
              },
              {
                key: 'CreateTime',
                align: 'center',
                header: t('创建时间'),
              },
              {
                key: 'ExpireDate',
                header: t('失效日期'),
                align: 'center',
                render: (item) => {
                  return item.ExpireDate || '-';
                },
              },
              {
                key: 'Status',
                header: t('状态'),
                align: 'center',
                render: (item) => {
                  return (
                    <Text theme={item.Status === 1 ? 'success' : 'danger'}>
                      {item.Status === 1 ? t('生效中') : t('已失效')}
                    </Text>
                  );
                },
              },
              {
                key: 'action',
                width: 180,
                header: t('操作'),
                align: 'center',
                render: (item) => (
                  <>
                    <Button
                      type="link"
                      onClick={() => {
                        dialogRef.current.open({
                          id: item.Id,
                          expireDate: item.ExpireDate,
                          email: item.Email,
                        });
                      }}
                    >
                      {t('修改')}
                    </Button>
                    <Button
                      type="link"
                      onClick={() => {
                        onDelete([item.Email]);
                      }}
                    >
                      <Text theme="danger">{t('删除')}</Text>
                    </Button>
                  </>
                ),
              },
            ]}
            addons={[
              pageable({
                recordCount: total,
                pageIndex: params.pageIndex,
                pageSize: params.pageSize,
                pageSizeOptions: [10, 20, 30, 50, 100],
                onPagingChange: ({ pageIndex, pageSize }) => {
                  setParams({
                    pageIndex: pageIndex || 1,
                    pageSize: pageSize || 10,
                  });
                },
              }),
              selectable({
                value: selectedKeys,
                onChange: (value) => setSelectedKeys(value),
              }),
            ]}
          />
        </Card.Body>
      </Card>
      <EditCustomBlockDialog
        dialogRef={dialogRef}
        onSuccess={() => {
          retry();
        }}
      />
      <CreateCustomBlockDialog
        dialogRef={createDialogRef}
        onSuccess={() => {
          retry();
        }}
      />
    </>
  );
}
