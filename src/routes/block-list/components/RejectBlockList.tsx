import React, { useCallback, useEffect, useState, useRef } from 'react';
import { t } from '@tea/app/i18n';
import { app } from '@tea/app';
import { Card, Table, Alert, ExternalLink } from '@tencent/tea-component';
import { listRejectedRecipients } from '@src/routes/block-list/actions';
import { getQCMainHost } from '@src/utils/domain';
import { isMainLand } from '@src/utils/CommonUtils';
import moment from 'moment';
import BlackListSearchQuery from '@src/routes/block-list/components/BlackListSearchQuery';
import { useTopTip } from './useTopTip';

import { useForm } from 'react-final-form-hooks';
import { autotip } from '@tea/component/table/addons';

const { pageable } = Table.addons;

const initialQueryValues = {
  emailAddress: '',
  timeRange: [moment().add(-15, 'd'), moment()],
};

export function RejectBlockList() {
  const [list, setList] = useState([]);
  const [total, setTotal] = useState(0);
  const [listLoading, setListLoading] = useState(false);
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });
  const queryValues = initialQueryValues;
  const activeId = 'self';
  const topTip = useTopTip({
    record: list,
    loading: listLoading,
    size: 'l',
    icon: 'blank',
  });
  function statsQuery(values, pageData) {
    setList([]);
    console.log('pageData=', pageData);
    const { domain, timeRange = [], emailAddress } = values;
    const [StartDate, EndDate] = timeRange.map((date) =>
      date.format('YYYY-MM-DD'),
    );
    const data = {
      StartDate,
      EndDate,
      Domain: domain,
      EmailAddress: emailAddress,
      Limit: pageData.pageSize,
      Offset: (pageData.pageIndex - 1) * pageData.pageSize,
    };
    setListLoading(true);
    return listRejectedRecipients(data)
      .then((res) => {
        console.log('listRejectedRecipients searchQuery Over', res);
        const { RejectedRecipients = [], TotalCount } = res;
        setList(RejectedRecipients);
        setTotal(TotalCount);
      })
      .catch(() => {})
      .finally(() => {
        setListLoading(false);
      });
  }

  const onSearchSubmit = useCallback(
    (values) => {
      console.log('onSearchSubmit', values, pageData);
      return statsQuery(values, pageData);
    },
    [pageData],
  );

  const formRenderProps = useForm({
    onSubmit: onSearchSubmit,
    initialValuesEqual: () => true,
    initialValues: queryValues,
    validate: ({ timeRange }: any) => ({
      receiveEmailAddress: undefined,
      timeRange: !timeRange ? t('请选择时间段') : undefined,
    }),
  });
  const { handleSubmit } = formRenderProps;

  // 翻页
  const onPagingChange = (data) => {
    setPageData(data);
  };
  useEffect(() => {
    handleSubmit();
  }, [pageData]);

  const serverLink = isMainLand
    ? `https://${getQCMainHost()}/online-service?from=intro_ses`
    : `https://${getQCMainHost()}/workorder/category`;
  return (
    <div className="reject-block-list">
      <Alert>
        {t(
          '收件人拒收或者将发件人拉黑，平台将不会继续往拒收邮箱中发信。如有疑问，请',
        )}

        <ExternalLink href={serverLink}>
          {isMainLand ? t('联系在线客服') : t('提交工单')}
        </ExternalLink>
        {t('。')}
      </Alert>
      <div
        className="flex-one-row"
        style={{
          display: 'flex',
          alignItems: 'start',
        }}
      >
        <div>
          <BlackListSearchQuery
            formRenderProps={formRenderProps}
            activeId={activeId}
            receive={true}
          />
        </div>
      </div>
      <Card style={{ marginTop: '10px' }}>
        <Table
          verticalTop
          topTip={topTip}
          records={list}
          recordKey="EmailAddress"
          columns={[
            {
              key: 'EmailAddress',
              header: t('收件邮箱地址'),
            },
            {
              key: 'FromDomain',
              header: t('被拒收的发信域名'),
            },
            {
              key: 'RejectTime',
              header: t('拒收时间'),
            },
          ]}
          addons={[
            pageable({
              recordCount: total,
              pageIndex: pageData.pageIndex,
              pageSize: pageData.pageSize,
              pageSizeOptions: [10, 20, 30, 50, 100],
              onPagingChange: (query) => {
                console.log('onPagingChange', query);
                // {pageIndex: 2, pageSize: 10}
                onPagingChange(query);
              },
            }),
          ]}
        />
      </Card>
    </div>
  );
}
