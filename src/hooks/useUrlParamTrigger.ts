import { useEffect } from 'react';

type ParamName = string | string[];

interface UseUrlParamTriggerProps {
  paramName: ParamName;
  onTrigger: (value: string, paramName: string) => void;
  triggerValue?: string;
}

export const useUrlParamTrigger = ({
  paramName,
  onTrigger,
  triggerValue,
}: UseUrlParamTriggerProps) => {
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const paramNames = Array.isArray(paramName) ? paramName : [paramName];

    // 处理所有参数
    paramNames.forEach((name) => {
      const value = urlParams.get(name);
      if (!value) return;

      if (triggerValue !== undefined) {
        // 精确匹配模式：只有当值与triggerValue相同时才触发
        if (value === triggerValue) {
          onTrigger(value, name);
        }
      } else {
        // 通用模式：直接将获取到的值传给回调
        onTrigger(value, name);
      }

      // 清除参数
      urlParams.delete(name);
    });

    // 只有当有参数被删除时才更新URL
    if (urlParams.toString() !== window.location.search.slice(1)) {
      window.history.replaceState(
        {},
        '',
        `${window.location.pathname}?${urlParams.toString()}`,
      );
    }
  }, [paramName, onTrigger, triggerValue]);
};
