import React from 'react';
import { Icon, Text } from '@tencent/tea-component';
import { t } from '@tea/app/i18n';

interface UseOptionsTipsProps {
  loading: boolean;
  dataLength: number;
  emptyText?: string;
}

export const useOptionsTips = ({
  loading,
  dataLength,
  emptyText,
}: UseOptionsTipsProps) => {
  if (loading) {
    return (
      <div
        style={{
          marginLeft: 10,
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <Icon type="loading" />
        <Text theme="text">{t('刷新中...')}</Text>
      </div>
    );
  }

  if (dataLength === 0) {
    return (
      <Text style={{ marginLeft: 10, marginBottom: 8 }} theme="text">
        {emptyText || t('暂无数据')}
      </Text>
    );
  }

  return null;
};
