{"k_0003y9x": "None", "k_0003yx6": "Yes", "k_00041oj": "No", "k_00042hl": "Value", "k_000izxt": "JSON format parsing error: ", "k_000omo1": "Callback of receiving service provider's rejection letter", "k_002r2rn": "Optional", "k_002r305": "Send", "k_002rflt": "Delete", "k_002sqcb": "Required", "k_002uu5g": "Collapse", "k_002uzrd": "Preview", "k_002vfrj": "Overview", "k_002wqjs": "Verify", "k_003kv3v": "Search", "k_003m67m": "Modify", "k_003m6xh": "Set", "k_003mnsy": "Notes", "k_003mow6": "Details", "k_003n1ik": "All", "k_003n1un": "Date", "k_003nevv": "Cancel", "k_003nzwy": "Status", "k_003ojje": "Remark", "k_003psvr": "Domain", "k_003py1h": "Type", "k_003r4i7": "Create", "k_003r4v2": "Normal", "k_003re34": "Expand", "k_003rj48": "Go Now", "k_003rk1s": "Save", "k_003rzap": "Confirm", "k_003s0e6": "<PERSON><PERSON><PERSON>", "k_003twr0": "<PERSON>", "k_003tywh": "Submit", "k_003u02c": "Operation", "k_009fkbr": "Enter a template name.", "k_010uudg": "Plan Details", "k_02o0j2y": "Verified", "k_02o0jjh": "Pending verification", "k_02y5za0": "Enter the email body.", "k_03bd1s6": "Opens", "k_03bkewo": "Isolated", "k_03c475t": "Plain text", "k_03ckobz": "Expired", "k_03dd1g6": "Terminated", "k_03fb658": "Record Value", "k_03i7qq9": "Under review", "k_03i9qmf": "Isolating", "k_03ia4nz": "To", "k_03i9uyg": "Sender", "k_03mjr2i": "Plan ID", "k_03vsgdv": "<PERSON>", "k_04hvg7n": "<PERSON> Clicks<1><0></0></1>", "k_0mmv1do": "Sending beyond the free limit will be charged based on actual usage. Please refer to the <1>Pricing</1> for more information.", "k_053u9rz": "Verification failed", "k_054aa2z": "Unapproved", "k_054qosm": "The email address configuration for this domain has reached its limit.", "k_056igp7": "JSON format parsing error.", "k_05bkrvu": "After the domain is verified, do not delete or modify the configured SPF and MX records. Otherwise, errors may occur when sending emails.", "k_05bzik9": "To avoid conflicts between SPF and MX records, do not use corporate email domains.", "k_05qpion": "Daily email sending limit: {{formattedCount}}", "k_05qpion_0": "Daily email sending limit: {{formattedCount}}", "k_05qpion_plural": "Daily email sending limit: {{formattedCount}}", "k_05vdj5k": "Enter variables in JSON format.", "k_061azvv": "The name does not conform to the required format.", "k_065n8bk": "The review will be completed within 1 working day (weekend and holidays postponed) after an template is submitted. Thank you for your patience and understanding.", "k_077xr6j": "https://abc.def.ghi/jk", "k_07jlccx": "Only when the \"Link Clicks Count\" capability is enabled will it be counted. You can check whether it is enabled on the <1>Statistic settings page</1>.", "k_07bp701": "Customize your sender addresses.", "k_08ghn1k": "{{count}} sender domain", "k_08ghn1k_0": "{{count}} sender domains", "k_08ghn1k_plural": "{{count}} sender domains", "k_09qcps9": "After deleting this address, you can no longer use it to send emails.", "k_0a0u13b": "Please upload a TXT or HTML file. You can use {{variable name}} to specify variables (if any), for example, dear {{name}}.", "k_0a61lzz": "drag & drop here", "k_0bsap4o": "From", "k_0by8t0g": "Bounce Time", "k_0bya3vq": "Creation time", "k_0bycvr3": "Expiration Time", "k_0c4p3to": "Getting Started", "k_0c6cj11": "Activate", "k_0c7ufsd": "Email Sending", "k_0cjhbiv": "<PERSON><PERSON> Delete", "k_0ck18xd": "Calls", "k_0ckd8bf": "<PERSON><PERSON><PERSON>", "k_0cl5bxm": "Sends", "k_0claj39": "Deliveries", "k_0clb83c": "Unsubscriptions", "k_0cw6hre": "The review will be completed within 1 working day (weekend and holidays postponed) after a template is submitted.", "k_0dh4tmm": "Subject", "k_0djs6ds": "Notes", "k_0dmr0ea": "Email title", "k_0ei23ck": "Select an email template.", "k_0f7ufx1": "Verification passed", "k_0f7zpcx": "Approved", "k_0f8cwpo": "Verification failed", "k_0fqivid": "Statistics", "k_0fvugro": "<PERSON><PERSON>", "k_0fw91ja": "Sending Overview", "k_0fz89ca": "Recipient Address", "k_0g7h09s": "Select", "k_0g9rf92": "Enter a domain", "k_0gqtb7v": "Sending Test", "k_0gsycam": "Sender Name", "k_0gzgqsc": "Submit", "k_0hjpnrx": "Each Tencent Cloud account can be configured with up to 20 domains.", "k_0i8nsi9": "Are you sure you want to delete this domain?", "k_0ie2gr1": "Last Updated", "k_0igtft3": "Enter a subject.", "k_0iq3dh9": "The number of users (deduplicated) who opened emails", "k_0jjl6fb": "Customize your domains and let the domain administrator configure DNS.", "k_0k8uwsf": "HTML rich text", "k_0m02zrf": "After deleting this domain, you can no longer use it to send emails.", "k_0m4xdw1": "You can use {{variable name}} to specify variables, for example, dear {{name}}.", "k_0my1re7": "Create Sender Address", "k_0nbv13s": "Domain status. Only a verified domain can be used to send emails.", "k_0nk5rm6": "Failed to submit, perhaps because the template file is too large. Please upload via API calls.", "k_0nktopl": "Callback for reported by recipients", "k_0o1r2vw": "Remaining emails in plans: {{packageFull}}", "k_0odpbha": "Account-level callback <PERSON><PERSON>", "k_0o4mj10": "You have {{count}} valid email plan.", "k_0o4mj10_0": "You have {{count}} valid email plans.", "k_0o4mj10_plural": "You have {{count}} valid email plans.", "k_0oez2pd": "Each domain supports up to 10 sender addresses.", "k_0ov552u": "Enter an email prefix.", "k_0qyibir": "Blocklist", "k_0r92e66": "Resource Plan Overview", "k_0s3sgel": "Unblocklist", "k_0st8xgu": "After the domain is configured, synchronization may take 5 minutes to 2 hours. Please wait for the verification to complete.", "k_0wty1kd": "Welcome to SES.\nPlease carefully read the billing rules of SES before using it.\nBy clicking \"Activate\", you can activate and start using this service.", "k_0yj8k52": "Template ID", "k_0yslfz1": "Are you sure you want to delete this template?", "k_0yt7gy4": "No sender addresses yet", "k_0yz2e66": "The number of emails in the plan", "k_0zfcqpx": "Create Sending Task", "k_0zri0bi": "Template name too long", "k_11k3gau": "Template Name", "k_11lfais": "Sender Domain Configuration", "k_11m83ba": "Sender Address Configuration", "k_11n6sh7": "Callback Address Configuration", "k_11wfhx3": "<PERSON><PERSON>", "k_12cnv1e": "Variables", "k_12ds00f": "Template Configuration", "k_12g1oux": "Email Body", "k_129ekqk": "Simplified Chinese", "k_129eldp": "Traditional Chinese", "k_1307fpw": "Do not add unsubscribe link", "k_131j5nv": "<PERSON>ail Te<PERSON>late", "k_131lpy2": "Create Template", "k_133i2d7": "Template", "k_13w75f0": "Current Status", "k_13we9fh": "Unknown status", "k_13yz33q": "Mouse up", "k_1456s6t": "A bounce address will be blocklisted for 2 weeks. You can click \"Unblocklist\" to remove the address from the blocklist.", "k_14cnlbh": "<0>Welcome to SES.</0><1>Please carefully read the <1>billing rules</1> of SES before using it.</1><2>By clicking \"Activate\", you can activate and start using this service.</2>", "k_14efxt6": "Configure the template content according to your needs.", "k_150dmu8": "Email Template Details", "k_15bv0kp": "Create <PERSON><PERSON>", "k_15e5z5s": "Recipient Domain", "k_15e62sn": "Sender Domain", "k_15ijusr": "Template type", "k_15kabpm": "Email Address", "k_15kilet": "Sender Address", "k_15lhn7s": "Callback Address", "k_15qnvlb": "发信任务", "k_15s8oce": "内容帮助", "k_15tg6qb": "Activate service", "k_15vp5xk": "Callback Guide", "k_15wcvlb": "Saved successfully", "k_15wpfw0": "Activated successfully", "k_15wpiai": "<PERSON><PERSON> successfully", "k_160hitd": "Set a callback address. Tencent Cloud will notify the callback address of successful delivery, email rejection, email bounce, email open, click on link, unsubscription, and other events. By default, this field is left empty, indicating no notification.", "k_1629prc": "Date Range", "k_164wmg4": "{{count}} email template", "k_164wmg4_0": "{{count}} email templates", "k_164wmg4_plural": "{{count}} email templates", "k_16lpoxo": "File Size", "k_1715qsq": "No templates yet", "k_176lfup": "Host Record", "k_17xmxqu": "Enter a recipient domain.", "k_17yj8dh": "Select a time period.", "k_180n6p9": "Create Sender Domain", "k_18o0omp": "Cancel Upload", "k_18o1b9u": "Upload Again", "k_18o9o0d": "Choose a file", "k_19sh4cg": "Purchase now", "k_1acn7c0": "Plan Management", "k_1aspzpe": "Unblocklisted successfully", "k_1bc4nvq": "Send a test email in the console after finishing the configuration.", "k_1bc7av2": "Emails", "k_1bz0q12": "Template status. Only an approved template can be used to send emails.", "k_1d252g7": "Uploaded Templates", "k_1dc8mgg": "You must have the admin permissions on the sender domains.", "k_1dk7itf": "Only when the \"Email Opens Count\" capability is enabled will it be counted. You can check whether it is enabled on the <1>Statistic settings page</1>.", "k_1e0lhgq": "Not approved yet", "k_1f75swu": "Enter a recipient.", "k_1fkkfu2": "{{count}} sender address", "k_1fkkfu2_0": "{{count}} sender addresses", "k_1fkkfu2_plural": "{{count}} sender addresses", "k_1gt20x5": "Upload a file.", "k_1iqbxes": "Used Emails", "k_1iqlrhq": "Remaining Emails", "k_1j1o2h0": "1小时内对同一用户发件数量限制：{{count}}封", "k_1j1o2h0_0": "1小时内对同一用户发件数量限制：{{count}}封", "k_1j1o2h0_plural": "1小时内对同一用户发件数量限制：{{count}}封", "k_1jrexi4": "Select…", "k_1jxgrp7": "Loading…", "k_1ke0v8r": "<EMAIL>", "k_1ki1c3d": "This page is only for testing email sending. Up to 20 recipient addresses are allowed for a single test. You can send to more recipients at a time via API calls. For more information, please see <1>API documentation</1>.", "k_1mvtsae": "Domain too short", "k_1obley8": "{{type}} Verification", "k_1orvhae": "Remaining free emails: {{formattedCount}}", "k_1orvhae_0": "Remaining free emails: {{formattedCount}}", "k_1orvhae_plural": "Remaining free emails: {{formattedCount}}", "k_1p75gbm": "No sending domain available.Please go to <1>Sender Domain</1> and create a new one first.", "k_1pbrkyz": "Supports up to 20 recipient addresses (one per line)", "k_1pcz27n": "Callback for openned mail", "k_1pi6v4x": "Callback for discarded mail", "k_1pkkoav": "Email tracking data, which allows self-service settings at the account level and domain level to determine whether to count email opens and link clicks, <1>Go to settings</1>.", "k_1pww5r3": "Enter a sender name.", "k_1qnv1ls": "{{count}} sending task", "k_1qnv1ls_0": "{{count}} sending tasks", "k_1qnv1ls_plural": "{{count}} sending tasks", "k_1r76n8v": "Email Template Preview", "k_1trzl1v": "Are you sure you want to delete this address?", "k_1xf76jl": "Opens", "k_1xjias4": "Upload an HTML file.", "k_1xuotg4": "There is a certain delay in statistics. The data displayed here is for reference only.", "k_1y3su2v": "After deleting this template, you can no longer use it to send emails.", "k_1yxzdrd": "Sender Address Preview", "k_03fcsh3": "Current value", "k_15im5hc": "Billing Rules", "k_1jeiu3n": "If your domain is hosted on Tencent Cloud, log in to <1>{{cnsUrl}}</1> to configure it. If your domain is hosted on another domain service provider, configure it according to the checklist.", "k_0tvnq3g": "If your domain is hosted on Tencent Cloud, log in to <1>https://console.cloud.tencent.com/cns</1> to configure it. If your domain is hosted on another domain service provider, configure it according to the checklist.", "k_0yjfjwc": "If your domain is hosted on Tencent Cloud, log in to <2>https://console.cloud.tencent.com/cns</2> to configure it. If your domain is hosted on another domain service provider, configure it according to the checklist.", "k_00043v0": "day(s)", "k_002vt4q": "Description", "k_002vxya": "Edit", "k_002wflr": "German", "k_002whil": "Japanese", "k_002wja5": "French", "k_002wjaw": "Thai", "k_002wqld": "Korean", "k_003kivf": "View", "k_003lpcj": "English", "k_003lxmq": "Refresh", "k_003lzri": "Recipients", "k_003mam4": "Tag", "k_003pnp8": "Import", "k_013su72": "Each Tencent Cloud account can be configured with up to 10 domains.", "k_03fsewb": "To start", "k_03jgupk": "Sending", "k_0c7m7kz": "<PERSON><PERSON>", "k_0c80eop": "Scheduled", "k_0c83xs3": "Recurring", "k_0ck5hnz": "Requests", "k_0d1i9c0": "Are you sure you want to delete this sending task?", "k_0ej1kv1": "Recipient Group Name", "k_0goic7q": "Start Time", "k_0kgls7e": "Create Recipient Group", "k_0mxnvn3": "Are you sure you want to delete this recipient group?", "k_0ozzbj8": "Paused today", "k_0ppk0bq": "Callback Event Filtering", "k_0r0sups": "Recipient Groups", "k_0wh5faf": "A group name and alias are used to identify the group and must be unique.", "k_0yjppo6": "Template ID", "k_11l8yh3": "Group Name", "k_13bssa0": "Recurrence", "k_13fzaqx": "<PERSON><PERSON>", "k_13w738o": "Task Status", "k_13wlv6k": "After deleted, this recipient group cannot be used to send emails.", "k_14xbqck": "1-50 characters", "k_15ihz3j": "Task Type", "k_15zkqci": "1-30 characters", "k_16aswzz": "Failed to send", "k_1778lc8": "Sending Progress", "k_1ce9p4q": "1-30 characters; must be unique", "k_1dl4v9t": "No recipient groups yet", "k_1o65jpx": "Supports <2>TXT</2> and <5>CSV</5> files; a field per line", "k_1rn94b8": "After deleted, this sending task will not be executed.", "k_14gl94g": "Supports <2>TXT</2> and <5>CSV</5> files; an email address per line", "k_0w182hh": "发送地址类型", "k_0wjlysa": "The number of recipients to import cannot exceed 10,000.", "k_1jx21kv": "Importing…", "k_0x9vzue": "This page is only for testing email sending. Up to 20 recipient addresses are allowed for a single test.", "k_0479sum": "Suitable for batch sending of marketing and notification emails. To send triggered emails (such as authentication and transaction emails), you are advised to call the SendEmail API.", "k_0qqr19t": "When a task enters the cache queue, its status is Paused and the sending progress bar remains static. After you restart the task the next day, its status becomes Sending and the progress bar grows.", "k_1qas27x": "The built-in automatic warm-up feature for batch sending intelligently determines the reputation of the sender domain/IP and the maximum number of sent emails allowed per day. When this daily limit is reached, email sending will stop and extra emails will enter the cache queue and be sent 24 hours later. For the daily email limit for a domain/IP that has not been warmed up, see the Features document.", "k_1uspwrz": "You can use a single domain for multiple sending tasks. When the total email volume exceeds the maximum number allowed per day, the excess emails will enter the cache queue and be sent the next day.", "k_0jgbz1y": "The built-in automatic warm-up feature for batch sending intelligently determines the reputation of the sender domain/IP and the maximum number of sent emails allowed per day. When this daily limit is reached, email sending will stop and extra emails will enter the cache queue and be sent 24 hours later. For the daily email limit for a domain/IP that has not been warmed up, see <1>here</1>.", "k_04ul4mk": "3. Cannot be the same as the previous password.", "k_09kfj0x": "Each domain supports up to 10 sender addresses only.", "k_0es79co": "SMTP Password", "k_0extds1": "The format of line {{unvalidIndex}} is wrong. Please enter again.", "k_0izea64": "SMTP password takes effect within 5 minutes after being set.", "k_0kli1x1": "SMTP server: smtp.qcloudmail.com; SMTP port: 25", "k_1a4fiiw": "Set SMTP Password", "k_1f7blbd": "2. Must contain at least 2 numbers, 2 uppercase letters, and 2 lowercase letters. Both numbers and letters cannot be a repeat of a particular character.", "k_1gu7uoc": "1. 10-20 characters; must contain numbers, uppercase letters, and lowercase letters.", "k_1t9kzib": "Enter the password.", "k_0mf3mut": "Must contain at least 2 numbers, 2 uppercase letters, and 2 lowercase letters. Both numbers and letters cannot be a repeat of a particular character.", "k_1dal177": "10-20 characters; must contain numbers, uppercase letters, and lowercase letters.", "k_187plyj": "SMTP server: smtp.qcloudmail.com; SMTP port: {{port}}", "k_0zvanuo": "Unsubscription Management", "k_0zzl751": "<0><0></0></0><1><0></0></1>", "k_19j1ta6": "To avoid conflicts between SPF and MX records, do not use corporate email domains. If there is a corporate email domain, create a second-level domain under it and use the second-level domain here. ", "k_002v5d3": "Unsubscription", "k_03edeml": "Soft bounce", "k_03ee05q": "Hard bounce", "k_0c7x74q": "Success", "k_0gj5oej": "Mailbox Simulator", "k_0gn49dd": "Arabic", "k_0n68af6": "Recipient Address", "k_0s3u5wk": "Global blocklist", "k_0s6pypu": "Your template application has been successfully submitted. The review is expected to be completed within one business day (deferred on weekends and holidays). Thank you for your patience.", "k_112qng2": "Code and Description", "k_13jcj72": "Simulated Scenario", "k_15wbkfo": "Simulated successfully", "k_03f64nn": "None", "k_0grmatd": "Ad Tag", "k_1d5syez": "After subject", "k_1d9635r": "Callback for the message to be successfully sent", "k_1rbyz4o": "Before subject", "k_002qrgn": "Time", "k_002rhlf": "In batch", "k_002vqvn": "Download", "k_002vud6": "Deliver", "k_003pg2v": "Click", "k_003ps0i": "<PERSON><PERSON>", "k_003r53r": "Dimension", "k_003rcwm": "Open", "k_003s65n": "Add", "k_03i7hfh": "Requesting", "k_03j9icr": "Soft bounce", "k_0xkw1mm": "Total recipient addresses", "k_12rri94": "Download link", "k_133n4vs": "Event selection", "k_13wcsoz": "Spam report", "k_13wiwd1": "Delivery status", "k_14u9cl2": "Sending period", "k_14ubaup": "Trigger period", "k_157z8wt": "Bounce reason", "k_15ibvd9": "Email type", "k_19s7lo5": "Invalid emails", "k_1a1iblm": "Paid emails", "k_1a4cii7": "Modify SMTP Password", "k_003puy5": "Queue", "k_03f15qk": "Blocklist", "k_03j92co": "Hard bounce", "k_06r0gzf": "Open/click", "k_0by7uds": "Triggered at", "k_133pwbb": "Select dimension", "k_13w34zu": "Email status", "k_16akrrc": "Delivery response", "k_1ch8o42": "Real-time queue status", "k_1h4pa4j": "Real-time sending data", "k_1hvvm91": "Email address list", "k_003lxlg": "Times", "k_003tw6w": "Event", "k_003utac": "Bounced", "k_02n71ag": "Delivered", "k_02s10wr": "Spam complaints", "k_0396nh9": "Delivery rate", "k_053r8e7": "Verification failed", "k_0bqcqu4": "Unsubscribed", "k_0c7w0m6": "Start sending", "k_0ckcv4a": "Sent emails", "k_0gt04ck": "Spam complaint", "k_0gv2ihv": "Spanish", "k_0jz7u56": "Downloading. Please wait.", "k_0yisp2o": "Time", "k_0yjt1wy": "Task ID", "k_0yld1cr": "The review will be completed within 1 working day (weekend and holidays postponed) after a template is submitted.", "k_132sw7j": "Sending date", "k_13no498": "Description", "k_1nijmc4": "Soft bounces", "k_1stk8ur": "Soft bounce retry", "k_1xslosl": "Rejected emails", "k_150zc86": "Help", "k_0gyzzkz": "Sending error", "k_18e75yn": "Sent at ", "k_0tekbse": "The record value must be ended with \".\"", "k_0u04exs": "Callback of the recipient unsubscribing", "k_1k4j5ms": "You need <NAME_EMAIL> with an email address that can receive email spoofing reports.", "k_09ul1wh": "When you open the downloaded CSV file with Excel or other tools, numbers might be displayed in scientific notation or other formats. ", "k_1appu53": "You can convert timestamps in milliseconds in the downloaded file to readable time using an Excel formula. Example: =TEXT(C2/86400000+DATE(1970,1,1), \"YYYY-MM-DD HH:mm:ss\"), where C2 is the cell that the timestamp is located in. The converted time is UTC time.", "k_1kjrza1": "This page displays only the status in the last 15 minutes. The queue is updated every 10 seconds.", "k_1kq755l": "Callback for delayed mail delivery", "k_1kyoydg": "Email opens count capability has been enabled.", "k_1l9p5g5": "Email opens count capability has been disabled.", "k_0enz57w": "Supports <2>.csv</2> files", "k_1th2t34": "The number of recipients imported for a single recipient group cannot exceed 10,000.", "k_14nl8n7": "The group name and description are used to call the group and thus cannot be duplicated.", "k_0mm9kvs": "Supports only .csv files. Please try again.", "k_0ux636k": "Duplicate variable names in the \"Recipient Group\". Please check and try again.", "k_1hi4can": "Invalid email address(es) in the first column of the \"Recipient Group\". Please check and try again.", "k_1oenozu": "Invalid variable name(s) in the \"Recipient Group\". Please check and try again.", "k_1pnfclb": "Null variable(s) in the \"Recipient Group\". Please check and try again.", "k_1xf91vv": "Empty \"Recipient Group\". Please check and try again.", "k_1yls2c8": "Invalid email address(es) in the \"Recipient Group\". Please check and try again.", "k_0gr159i": "1–{{len}} characters", "k_0yf0hf3": "1–200 characters; must be unique", "k_1h1y0l2": "Only Chinese characters, letters (a-z, A-Z), digits (0–9), and underscores (_) are supported", "k_1i2wvvs": "CSV files are supported (<1>download template</1>); a variable name in the file can only contain letters (a-z, A-Z), digits (0–9), and underscores (_).", "k_0rivkz2": "If this feature is enabled, the system automatically adds the unsubscription link at the end of any email you send, and users do not receive emails sent from the corresponding sender domain after unsubscription.", "k_1653zpr": "Upload an HTML file. Only UTF-8 encoded files are supported.", "k_18yg2oa": "If this feature is enabled, the system automatically adds the \"<AD>\" in the subject of any email you send. It is recommended for advertising and marketing emails.", "k_1atf9he": "A variable in the email body is expressed with {{variable name}}, such as \"Dear {{name}}\". A variable name can only contain letters (a-z, A-Z), digits (0–9), and underscores (_).", "k_0k1n2f3": "3. If there is no variable in your template, clear all the contents of columns B and C (including \"name\" and \"gender\"); otherwise, an error will occur during email sending.", "k_0k97buz": "4. Clear all the contents of \"Notes\" before uploading; otherwise, an error will occur during uploading.", "k_0q1em4b": "1. Cells following the \"email_address\" in column A must be filled with email addresses only.", "k_0vaz0t4": "2. \"name\" and \"gender\" in row 1 of columns B and C are variable name samples. You can modify or add variable names based on your template. A variable name can only contain letters (a-z, A-Z), digits (0–9), and underscores (_).", "k_0vgbj2u": "Link clicks count capability has been enabled.", "k_13i6n8p": "Notes:", "k_1w6ovdn": "Clear all the contents of \"Notes\".", "k_03idh9e": "Uploading", "k_03j88xh": "OK", "k_1414i2p": "The file size exceeds the size limit", "k_192owig": "A CSV file can contain up to 10,000 email addresses, but its size cannot exceed {{size}} KB.", "k_04qtcle": "Do not use corporate email domains to avoid domain configuration conflicts.", "k_0ak9hks": "Each Tencent Cloud account can be configured with up to 10 domains.", "k_0mz86q4": "It may take 5 minutes to 2 hours to synchronize the domain configurations. Please wait patiently.", "k_0xqdwwm": "If your domain is hosted on Tencent Cloud, log in to the <1>DNSPod console</1> to configure the domain. If the domain is hosted on another domain service provider, set it as instructed in the configuration details.", "k_1bondbq": "Set the domain verification info according to the domain configuration details on the configuration interface provided by your domain service provider.", "k_1q8si1v": "After a sender domain is configured and verified, do not modify MX, SPF, DKIM and DMARC records to avoid errors in email sending.", "k_1qw2hky": "Callback for clicked mail", "k_03aujoz": "Account level", "k_0hsq27o": "Confirm deletion", "k_0i3xd3v": "When the remaining emails in your plans are used up, you will be charged according to your actual usage. For more information, please see <2>Pricing</2>.", "k_10is6d7": "If only \"Account level\" callback is set, the settings apply to all sender addresses under this account; ", "k_19iazzq": "Only one \"Account level\" callback can be created, and only one \"Sender address level\" callback can be created for a sender address.", "k_1edfski": "Sender address level", "k_1fo9e9f": "If both \"Account level\" and \"Sender address level\" callbacks are set, the \"Sender address level\" settings apply to this sender address only, while the \"Account level\" settings apply to all other sender addresses other than this one; ", "k_1ftienj": "Sender address level callback URL", "k_1o6zqsv": "The \"Sender address level\" callback takes priority over the \"Account level\" callback as follows:", "k_1uq81x7": "If only \"Sender address level\" callback is set, the settings apply to this sender address only.", "k_1uqzsw6": "Only one \"Sender address level\" callback can be created for a sender address.", "k_1vx0d6p": "Email opens<1><0></0></1>", "k_00rdj04": "The file size cannot exceed {{size}}.", "k_010h52g": "In order to ensure the approval rate of the Email Template, the content of the template must reflect the actual business and need to follow the <1>Email Template Content Specification</1>.", "k_0ckll36": "Send your first email using regular sending in Email Sending page. ", "k_0e2br0o": "Set the template based on your needs.", "k_0e7ftdh": "The data is collected daily. Please query data of today on the next day.", "k_0wlgs9r": "Email template configuration", "k_0xfiovd": "The recipient address is required", "k_0xhoyqp": "Callback Basic Configuration", "k_16fy8s6": "Start now", "k_18q4pq6": "Send email", "k_18z43gz": "Customize your sender address.", "k_01wqny9": "Highest reputation grade: {{levelCount}}", "k_0bbrtms": "<PERSON><PERSON><PERSON> (others)", "k_0dewb0e": "<PERSON><PERSON><PERSON> (spam)", "k_0udb8jl": "Max one-day email limit: {{emailCount}}", "k_0ukkv0n": "Link clicks count capability has been disabled.", "k_11y1ewz": "Reputation grade", "k_13qdg7f": "Email sending data", "k_1655hqa": "Invalid addresses", "k_1fxv0c6": "Email tracking data", "k_1xqkn8y": "One-day email limit", "k_003s3gw": "Others", "k_0or1amx": "The built-in automatic warm-up feature for batch sending intelligently determines the reputation of the sender domain/IP and the maximum number of sent emails allowed per day. When this daily limit is reached, email sending will stop and extra emails will enter the cache queue and be automatically sent 24 hours later. For more details, see <1>Features - Automatic Warm-up</1>.", "k_0rf6ecf": "A domain can be used for multiple sending tasks, but the total email volume of multiple sending tasks in a day must not exceed the maximum number allowed per day.", "k_13bjqni": "When a sending task enters the cache queue, its status is Paused and the sending progress bar remains static. After automatic restart of sending the next day, its status becomes Sending and the progress bar grows automatically.", "k_17qt72f": "It is suitable for batch sending of marketing and notification emails. To send triggered emails (such as authentication and transaction emails), you are advised to call the SendEmail API.", "k_1c3oivm": "SMTP server: {{url}}; SMTP port: {{port}}", "k_0otohur": "View limit increase rules", "k_15i847j": "Increase rules", "k_0oqqlf5": "The sender name cannot be in the format of an email address.", "k_168ia83": "If a domain also provides email services (an MX record already exists), \"mxbiz1.qq.com\" is not required. For more details, see Help.", "k_0c9fju5": "Recipient Group ID", "k_0yivubb": "Group ID", "k_0e6rvob": "When the remaining emails in your plans are used up, you will be charged according to your actual usage. For more information, please see <1>Pricing</1>. ", "k_0o0gabu": "SES is pay-as-you-go now. Resource plan is not supported. ", "k_0c81sfw": "Delay sending", "k_1du7ojz": "Incorrect email address", "k_02iq161": "Closed", "k_03eq60o": "Opened", "k_09o7u99": "<PERSON>licks Count", "k_09zw3kv": "No longer counting email opens", "k_0cwp8fz": "Email Opens Count", "k_0hts7rv": "If the email tracking data capability is disabled, the number of users opening emails or clicking links from the corresponding account or sender domain will not be counted. ", "k_0laksld": "After enabling, you can view the number of link clicks from emails sent by all sender domains under this Tencent Cloud account on the <1>Email tracking data</1> page.", "k_0lebfg9": "Already exists.", "k_0os6ph1": "After disabling, the link clicks from emails sent by all sender domains under this Tencent Cloud account will not be counted.", "k_0q11971": "No longer counting link clicks", "k_0qjk7uz": "After enabling, you can view the number of email opens from all sender domains under this Tencent Cloud account on the <1>Email tracking data</1> page.", "k_0rxmyoy": "Enable link clicks count", "k_112oonx": "Sender Domain: {{domain}}", "k_12clex7": "Statistics settings", "k_12cnryb": "Batch Set", "k_180o0c1": "Enter the sender domain", "k_18qrgl2": "Account level tracking data count settings", "k_1uec10n": "The {{count}} sender domain selected this time", "k_1uec10n_0": "The {{count}} sender domain selected this time", "k_1uec10n_plural": "The {{count}} sender domains selected this time", "k_1kbidrz": "After disabling, the email opens from all sender domains under this Tencent Cloud account will not be counted.", "k_1ppsbbl": "<0><0></0> User statistics for opened emails</0><1><0></0> User statistics for clicked links</1>", "k_1s4j10w": "Enable email opens count", "k_1dqdlmk": "Selected {{count}} sender domain", "k_1dqdlmk_0": "Selected {{count}} sender domain", "k_1dqdlmk_plural": "Selected {{count}} sender domains", "k_1yyvzqd": "Sender domain level tracking data count settings", "k_0lnt5gx": "不添加MX记录可能会导致部分类型的邮箱无法收件。少部分邮件服务商要求SPF记录中的域名与MX对应，否则无法递送。如果您已经使用了其它MX记录，需要再添加一条，请将上述的MX优先级降低（数值越大优先级越低）", "k_1jy0x9g": "套餐包邮件发送总量：{{count}}万封", "k_1jy0x9g_0": "套餐包邮件发送总量：{{count}}万封", "k_1jy0x9g_plural": "套餐包邮件发送总量：{{count}}万封", "k_1q08yw3": "请输入邮件标题", "k_1xjmi6g": "当前剩余邮件发送总量：{{count}}万封", "k_1xjmi6g_0": "当前剩余邮件发送总量：{{count}}万封", "k_1xjmi6g_plural": "当前剩余邮件发送总量：{{count}}万封", "k_002qzi3": "Disable", "k_003pvbe": "Enable", "k_0s0c637": "为方便处理，下载内容使用CSV格式，使用Excel等工具查看时可以会有展现格式转换。", "k_1ah7rvh": " 时间使用毫秒时间戳的形式，可以通过excel的公式转为可读时间。例：=TEXT(C2/86400000+DATE(1970,1,1), \"YYYY-MM-DD HH:mm:ss\") 其中C2为时间戳所在的单元格，输出的时间为UTC时间。", "k_0r6j27c": "CSV files are supported (<7>download template</7>); a variable name in the file can only contain letters (a-z, A-Z), digits (0–9), and underscores (_).", "k_16s2zxs": " ", "k_003nbma": "Period", "k_03cxs8z": "One-time", "k_0crezr5": "Every {{count}} day", "k_0crezr5_0": "Every {{count}} day", "k_0crezr5_plural": "Every {{count}} days", "k_0fguaoh": "Every {{count}} hour", "k_0fguaoh_0": "Every {{count}} hour", "k_0fguaoh_plural": "Every {{count}} hours", "k_1g5els9": "A bounce address will be blocklisted for 180 days. You can click \"Unblocklist\" to remove the address from the blocklist.", "k_002r1jz": "Open", "k_002wh4y": "Query", "k_003j4vl": "Disable", "k_003j5ky": "Enable", "k_007cu5m": "Please contact <1>Tencent Cloud Online Customer Service</1>", "k_03c67bm": "Please select", "k_03fkcas": "Please enter", "k_08bw07r": "If you want to add an independent IP, please contact <1>Tencent Cloud Online Customer Service</1>.", "k_0askmaa": "An independent IP will not be interfered by other users sending emails. It can ensure the credibility of the sending domain name and IP and improve the email arrival rate. Click to learn <1>price details</1>", "k_0b50cbv": "Please <1>submit a ticket</1> to contact us", "k_0c6jivb": "Open now", "k_0ef4qs6": "If you want to add an independent IP, please <1>submit a ticket</1> and contact us.", "k_0fs0zmp": "Deactivate the independent IP in the current month: The deactivation fee for the current month will still be calculated based on the actual number of days of use and will be reflected in the bill on the 1st of the next month.", "k_0gqzxfi": "Add independent IP in the current month: If it is less than one natural month, it will be converted according to the actual number of days used in the month.", "k_0svjhyw": "To add and deactivate independent IP, please <1>submit a ticket</1> to contact us.", "k_0ykg32z": "Independent IP", "k_12o684n": "Configure at least one sending domain name and one sending address before activation, click <1><0><0></0></0></1>", "k_12swybn": "Learn more", "k_180u7no": "Configure sending domain name", "k_1af344z": "Independent IP resources need to be warmed up and prepared in advance in the background. When there is insufficient inventory, it usually takes 2-4 weeks.", "k_1h56xau": "At least one sending domain name and one sending address have been configured, click <1><0></0></1>", "k_1mckgxl": "To add and deactivate independent IP, please contact <1>Tencent Cloud Online Customer Service</1>.", "k_1ngtyqn": "I have read and agreed to Tencent Cloud Email Push <1> \"Service Agreement\"</1>", "k_1onpi96": "Note: A maximum of 3 independent IP applications are supported per month.", "k_1rir0ao": "It will not be interfered by other users sending emails. It can ensure the credibility of the sending domain name and IP and improve the email arrival rate.", "k_1rx5lef": "Supports application for up to 3 independent IPs per month.", "k_1t0ektx": "Independent IP value-added service", "k_1wz78ek": "Start billing date", "k_1x5rjuo": "Add independent IP", "k_01165mp": "I have read and agreed to  <1> \"TENCENT CLOUD TERMS OF SERVICE\"</1>", "k_002r79h": "All", "k_003tnp0": "File", "k_03be766": "Expired", "k_03i8dlr": "Effective", "k_045yp2t": "Custom blacklist", "k_06ug6sy": "The {{errIndex}}th email address format is incorrect", "k_0armcjx": "Use the Enter key to change the line. One line is regarded as one receiving email address. You can enter up to 100 email addresses. A total of <1><0></0></1> email addresses were entered.", "k_0b88rbc": "Please select a date", "k_0clrvu3": "After deletion, the receiving email address will be removed from the blacklist and emails can be received", "k_0e866wo": "Supports uploading 20,000 email addresses at a time, please upload a csv file. Please fill in the recipient email address according to the template requirements<2>Download standard template</2>", "k_0hf7x3z": "Example of receiving email address: <EMAIL>", "k_0jtq3b2": "Modify {{email}}", "k_10l6ehq": "The receiving email address is required", "k_1330o12": "Expiration date", "k_15wgku3": "Operation successful", "k_15wrir2": "Deleted successfully", "k_167im9c": "Manual input", "k_16pfvsz": "Entry method", "k_18nbn3t": "Batch upload", "k_19ru9zk": "Is it permanently valid?", "k_1ff0y1c": " or drag it here", "k_1pg4eaj": "Add a custom blacklist", "k_1xofdc7": "Are you sure you want to delete {{email}}", "k_003kim4": "Okay", "k_003l8z3": "Tip", "k_003qjuk": "Recharge", "k_003qnm7": "Return", "k_01m9hek": "<0>submit a ticket</0> contact us", "k_02jmg04": "Modification failed, please try again later.", "k_02pk5fc": "Notes:", "k_068ld1s": "The pre-frozen fees during activation will be returned to your account later. After a few minutes, you can click the official website console > Fee Center > <2><0></0></2> to view the unfreeze Details.", "k_07w0bdb": "After deactivation, all configurations related to independent IP {{formatIPList}} will be deleted, and the platform will use shared IP to send messages, and the effect may be affected.", "k_08v1e69": "To add an independent IP, please contact <1>Tencent Cloud Online Customer Service</1>.", "k_0bw1rbc": "Unfreeze funds", "k_0bybdc3": "Update time", "k_0c4qp7v": "Please try again later.", "k_0c53l1e": "Are you sure to disable [Independent IP value-added service]?", "k_0dco140": "To add an independent IP, please <1>submit a ticket</1> to contact us.", "k_0emhs82": "Deactivate independent IP successfully.", "k_0f7em2x": "Unfreezing failed", "k_0fkenrp": "Are you sure you want to disable independent IP{{formatIPList}}?", "k_0glj5qy": "The \"Unfreeze Funds\" operation can only be performed after the [Independent IP Value-Added Service] is successfully terminated and the deduction is completed on the 1st of the following month.", "k_0ijk07x": "[Independent IP value-added service] has been successfully deactivated", "k_0iu8eqx": "Independent IP resources require preparation by the R&D team in advance. In order to ensure that the resources can be used reasonably, the activation fee needs to be frozen for one month in advance (<1></1>). Click to learn about <3><0> freezing instructions. </0></3>. Please make sure to replenish enough, click to go to <5><0><0></0></0></5>.", "k_0mkhlai": "After the independent IP is deactivated, all independent IP related will be deleted, the platform will use the shared IP to send messages, and the arrival rate may be reduced.", "k_0q3xhma": "The earliest frozen fee for activating [Independent IP Value-Added Service] is {{price}}. Your account balance is currently insufficient, please recharge first. (Vouchers cannot be used to offset the frozen fee)", "k_0s35lu1": "(After the funds are unfrozen next month, the freezing fee of {{price}} will be returned to your account. At that time, you can click the official website console > Fee Center > <4><0></0 > </4>You can view the unfreezing details)", "k_0ti5yya": "Contact online customer service", "k_0v715pl": "In the month of expiration, the fee for the month of expiration will still be calculated based on the actual number of days of use and will be included in the invoice on the 1st of the next month.", "k_0wajnwd": "contact <1>Tencent Cloud Online Customer Service</1>", "k_0xaqyke": "<0>submit a ticket</0> contact us.", "k_0y7hqy6": "[Independent IP value-added service] has been successfully activated. You can later click on the official website console > Fee Center > <2><0></0></2> to view the freezing details.", "k_0zs56t7": "Confirm deactivation", "k_11r9mft": "Failed to deactivate independent IP, please try again later.", "k_11s9gp1": "Configure at least one sending domain name and one sending address before activation. Click to go to <1><0><0></0></0></1>.", "k_11y596c": "Income and expenditure details", "k_13d3xmk": "[Independent IP value-added service] has been successfully deactivated on {{stopTime}}.", "k_14by2t4": "Independent IP is not allowed to be deactivated in the month it is opened.", "k_15gdrvi": "Detailed bill", "k_15p1s0v": "900 yuan", "k_15t92ap": "Submit a ticket", "k_15u9o8t": "Disable service", "k_15weqxe": "<PERSON><PERSON><PERSON><PERSON> successfully", "k_17kqpnb": "After deactivation, all configurations related to independent IP will be deleted, and the platform will use shared IP to send messages, and the effect may be affected.", "k_19bsc4b": "$120", "k_1b4zf1w": "Note: Independent IP is a resource that has been pre-heated and prepared by the R&D team in advance. In order to ensure that the resources can be used reasonably and effectively, and to allow you to experience the actual improvement effect of independent IP on the delivery rate, it is recommended to use at least one If you have any other questions, you can consider whether you need to deactivate it after a few months.", "k_1fa64q0": "Add an independent IP in the current month. If it is less than one calendar month, the calculation will be based on the actual number of days used in the month.", "k_1fv38vz": "Modification successful.", "k_1i8q312": "After deactivating the service, you need to activate it again when you use it again.", "k_0qfek9f": "Independent IPs newly added in the current month cannot be deactivated.Independent IP is a resource that has been preheated and prepared by the R&D team in advance. In order to ensure that resources can be used reasonably and effectively, and to allow you to experience the actual improvement effect of independent IP on the delivery rate of emails, it is recommended to use it for at least one month before considering whether to deactivate it. If you have any other questions,", "k_1syq7np": "Freezing instructions guide", "k_1tljcg6": "In order to ensure that resources are used reasonably, [independent IP value-added service] needs to be activated. The platform estimates that the freezing fee is {{price}}, and the actual frozen amount shall be subject to the bill.", "k_1w1ef1y": "Sorry, deactivation of [Independent IP value-added service] is not supported in the month of activation", "k_1x5ygrz": "Disable independent IP", "k_1y3qd5r": "contact <1>Tencent Cloud Online Customer Service</1>.", "k_1y49of7": "Note: After the deduction is completed on the 1st of the next month, the \"unfreeze funds\" operation can be performed.", "k_002rkhe": "Variable", "k_003lzes": "<PERSON><PERSON>", "k_003lzsd": "Invalid", "k_003m86e": "Reset", "k_003polf": "Export", "k_003qdlq": "Add", "k_01z222r": "JSON format is wrong, please fill it in according to the above example format!", "k_03aunyy": "The number of imported dishes cannot exceed 1 million.", "k_03ej395": "To be imported", "k_03ibj2i": "Processing", "k_0737skn": "Upload file requirements:", "k_0cnwdpu": "The variable length cannot exceed 1000 characters, please re-enter!", "k_0esaq13": "Example: {\"Username\":\"Zhang San\",\"City\":\"Beijing\"}", "k_0g99xvs": "When there are \"duplicate pearls\", \"pearls are empty\", and \"the entire pearl json length exceeds 1000 characters\" in the file, they will be cleared directly when uploading.", "k_0i8u1uy": "Note:", "k_0j2buvy": "Modify {{email}}", "k_0jy58bx": "Please confirm the deletion operation", "k_0lc4p5f": "Import completed - all valid\" status hunger list is available, other status hunger lists are <1>unavailable</1>. When the status is \"Import completed - partially valid\", please <3>click edit</3> to modify or delete the \"Invalid\" status. ", "k_0mfjq2c": "Total: <1>{{TotalCount}} </1>, valid: <3>{{ValidCount}} </3>, invalid: <5>{{InvalidCount}} </5 > recipients", "k_0ndboqh": "The imported file contains garbled characters, please make sure you are using UTF-8 encoding.", "k_0vi0a5d": "The recipient's email address cannot be empty!", "k_0ye5gww": "{\"Please enter the variable name 1\":\"Please enter the variable value\",\n\"Please enter the variable name 2\":\"Please enter the variable value\",\n... }", "k_0yzf3p4": "The email address format is entered incorrectly!", "k_101kwfv": "After deletion, {{Email}} will be removed from the recipient list. Are you sure to delete it?", "k_11gjjmq": "Import completed - partially valid", "k_13w7mgb": "Import status", "k_13wa1rs": "Upload failed, please try again", "k_14rb87s": "Recipient Edit", "k_16d1saj": "Please confirm the batch deletion operation", "k_18dm8wc": "Import completed - all valid", "k_1boy8af": "Refers to the number of valid recipients imported.", "k_1bqytkb": "Supports CSV format files (<1>Click to download template file</1>); each recipient list supports uploading up to 1 million recipient addresses.", "k_1cyubfw": "File reading failed, please try again.", "k_1j1k5h6": "Each recipient list supports uploading up to 1 million recipient addresses, and the size does not exceed 100Mb.", "k_1jd15tw": "Add recipient", "k_1kdb0kv": "After deletion, these recipients will be removed from the recipient list. Are you sure to delete?", "k_1ko06jz": "Import completed - all valid status recipient lists can be used, other status recipient lists cannot be used.", "k_1o4jwea": "You need to fill in the variables in JSON format here {\"Variable name 1\":\"Variable value\",\"Variable name 2\":\"Variable value\",...}", "k_1s5cukh": "Note: Only the list of recipients whose status is \"Import completed - all valid\" is included, <1>go to view</1>", "k_1s9fdbo": "This list is unavailable. There are {{count}} \"invalid\" recipients. Please click Edit \"Modify\" or \"Delete\"", "k_1s9fdbo_plural": "This list is unavailable. There are {{count}} \"invalid\" recipients. Please click Edit \"Modify\" or \"Delete\"", "k_1tn4vw6": "Variables cannot be empty, please enter variables in json format!", "k_15wdq5o": "Export successful", "k_1ikwphs": "(if there are \"duplicate recipients\", they will be eliminated directly)", "k_1k6flox": "(if there are \"duplicate recipients\", \"recipient is empty\", \"variable data exceeds 1000 characters\", they will be eliminated directly)", "k_0o6s2aw": "There are {{count}} \"invalid\" recipients. Please click Edit \"Modify\" or \"Delete\"", "k_0o6s2aw_0": "There are {{count}} \"invalid\" recipients. Please click Edit \"Modify\" or \"Delete\"", "k_0o6s2aw_plural": "There are {{count}} \"invalid\" recipients. Please click Edit \"Modify\" or \"Delete\"", "k_0xf9ztc": "Importing, please do not leave the current interface...", "k_1iypwwr": "When the recipient status is \"invalid\", <1>please modify or delete the \"invalid\" recipient.<1/>", "k_1ja9fm5": "Original file: <1>{{totalCount}}</1> Recipients (excluding blank lines) <3></3> Actual upload: <5>{{validCount}}</5> Recipients<7>{{actualMsg}}</7>", "k_01hc00s": "Note: The import status of the recipient list is \"Import completed - partially valid\". <1>Go to edit</1> to modify or delete \"invalid\" recipients", "k_0dvce33": "Supports uploading 500,000 email addresses at a time, please upload a csv file. Please fill in the recipient email address according to the template requirements<2>Download standard template</2>", "k_0k4y0s3": "The imported file contains no valid email addresses.", "k_10a4phq": "The number of imported dishes cannot exceed 500,000.", "k_1qb2isu": "Each recipient list supports uploading up to 500,000 recipient addresses, and the size does not exceed 100Mb.", "k_1vd8lor": "The number of imported email addresses cannot exceed 500,000.", "k_1wp19un": "Supports CSV format files (<1>Click to download template file</1>); each recipient list supports uploading up to 500,000 recipient addresses.", "k_0003zby": "Total", "k_003m98p": "By day", "k_003n1ok": "By month", "k_0bjr4fa": "Note: The receiving email address is case sensitive, please fill in the correct receiving email address", "k_0ld10w7": "Please select a time range", "k_0qpvlj5": "Original file: {{TotalCount}} recipient email addresses (excluding blank lines)", "k_11nii1b": "Supports uploading 500,000 email addresses at a time, please upload a csv format file. Please fill in the recipient email address according to the template requirements <1>Download standard template</1>", "k_1dhmv6f": "Actually transmitted: {{ValidCount}} recipient email addresses (there are \"duplicate email addresses\" and \"email address format is correct\", which have been cleared)", "k_13rt15y": "If you no longer use the email push service, please deactivate the independent IP service in time, otherwise the fee for the previous month will be automatically deducted at the beginning of each month.", "k_1txqw4q": "I have read and agreed to <1>Tencent Cloud Email Push Service Terms</1><2>Billing Rules</2>", "k_00046tr": "piece", "k_002wmu9": "apply", "k_01frutw": "1. After deletion, the independent IP {{ip}} will have no configured sending domain names.", "k_038j3oj": "Disabled", "k_03iawv3": "In use", "k_05wg7vc": "To ensure the reasonable use of resources, a fee of <1></1> needs to be pre-frozen when opening. Learn about <3><0>Freeze Instructions</0></3>. Please ensure there is sufficient balance and go to <5><0><0></0></0></5>.", "k_07k2v1h": "Independent IP {{index}}", "k_09c9c98": "Please select an independent IP", "k_09og6dy": "Failed to obtain the IP list. Please try again later.", "k_09uutmb": "1. After deactivation, all sending domain name configurations related to the independent IP {{ip}} will be deleted, and the platform will use a shared IP for sending. The effect may be affected.", "k_0au6k9k": "Independent IP cannot be added temporarily. Please first activate the [Independent IP Value-added Service].", "k_0bhs7u7": "Failed to obtain account information. Please try again later.", "k_0bye8ab": "Deactivation Time", "k_0c5scq9": "Confirm Closing", "k_0c6jvde": "Go to Activate", "k_0cjfhqj": "Confirm Deletion", "k_0d458ip": "Currently, it includes the newly added independent IPs of the current month, so the service cannot be closed.", "k_0e5eivz": "A maximum of {{MAX_IP_COUNT}} independent IPs can be applied for each month. If you have any questions, please <3>contact Tencent Cloud Online Customer Service</3>.", "k_0fux5it": "Application Suggestions", "k_0g16c4b": "Pay-as-you-go", "k_0gt7qpi": "After closing, the independent IP bound to this sending domain name will be cleared, and the system will default to using a shared IP for sending.", "k_0gws9kb": "Confirm Application", "k_0hen4nz": "Failed to close the independent IP value-added service. Please try again later.", "k_0hquty1": "Failed to deactivate the independent IP. Please try again later.", "k_0hr0ily": "Failed to add the independent IP. Please try again later.", "k_0iy7jhw": "3. In the month of deactivation, the fee for that month will still be calculated according to the actual number of days used and reflected in the bill on the 1st day of the following month.", "k_0iy7jhx": "2. In the month of deactivation, the fee for that month will still be calculated according to the actual number of days used and reflected in the bill on the 1st day of the following month.", "k_0l9qc0l": "The independent IP is configured based on the sending domain name dimension. Please complete <1><0><0></0></0></1> first.", "k_0lve5mu": "There are currently {{count}} independent IPs in use", "k_0lve5mu_plural": "There are currently {{count}} independent IPs in use", "k_0lyku9m": "2. After closing the service, after deducting the fee for the current month before the 3rd day of the following month, the pre-frozen funds when opening will be automatically returned to the account. At that time, you can click Console > Cost Center ><1></1> to view the details.", "k_0mvti54": "Please enter the sending domain name to search", "k_0nnwgjs": "Note: A maximum of 3 independent IPs can be applied for each month. Click to view.", "k_0p2bnoz": "2. After closing the service, after deducting the fee for the use of independent IPs in the current month before the 3rd day of the following month, the pre-frozen funds when opening will be automatically returned to the account. At that time, you can click Console > Cost Center ><1></1> to view the details.", "k_0p4tuwv": "2. If you still need the independent IP to continue helping you send emails to improve the delivery rate, please add a sending domain name to it in a timely manner.", "k_0p7ecrc": "1. Before deactivating the IP value-added service, you need to deactivate the independent IPs in use first: {{ips}}", "k_0q6iu90": "Sorry, the current inventory of independent IPs is insufficient.", "k_0qnavzs": "Precondition: At least one sending domain name and one sending address have been configured. Click <1><0></0></1>", "k_0sl30z4": "Insufficient balance in the current account", "k_0tcjlin": "Successfully added the independent IP", "k_0tcqidc": "Successfully deactivated the independent IP", "k_0tdukmp": "No sending domain name can be added temporarily", "k_0tr8izd": "2. After closing the service, the pre-frozen funds when opening will be automatically returned to the account. Please click Console > Cost Center ><1></1> later to view the details.", "k_0v8o8jm": "Can't find the sending domain name? <1>Refresh</1> or <3>Create a New Sending Domain Name</3>", "k_0w2tyt8": "After closing, it can be reactivated later.", "k_0w4n1i6": "Only the verified sending domain names can be configured with an independent IP", "k_0w77lrk": "Please select the sending domain name", "k_0wg1jp4": "Deactivate all independent IPs in use", "k_0xacaac": "Are you sure you want to deactivate the independent IP {{ip}}?", "k_0ykdkek": "Sending IP", "k_0yke4yu": "Shared IP", "k_0z94o9h": "Failed to delete the sending domain name. Please try again later.", "k_0z95hrj": "Failed to close the sending domain name. Please try again later.", "k_0za3sv2": "Failed to open the sending domain name. Please try again later.", "k_0za4i8a": "Failed to add the sending domain name. Please try again later.", "k_0zo804b": "After deletion, this configuration cannot be restored.", "k_1116hfx": "No independent IP can be added temporarily", "k_11mzt9a": "The independent IPs with the status of \"In use\" will have the fees of the previous month automatically deducted before the 3rd day of each month. If you no longer want to use them, please deactivate the independent IPs in a timely manner to avoid continuous fees.", "k_11qfqwz": "The independent IPs are resources that the R&D team has pre-warmed up and prepared in advance. To ensure the reasonable and effective use of resources, <1> a maximum of 3 independent IPs can be applied for each month</1>. Learn about <3>Application Suggestions</3>.", "k_11zxz9r": "If you want to deactivate all independent IPs and no longer use them in the future, please click \"Close Service\"; if you want to deactivate a single independent IP, please click the \"Deactivate\" button in the operation column.", "k_12d8i0r": "A maximum of {{count}} independent IPs can be applied for", "k_12d8i0r_plural": "A maximum of {{count}} independent IPs can be applied for", "k_12kunhn": "Failed to apply for the independent IP. Please try again later.", "k_13h5h41": "Successfully activated the [Independent IP Value-added Service]", "k_13qhqgp": "No data available", "k_13u5j9i": "I have read and agree to <1>《Billing Rules》</1>", "k_14jg966": "The independent IP has been issued and the application is successful.", "k_15thjg7": "Close Service", "k_161gnjl": "Go to Recharge", "k_16ang1t": "Are you sure you want to delete the sending domain name {{domain}}?", "k_16katx7": "Are you sure you want to close the sending domain name {{domain}}?", "k_180nsos": "Add Sending Domain Name", "k_18uv0lw": "Billing Start Time", "k_1cocynt": "Once an independent IP is applied for, the billing will start. It cannot be deactivated in the current month.", "k_1dagy9p": "The newly added independent IPs in the current month cannot be deactivated.", "k_1dxy4ml": "Successfully activated the [Independent IP Value-added Service] and frozen the fee of {{price}}. Click Console > Cost Center ><3></3> to view the details.", "k_1eg8zkk": "1000 free test emails. For emails sent exceeding the free quota, the billing will be based on the actual usage. View <1>Price Instructions</1>.", "k_1ega5u3": "Close the Independent IP Value-added Service", "k_1g0eiio": "Once an independent IP is configured, the billing will start. Before the 3rd day of each month, the fees of the previous month will be <1>automatically deducted according to the number of independent IPs configured and enabled under your account in the previous month</1>. For details, see <3>Billing Overview</3>.", "k_1g2mytm": "Are you sure you want to apply for an independent IP?", "k_1h0rkzo": "Number of Independent IPs", "k_1jwyge9": "Refreshing...", "k_1k29xaf": "Please enter the independent IP or sending domain name to search", "k_1l3buq3": "Failed to apply for the independent IP", "k_1l4onl0": "Successfully closed the independent IP value-added service", "k_1lvt8sj": "1. After closing the service, it needs to be reactivated when using it again.", "k_1m82lsq": "1. After closing, the independent IP {{ip}} will have no effective sending domain names.", "k_1mcume1": "I have read and understood the above information.", "k_1nj4qto": "Successfully deleted the sending domain name", "k_1nj5ddi": "Successfully closed the sending domain name", "k_1njgn47": "Successfully opened the sending domain name", "k_1njhumr": "Successfully added the sending domain name", "k_1nkrqi2": "2. After deactivation, all sending domain names related to the independent IP will be deleted, and the platform will use a shared IP for sending. The effect may be affected.", "k_1o0af49": "3. If you don't want to continue using the independent IP {{ip}} service, please deactivate the independent IP in a timely manner to avoid continuous fees.", "k_1ojhv7d": "The system has received your request and has started pre-warming up. Please be patient and try to add it again after 2-4 weeks.", "k_1po02h2": "Recharge Freeze Guide", "k_1rsfidt": "Please select a sending domain name for each independent IP", "k_1soc5qp": "If you have already activated the [Independent IP Value-added Service], please refresh the page and then try to add an independent IP.", "k_1sx77qi": "The independent IP adopts the postpaid monthly settlement method. The fees of the previous month will be automatically deducted before the 3rd day of each month. If you no longer want to use it, please deactivate the independent IP or close the service in a timely manner. The independent IP cannot be deactivated in the month it is added.", "k_1tb02j0": "You have not activated the [Independent IP Value-added Service]", "k_1v7ak6e": "If your domain name is hosted on Tencent Cloud, please go to <1>D<PERSON><PERSON>od Console</1> to configure the domain name verification information; if your domain name is hosted on other domain name service providers, please configure it according to the detailed domain name configuration information by yourself.", "k_1wlf9mw": "Are you sure you want to close the [Independent IP Value-added Service]?", "k_1wy208t": "Failed to obtain the domain name list. Please try again later.", "k_1x08p4z": "Can't find the independent IP? <1>Refresh</1> or <3>Apply for an Independent IP<1></1></3>", "k_1x5dqdb": "Apply for an Independent IP", "k_1x84ne4": "Are you sure you want to activate the [Independent IP Value-added Service]?", "k_1ykndoj": "Closing the independent IP value-added service will deactivate all independent IPs currently in use at the same time.", "k_0qyfwtl": "Unfreeze failed, please try again later", "k_1tdyiuz": "If unfreeze failed, please make sure the deduction on the 1st of the next month has been completed", "k_03h1j7j": "Will you submit, upload, transmit, display or instruct Tencent Cloud and the selected Tencent Cloud service to process U.S. sensitive personal data and/or U.S. government-related data (as defined in the U.S. Department of Justice Data Security Program, available at 28 CFR Part 202 (\"DSP\")) (\"Covered Data\")? If so, your use of the selected Tencent Cloud services must be configured to comply with US CISA data-level and system and organizational Security Requirements, available <1>here</1>.", "k_06i9fj0": "Yes, we have or expect to store or process data that may include Covered Data, and have not fully complied with the CISA security requirements.", "k_0ax3epx": "Data that could be considered U.S. \"government-related data\" or \"U.S. sensitive personal data\" under recent U.S. Department of Justice regulations (28 C.F.R. Part 202, effective April 2025) – see the fact sheet <1>here</1>.", "k_0f1lrw6": "Please contact us to discuss your options for this product.", "k_0fpn2pn": "CONTACT TENCENT", "k_0h5slpt": "Thank you for your responses. If your data includes Covered Data (which you will know best), are you able to modify your use of the Tencent Cloud so that you do not use or store any Covered Data, and/or have you implemented and are you able to comply with the CISA security requirements with respect to the use of Tencent Cloud?", "k_0m7915e": "To ensure alignment with evolving U.S. regulatory standards, we ask that you take a moment to confirm the nature of the data you plan to store or process using our Tencent Cloud services. For more information, please see <1>link</1>.", "k_0mz2at5": "Thank you for your responses. We have noted this in our records.", "k_0olm81n": "Regulated Datasets relating to U.S. individuals' health, genetics, geolocation, finance, or similar sensitive categories, or", "k_0q47lta": "Why This Matters:", "k_0v1g2he": "We are standing by to support necessary adjustments to your planned use of services which may require processing of Covered Data. It will be important to review your architecture and support paths, identify options to restructure data locations or support tiers, or adjust your contracting plans to service models that align with the new DSP framework.", "k_0vyqrjj": "We are committed to helping new customers chart the best path forward.", "k_0ye1k0n": "Yes, we <1></1>(a) commit not to submit, upload, transmit, display or instruct Tencent Cloud and the selected Tencent Cloud service to process any Covered Data,  and/or <3></3>(b) certify that we will timely comply with the CISA security requirements with respect to the use of Tencent Cloud.", "k_11kje0o": "<0>Thank you for your responses. At this time we unfortunately <1>are unable to agree</1> to provide the selected Tencent Cloud service to you.</0>", "k_12ssof5": "The United States government has enacted the Data Security Program in an effort to regulate the access to certain Covered Data by certain countries of concern, including China. As such, certain additional security or other requirements may apply to how this type of data is stored, transmitted, or accessed — especially when handled by infrastructure with international affiliations. Our goal is to support your compliance by making sure we understand your needs up front.", "k_13r9vdv": "No, we have to upload Covered Data and do not expect to comply with the CISA security requirements.", "k_1c8an1m": "This step helps us understand whether your intended use may involve Covered Data, namely:", "k_1gyuovo": "To learn more about this regulation (28 CFR Part 202), please see the full text of the Final Rule <1>here</1>.", "k_1ioepz9": "If you later <1></1>(a) become aware that your use of our Tencent Cloud services involves storing or processing Covered Data, <3></3>(b) intend to expand or modify your use of our Tencent Cloud services to store or process Covered Data, and/or <5></5>(c) Covered Data is not stored in compliance with the CISA security requirements where applicable, <7>please notify us immediately</7>, so that the parties may mutually and independently assess and address DSP compliance obligations.", "k_1l7mwts": "Our use does not involve Covered Data, or our use is in compliance with CISA security requirements.", "k_1o00b2y": "Please answer the following question:", "k_1tl5lk8": "With respect to the classes of \"restricted transactions\" identified in the regulations, CISA developed security requirements to mitigate the risk of sharing U.S. sensitive personal data or U.S. government-related data with countries of concern or covered persons through restricted transactions. Please see CISA's security requirements <1>here</1>.", "k_07a5iko": "Tag name and tag value cannot be empty", "k_0kr1z2w": "Please add at least one tag", "k_0r1t0wf": "Tag editing failed, please try again later", "k_12b5vhw": "The tag has been edited successfully", "k_12gxfiz": "Edit tag", "k_12ra6gp": "Up to 10 tags can be added", "k_13gpoqc": "Failed to open tag editor", "k_0003jjb": ".", "k_002rkey": "Remove", "k_003kf3s": "Add whitelist", "k_003nkp5": "Block", "k_061zlqt": "Note: A high invalid address rate will be considered \"blind mass spam,\" affecting email delivery rate and the reputation of the sending domain. Please proceed with caution.", "k_06ptx4c": "Confirm deletion of the recipient email address {{email}}?", "k_08wa1u9": "Invalid email addresses are blocked", "k_0a7qxhn": "My trigger block list", "k_0bybl43": "Rejection Time", "k_0bz6p9h": "Unsubscribe Time", "k_0dp6s9e": "After removal, the platform will resume sending emails to this email address. Please follow the instructions in detail!", "k_0ebfqk5": "Auto Block - Inbox Full, Disabled!", "k_0f8drqm": "Removal Failed", "k_0fw2d12": "Block List", "k_0g3s0ym": "Confirm to add email address {{email}} to the whitelist?", "k_0gqtwb2": "Block Query", "k_0irme7v": "<EMAIL>", "k_0k6jmon": "After deletion, this recipient email address will no longer be blocked and can receive emails", "k_0m3achi": "Auto Block Rules", "k_0mg68be": "You can check whether the email address is determined to be invalid by the platform.", "k_0o67qgg": "Are you sure you want to remove the selected {{nums}} email addresses?", "k_0q5abf4": "Customize blocking", "k_0s2romp": "Add to whitelist failed", "k_0s6qr72": "Invalid address blocking triggered by emails sent from this account supports 'removal'; invalid address blocking triggered by other user accounts can only be 'whitelisted'.", "k_0vsfte0": "By default, data for the past 15 days is displayed. To search for more data, adjust the date range and search again.", "k_0wozwfj": "Query successful. No data is available for the current query criteria. Please modify the query criteria.", "k_0x4ttiv": "Blocked due to inbox full.", "k_0ygt8kv": "No query results yet.", "k_13i7d7y": "1. The platform will proactively intercept invalid addresses that do not exist, are unavailable, or have been inactive for a long time. They will be released after 180 days.", "k_13ji371": "Reject", "k_149jyfo": "If the recipient refuses to receive the email or blocks the sender, the platform will not send emails to the rejected email address. If you have any questions, please submit a ticket.", "k_15iko49": "Block type", "k_15wra7p": "Removed successfully.", "k_19snt8q": "Successfully added to the whitelist", "k_1erx7jm": "After adding to the whitelist, the platform will resume sending emails to this email address. Please ensure that the email address is valid to avoid an increase in invalid addresses that could lead to an increase in bounce rates, which could affect delivery rates and domain reputation. Please proceed with caution!", "k_1h0x0wj": "This email address is not on the platform's invalid address list. Please search again.", "k_1iys74w": "After removing, the platform will resume sending emails to this email address. Please ensure that the email address is valid to avoid an increase in invalid addresses that could lead to an increase in bounce rates, which could affect delivery rates and domain reputation. Please proceed with caution!", "k_1l8lm3v": "Auto-block - Inbox full is enabled!", "k_1nfdppo": "Confirm to remove email address {{email}}?", "k_1oe15rc": "Recommended. Sending to a \"full inbox\" for a long time may be considered \"harassing.\"", "k_1sq8802": "Operation failed.", "k_1t1gkut": "Please enter an email address to search.", "k_1u86ylm": "Unsubscribed sending domain name.", "k_1udcp0s": "Rejected sending domain name.", "k_1xir7nc": "2. If you confirm that your email address is valid, invalid address blocking triggered by emails sent from this account can be removed. Invalid address blocking triggered by other user accounts can only be whitelisted.", "k_1xyn48k": "The platform will proactively block invalid addresses that do not exist, are unavailable, or have been inactive for a long time, and release them after 180 days. Note: A high rate of invalid addresses will be considered \"blind mass spam,\" affecting email deliverability and the reputation of the sending domain.", "k_1y67fyv": "Please enter  an email address", "k_1yce9ih": "Please enter an email address to search"}