{"k_003rzap": {"rawText": "确定", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "Modal", "<PERSON><PERSON>"]}}, "k_003m86e": {"rawText": "重置", "staticAnalysis": {"componentStack": ["DynamicForm", "FinalForm", "form", "<PERSON><PERSON>"]}}, "k_13gpoqc": {"rawText": "打开标签编辑器失败", "staticAnalysis": {"componentStack": ["editResourceTag", "main"]}}, "k_15e62sn": {"rawText": "发信域名", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form.stats-search-query", "Justify", "Form"]}}, "k_0ykg32z": {"rawText": "独立IP", "staticAnalysis": {"componentStack": ["DedicatedIPCard", "Col", "Card"]}}, "k_12ra6gp": {"rawText": "最多可添加10个标签", "staticAnalysis": {"componentStack": ["validateTags"]}}, "k_07a5iko": {"rawText": "标签名和标签值不能为空", "staticAnalysis": {"componentStack": ["getTagValid"]}}, "k_12b5vhw": {"rawText": "标签编辑成功", "staticAnalysis": {"componentStack": ["EditTagModal"]}}, "k_0r1t0wf": {"rawText": "标签编辑失败，请稍后重试", "staticAnalysis": {"componentStack": ["EditTagModal"]}}, "k_12gxfiz": {"rawText": "编辑标签", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>"]}}, "k_003mam4": {"rawText": "标签", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_003nevv": {"rawText": "取消", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_003s3gw": {"rawText": "其它", "staticAnalysis": {"componentStack": ["TO_DOMAIN_LIST"]}}, "k_0c7w0m6": {"rawText": "开始发送", "staticAnalysis": {"componentStack": ["EVENT_TYPE"]}}, "k_02n71ag": {"rawText": "已送达", "staticAnalysis": {"componentStack": ["EVENT_TYPE"]}}, "k_053r8e7": {"rawText": "校验未通过", "staticAnalysis": {"componentStack": ["EVENT_TYPE"]}}, "k_003utac": {"rawText": "退信", "staticAnalysis": {"componentStack": ["STATISTIC_PATH"]}}, "k_003rcwm": {"rawText": "打开", "staticAnalysis": {"componentStack": ["EmailEventPage", "Layout", "Body", "Content", "Tabs"]}}, "k_003pg2v": {"rawText": "点击", "staticAnalysis": {"componentStack": ["EmailEventPage", "Layout", "Body", "Content", "Tabs"]}}, "k_0gt04ck": {"rawText": "垃圾投诉", "staticAnalysis": {"componentStack": ["EVENT_TYPE"]}}, "k_0bqcqu4": {"rawText": "取消订阅", "staticAnalysis": {"componentStack": ["EVENT_TYPE"]}}, "k_0c81sfw": {"rawText": "延期发送", "staticAnalysis": {"componentStack": ["EVENT_TYPE"]}}, "k_0gyzzkz": {"rawText": "发送错误", "staticAnalysis": {"componentStack": ["EVENT_TYPE"]}}, "k_06r0gzf": {"rawText": "打开/点击", "staticAnalysis": {"componentStack": ["STATISTIC_PATH"]}}, "k_13wcsoz": {"rawText": "垃圾举报", "staticAnalysis": {"componentStack": ["STATISTIC_PATH"]}}, "k_002v5d3": {"rawText": "退订", "staticAnalysis": {"componentStack": ["sceneList"]}}, "k_1307fpw": {"rawText": "不加入退订链接", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_129ekqk": {"rawText": "简体中文", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_003lpcj": {"rawText": "英文", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_129eldp": {"rawText": "繁体中文", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_0gv2ihv": {"rawText": "西班牙语", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_002wja5": {"rawText": "法语", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_002wflr": {"rawText": "德语", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_002whil": {"rawText": "日语", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_002wqld": {"rawText": "韩语", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_0gn49dd": {"rawText": "阿拉伯语", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_002wjaw": {"rawText": "泰语", "staticAnalysis": {"componentStack": ["unsubscribeList"]}}, "k_1jwyge9": {"rawText": "刷新中...", "staticAnalysis": {"componentStack": ["useOptionsTips", "div", "Text"]}}, "k_13qhqgp": {"rawText": "暂无数据", "staticAnalysis": {"componentStack": ["useTableTopTip", "tableTopTip", "StatusTip", "Status"]}}, "k_15tg6qb": {"rawText": "开通服务", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content"]}}, "k_15wpfw0": {"rawText": "开通成功", "staticAnalysis": {"componentStack": ["AccountOpenPage", "onCreateSubmit"]}}, "k_14cnlbh": {"rawText": "<0>欢迎使用邮件推送SES！</0><1>在开始使用前，请认真阅读该产品的<1>计费规则</1></1><2>点击确认开通，即可开始使用我们的产品。</2>", "staticAnalysis": {"componentStack": ["OpenAccountForm", "form.open-account-form ", "Form"]}}, "k_0c6cj11": {"rawText": "确认开通", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "Modal", "<PERSON><PERSON>"]}}, "k_0m7915e": {"rawText": "为确保符合美国不断更新的监管标准，请确认您当前及预期通过腾讯云服务存储或处理的数据性质。更多信息请参阅<1>链接</1>。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderStep1", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_1o00b2y": {"rawText": "请回答以下问题：", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_03h1j7j": {"rawText": "贵方是否会提交、上传、传输、展示或指示腾讯云及所选腾讯云服务处理美国敏感个人数据及/或美国政府相关数据（定义见美国司法部《数据安全计划》（《联邦法规》第28章第202部分））（以下简称\"受限制数据\"）？若涉及此类数据，贵方使用所选腾讯云服务时必须配置为符合美国网络安全和基础设施安全局（CISA）数据级及系统与组织安全要求（详见<1>此处</1>）。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderStep1", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_06i9fj0": {"rawText": "是，我方预计存储或处理的数据可能包含受限制数据，且尚未完全符合CISA安全要求。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "Form", "Radio"]}}, "k_1l7mwts": {"rawText": "我方使用场景不涉及受限制数据，或已符合CISA安全要求。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "Form", "Radio"]}}, "k_003tywh": {"rawText": "提交", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal", "form", "<PERSON><PERSON>"]}}, "k_0mz2at5": {"rawText": "感谢您提供的信息。我们已将此记录在案。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_1ioepz9": {"rawText": "若贵方后续出现以下情形： <1></1>(a) 发现使用腾讯云服务涉及存储或处理受限制数据；<3></3>(b) 拟扩展或变更腾讯云服务用途以存储或处理受限制数据；和/或<5></5>(c) 受限制数据的存储不符合适用的CISA安全要求，<7>请立即通知我方</7>，以便双方可共同或分别评估并履行《数据安全计划》合规义务。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderStep2A", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_002qzi3": {"rawText": "关闭", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "handlerSubmit"]}}, "k_0h5slpt": {"rawText": "感谢您的回复。若贵方数据包含受限制数据（贵方应最为了解），贵方是否能够调整腾讯云使用方式以避免使用或存储任何受限制数据，和/或贵方是否已实施并是否能够遵守与腾讯云使用相关的CISA安全要求？", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_0ye1k0n": {"rawText": "是，我方承诺：<1></1>(a) 不提交、上传、传输、展示或指示腾讯云及所选腾讯云服务处理任何受限制数据；和/或<3></3>(b) 确认将即刻实施并遵守与腾讯云使用相关的CISA安全要求。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderStep2B", "div.pre-open-check-step", "Form", "Radio"]}}, "k_13r9vdv": {"rawText": "否，我方必须上传受限制数据且预期无法遵守CISA安全要求。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "Form", "Radio"]}}, "k_003qnm7": {"rawText": "返回", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "div", "<PERSON><PERSON>"]}}, "k_1c8an1m": {"rawText": "此步骤旨在帮助我方了解贵方的预期使用行为是否可能涉及受限制数据，即：", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_0olm81n": {"rawText": "与美国个人健康、基因、地理位置、财务或类似敏感类别相关的受监管数据集；或", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "ul", "li"]}}, "k_0ax3epx": {"rawText": "根据美国司法部最新法规（《联邦法规》第28章第202部分，2025年4月生效）可能被认定为\"美国政府相关数据\"或\"美国敏感个人数据\"的内容——详见情况说明：<1>此处</1>。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderInfoPage", "div.pre-open-check-step", "div.step-content", "ul", "li"]}}, "k_0q47lta": {"rawText": "重要性说明：", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "h4"]}}, "k_12ssof5": {"rawText": "美国政府已颁布《数据安全计划》，旨在规范包括中国在内的特定受关注国家获取某些受限制数据的行为。因此，对此类数据的存储、传输或访问方式可能适用额外的安全或其他要求——特别是当涉及具有国际关联的基础设施处理时。我们的目标是通过预先了解贵方需求，协助贵方实现合规要求。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_1gyuovo": {"rawText": "如需了解该法规（《联邦法规》第28章第202部分）详情，请查阅最终规则全文：<1>此处</1>。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderInfoPage", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_1tl5lk8": {"rawText": "针对法规中定义的\"受限交易\"类别，美国网络安全和基础设施安全局（CISA）制定了安全要求，以降低通过受限交易向受关注国家或受限制主体共享美国敏感个人数据或美国政府相关数据的风险。请查阅CISA安全要求：<1>此处</1>。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderInfoPage", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_11kje0o": {"rawText": "<0>感谢您的回复。我们目前<1>无法同意</1>向贵方提供所选的腾讯云服务。</0>", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderBlocked", "div.pre-open-check-step", "div.step-content"]}}, "k_0v1g2he": {"rawText": "我们随时准备支持贵方对可能涉及处理受限制数据的服务使用计划进行必要调整。贵方需重点审查现有架构和支持途径，审视调整数据存储位置或调整支持层级的方案，或修改合同计划下的服务模式以符合新的《数据安全计划》框架要求。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_0vyqrjj": {"rawText": "我们致力于协助新客户规划最佳实施方案。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_0f1lrw6": {"rawText": "如需就该产品方案进行商讨，请联系我们。", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.step-content", "p"]}}, "k_0fpn2pn": {"rawText": "联系腾讯", "staticAnalysis": {"componentStack": ["PreOpenCheck", "renderCurrentStep", "div.pre-open-check-step", "div.button-group center", "<PERSON><PERSON>"]}}, "k_003r4v2": {"rawText": "正常", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table", "div.package-status package-status-"]}}, "k_03i9qmf": {"rawText": "隔离中", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table", "div.package-status package-status-"]}}, "k_03bkewo": {"rawText": "已隔离", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table", "div.package-status package-status-"]}}, "k_03dd1g6": {"rawText": "已销毁", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table", "div.package-status package-status-"]}}, "k_03ckobz": {"rawText": "已过期", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table", "div.package-status package-status-"]}}, "k_1acn7c0": {"rawText": "套餐包管理", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content"]}}, "k_03mjr2i": {"rawText": "套餐包ID", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_1bc7av2": {"rawText": "额度（封）", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0yz2e66": {"rawText": "套餐包内包含多少封邮件", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table", "Bubble"]}}, "k_1iqlrhq": {"rawText": "剩余额度（封）", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_1iqbxes": {"rawText": "已用额度（封）", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_003nzwy": {"rawText": "状态", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0bya3vq": {"rawText": "创建时间", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0bycvr3": {"rawText": "过期时间", "staticAnalysis": {"componentStack": ["AccountPackagePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_09kfj0x": {"rawText": "每个域名仅支持创建10个发信地址。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_1c3oivm": {"rawText": "SMTP服务地址：{{url}}，SMTP服务端口号：{{port}}。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_0izea64": {"rawText": "设置SMTP密码在5分钟内生效。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_0g7h09s": {"rawText": "请选择域名", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form.stats-search-query", "Justify", "Form", "Select.select-field"]}}, "k_054qosm": {"rawText": "此域名发信地址配置已达上限", "staticAnalysis": {"componentStack": ["AddressPage", "NewSenderModal"]}}, "k_1trzl1v": {"rawText": "确认删除当前所选地址？", "staticAnalysis": {"componentStack": ["AddressPage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_09qcps9": {"rawText": "删除后，不能再用该地址发送邮件。", "staticAnalysis": {"componentStack": ["AddressPage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_002rflt": {"rawText": "删除", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>"]}}, "k_15kilet": {"rawText": "发信地址", "staticAnalysis": {"componentStack": ["EmailSimulatorForm", "form.send-email-form ", "Form"]}}, "k_0djs6ds": {"rawText": "注意事项", "staticAnalysis": {"componentStack": ["StatusTable", "div", "<PERSON><PERSON>", "h4"]}}, "k_002uu5g": {"rawText": "收起", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "<PERSON><PERSON>", "Collapse", "Text"]}}, "k_003re34": {"rawText": "展开", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "<PERSON><PERSON>", "Collapse", "Text"]}}, "k_003r4i7": {"rawText": "新建", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Justify", "<PERSON><PERSON>"]}}, "k_0gsycam": {"rawText": "发件人别名", "staticAnalysis": {"componentStack": ["NewSenderModal", "Modal", "FinalForm", "form", "Form", "Field"]}}, "k_003u02c": {"rawText": "操作", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_1a4cii7": {"rawText": "修改SMTP密码", "staticAnalysis": {"componentStack": ["AddressPage", "PwdModal"]}}, "k_1a4fiiw": {"rawText": "设置SMTP密码", "staticAnalysis": {"componentStack": ["AddressPage", "PwdModal"]}}, "k_1pww5r3": {"rawText": "请输入发件人别名", "staticAnalysis": {"componentStack": ["validateName"]}}, "k_0oqqlf5": {"rawText": "发件人别名不允许使用邮箱格式", "staticAnalysis": {"componentStack": ["NewSenderModal", "Modal", "FinalForm", "form", "Form", "Field"]}}, "k_0ov552u": {"rawText": "请输入邮箱前缀", "staticAnalysis": {"componentStack": ["validatPrefix"]}}, "k_0lebfg9": {"rawText": "当前发信地址已存在", "staticAnalysis": {"componentStack": ["NewSenderModal", "Modal", "FinalForm", "form", "Form", "Field"]}}, "k_1jxgrp7": {"rawText": "加载中...", "staticAnalysis": {"componentStack": ["StatsSearchQuery"]}}, "k_0my1re7": {"rawText": "新建发信地址", "staticAnalysis": {"componentStack": ["NewSenderModal", "Modal"]}}, "k_0oez2pd": {"rawText": "每个域名仅支持配置10个发信地址", "staticAnalysis": {"componentStack": ["NewSenderModal", "Modal", "FinalForm", "form", "Form", "Field"]}}, "k_1p75gbm": {"rawText": "暂无发信域名，请先至<1>发信域名</1>新建", "staticAnalysis": {"componentStack": ["NewSenderModal", "Modal", "FinalForm", "form", "Form", "Field", "div"]}}, "k_11wfhx3": {"rawText": "邮箱前缀", "staticAnalysis": {"componentStack": ["NewSenderModal", "Modal", "FinalForm", "form", "Form", "Field"]}}, "k_1yxzdrd": {"rawText": "发信地址预览", "staticAnalysis": {"componentStack": ["NewSenderModal", "Modal", "FinalForm", "form", "Form"]}}, "k_1t9kzib": {"rawText": "请输入密码", "staticAnalysis": {"componentStack": ["PwdModal", "passwordValidate"]}}, "k_1dal177": {"rawText": "长度为10~20位，且必须包含数字、大写字母、小写字母。", "staticAnalysis": {"componentStack": ["PwdModal", "Modal.smtp-pwd-modal", "form", "Form", "div.tips"]}}, "k_0mf3mut": {"rawText": "至少包含2位数字、2位大写字母和2位小写字母，并且数字和字母均不能只由单一字符重复组成。", "staticAnalysis": {"componentStack": ["PwdModal", "Modal.smtp-pwd-modal", "form", "Form", "div.tips"]}}, "k_0es79co": {"rawText": "SMTP密码", "staticAnalysis": {"componentStack": ["PwdModal", "Modal.smtp-pwd-modal", "form", "Form"]}}, "k_17qt72f": {"rawText": "批量发送适用于营销类、通知类的邮件发送场景。触发类邮件（如身份验证、交易相关等）建议通过 API - SendEmail 接口发送。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_0or1amx": {"rawText": "批量发送内置自动 Warm Up 功能，系统会智能判断发信域名/IP的信誉度等级并分配当日最大发信量。 当实时发信量到达当日最大发信量时，系统会自动暂停发送，剩余未发送的邮件自动进入队列缓存中，并在本次暂停时刻的24小时后自动开始发送。 详细请参见文档<1>产品功能-自动Warm Up</1>章节的说明。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_0rf6ecf": {"rawText": "同一域名可执行多个发送任务，单日内多个任务的总实时发信量也不能超过当日最大发信量。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_13bjqni": {"rawText": "发送任务进入队列缓存时，任务状态显示为暂停、发送进度条保持静止状态；次日自动开始发送后，任务状态和发送进度会自动更新。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_0c7m7kz": {"rawText": "批量发送", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form", "Select"]}}, "k_0c80eop": {"rawText": "定时发送", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form", "Select"]}}, "k_0c83xs3": {"rawText": "频率发送", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form", "Select"]}}, "k_0d1i9c0": {"rawText": "确认删除当前发送任务？", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_1rn94b8": {"rawText": "删除后，将不会执行该发送任务。", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_03fsewb": {"rawText": "待开始", "staticAnalysis": {"componentStack": ["BatchSendPage", "getTaskStatusText"]}}, "k_03jgupk": {"rawText": "发送中", "staticAnalysis": {"componentStack": ["BatchSendPage", "getTaskStatusText"]}}, "k_0ozzbj8": {"rawText": "今日暂停发送", "staticAnalysis": {"componentStack": ["BatchSendPage", "getTaskStatusText"]}}, "k_16aswzz": {"rawText": "发送异常", "staticAnalysis": {"componentStack": ["BatchSendPage", "getTaskStatusText"]}}, "k_13fzaqx": {"rawText": "发送完成", "staticAnalysis": {"componentStack": ["BatchSendPage", "getTaskStatusText"]}}, "k_0zfcqpx": {"rawText": "新建发送任务", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal"]}}, "k_003lxmq": {"rawText": "刷新", "staticAnalysis": {"componentStack": ["DomainPage", "handleAddIp"]}}, "k_15ihz3j": {"rawText": "任务类型", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form"]}}, "k_0yjt1wy": {"rawText": "任务ID", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_03i9uyg": {"rawText": "发件人", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0c9fju5": {"rawText": "收件人列表ID", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0ej1kv1": {"rawText": "收件人列表名称", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0dh4tmm": {"rawText": "邮件主题", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form"]}}, "k_0yjppo6": {"rawText": "模板ID", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_1778lc8": {"rawText": "发送进度", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0goic7q": {"rawText": "任务开始时间", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form"]}}, "k_003nbma": {"rawText": "周期", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_03cxs8z": {"rawText": "一次性", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0crezr5": {"rawText": "每{{count}}天一次", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0fguaoh": {"rawText": "每{{count}}小时一次", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0ck5hnz": {"rawText": "请求数量", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_13w738o": {"rawText": "任务状态", "staticAnalysis": {"componentStack": ["BatchSendPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0yt7gy4": {"rawText": "尚未创建发件邮箱", "staticAnalysis": {"componentStack": ["SendEmailForm", "fetchSenderList"]}}, "k_1dl4v9t": {"rawText": "尚未创建收件人列表", "staticAnalysis": {"componentStack": ["NewModal", "fetchContact"]}}, "k_056igp7": {"rawText": "JSON格式解析错误", "staticAnalysis": {"componentStack": ["SendEmailForm"]}}, "k_000izxt": {"rawText": "JSON格式解析错误：", "staticAnalysis": {"componentStack": ["SendEmailForm"]}}, "k_0gr159i": {"rawText": "长度为1-{{len}}个字符", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-contact-modal", "form", "Form", "Input"]}}, "k_1jrexi4": {"rawText": "请选择...", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form", "Select"]}}, "k_0igtft3": {"rawText": "请输入主题", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form", "Input"]}}, "k_0r0sups": {"rawText": "收件人列表", "staticAnalysis": {"componentStack": ["ContactPage", "Layout", "Body", "Content"]}}, "k_01hc00s": {"rawText": "注：该收件人列表导入状态是“导入完成-部分有效”，<1>去编辑</1>修改或者删除“无效”收件人", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form", "SesFormItem"]}}, "k_133i2d7": {"rawText": "模板选择", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form", "Justify", "Form"]}}, "k_12cnv1e": {"rawText": "变量设置", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form", "SesFormItem"]}}, "k_05vdj5k": {"rawText": "可在此处填写JSON格式的变量设置", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form", "SesFormItem"]}}, "k_003twr0": {"rawText": "张三", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form", "SesFormItem", "Input"]}}, "k_0bsap4o": {"rawText": "发件人选择", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form"]}}, "k_0zvanuo": {"rawText": "退订管理", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form"]}}, "k_0rivkz2": {"rawText": "开启后系统将会在您发送的邮件末尾自动加入退订链接，用户退订后将不再收到同一个发信域名发送的邮件。", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form"]}}, "k_0grmatd": {"rawText": "AD标识", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form"]}}, "k_18yg2oa": {"rawText": "开启后系统将会在您发送的邮件主题中自动加入“<AD>”。建议广告营销类邮件开启此项。", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form"]}}, "k_03f64nn": {"rawText": "不添加", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form", "Radio"]}}, "k_1rbyz4o": {"rawText": "添加到邮件主题前", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form", "Radio"]}}, "k_1d5syez": {"rawText": "添加到邮件主题后", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form", "Radio"]}}, "k_13bssa0": {"rawText": "任务周期", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form"]}}, "k_00043v0": {"rawText": "天", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-batch-send-task-modal", "form", "Form", "InputNumber"]}}, "k_002r305": {"rawText": "发送", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Button.send-submit-btn"]}}, "k_08wa1u9": {"rawText": "无效地址拦截", "staticAnalysis": {"componentStack": ["BlockListPage", "Layout", "Body", "Content", "Tabs"]}}, "k_13ji371": {"rawText": "拒收拦截", "staticAnalysis": {"componentStack": ["BlockListPage", "Layout", "Body", "Content", "Tabs"]}}, "k_0q5abf4": {"rawText": "自定义拦截", "staticAnalysis": {"componentStack": ["BlockListPage", "Layout", "Body", "Content", "Tabs"]}}, "k_003nkp5": {"rawText": "拦截", "staticAnalysis": {"componentStack": ["BlockListPage", "Layout", "Body", "Content"]}}, "k_1629prc": {"rawText": "日期范围", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form.stats-search-query", "Justify", "Form"]}}, "k_0fz89ca": {"rawText": "收件邮箱地址", "staticAnalysis": {"componentStack": ["BlackListSearch<PERSON><PERSON><PERSON>", "form.stats-search-query", "Justify", "Form"]}}, "k_15kabpm": {"rawText": "邮箱地址", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "Card", "Table"]}}, "k_1t1gkut": {"rawText": "请输入邮箱地址搜索", "staticAnalysis": {"componentStack": ["SearchQuery", "form", "Form", "SearchBox"]}}, "k_1yce9ih": {"rawText": "请输入完整的邮箱地址搜索", "staticAnalysis": {"componentStack": ["BlackListSearch<PERSON><PERSON><PERSON>", "form.stats-search-query", "div", "div", "SearchBox"]}}, "k_0gqtwb2": {"rawText": "拦截查询", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "div.flex-one-row", "div", "div", "Tabs"]}}, "k_0a7qxhn": {"rawText": "我的触发拦截", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "div.flex-one-row", "div", "div", "Tabs"]}}, "k_1y67fyv": {"rawText": "请输入精准的邮箱地址", "staticAnalysis": {"componentStack": ["BlackListTable", "statsQuery"]}}, "k_0vsfte0": {"rawText": "默认展示近15天数据，更多数据请调整日期范围后重新搜索", "staticAnalysis": {"componentStack": ["BlackListTable", "statsQuery"]}}, "k_0mg68be": {"rawText": "可查询该邮箱地址是否被平台判定为无效地址", "staticAnalysis": {"componentStack": ["BlackListTable", "statsQuery"]}}, "k_0wozwfj": {"rawText": "查询成功，当前查询条件暂无数据，请修改查询条件", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_0ygt8kv": {"rawText": "暂无查询结果", "staticAnalysis": {"componentStack": ["BlackListTable", "statsQuery"]}}, "k_1h0x0wj": {"rawText": "该邮箱地址不在平台无效地址列表内，请重新搜索", "staticAnalysis": {"componentStack": ["BlackListTable", "statsQuery"]}}, "k_17yj8dh": {"rawText": "请选择时间段", "staticAnalysis": {"componentStack": ["StatsSearchQuery"]}}, "k_15wra7p": {"rawText": "移除成功", "staticAnalysis": {"componentStack": ["EmailStatusTable", "onConfirm"]}}, "k_19snt8q": {"rawText": "加入白名单成功", "staticAnalysis": {"componentStack": ["BlackListTable", "onConfirm"]}}, "k_0s2romp": {"rawText": "加入白名单失败", "staticAnalysis": {"componentStack": ["BlackListTable", "onConfirm"]}}, "k_13i7d7y": {"rawText": "1、平台会对实际不存在、不可用或者长期不活跃的无效地址主动进行拦截，180天后释放。", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "<PERSON><PERSON>", "div"]}}, "k_1xir7nc": {"rawText": "2、若您确认邮箱地址有效，本账号下发信触发的无效地址拦截支持\"移除\"；其他用户账号触发的无效地址拦截只能“加白”。", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "<PERSON><PERSON>", "div"]}}, "k_061zlqt": {"rawText": "注意：无效地址率过高会被认为“盲目群发垃圾邮件”，影响邮件到达率和发信域名信誉度，请谨慎操作。", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "<PERSON><PERSON>", "div"]}}, "k_0cjhbiv": {"rawText": "批量删除", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "Justify", "<PERSON><PERSON>"]}}, "k_0s6qr72": {"rawText": "本账号下发信触发的无效地址拦截支持\"移除\"；其他用户账号触发的无效地址拦截只能“加白”。", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "<PERSON><PERSON>", "div"]}}, "k_0by8t0g": {"rawText": "弹回时间", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "Card", "Table"]}}, "k_003ojje": {"rawText": "备注", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "Table"]}}, "k_002rkey": {"rawText": "移除", "staticAnalysis": {"componentStack": ["UnsubscribeModal", "Modal", "<PERSON><PERSON>"]}}, "k_003kf3s": {"rawText": "加白", "staticAnalysis": {"componentStack": ["BlackListTable", "div", "Card", "Table", "<PERSON><PERSON>"]}}, "k_0b88rbc": {"rawText": "请选择日期", "staticAnalysis": {"componentStack": ["validateDate"]}}, "k_10l6ehq": {"rawText": "收件邮箱地址为必填", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog"]}}, "k_06ug6sy": {"rawText": "第{{errIndex}}个邮箱地址格式错误", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog"]}}, "k_15wgku3": {"rawText": "操作成功", "staticAnalysis": {"componentStack": ["ConfirmDialog"]}}, "k_1jx21kv": {"rawText": "导入中...", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "uploadCustomBlacklistByCos"]}}, "k_1pg4eaj": {"rawText": "新增自定义黑名单", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal"]}}, "k_16pfvsz": {"rawText": "录入方式", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form"]}}, "k_18nbn3t": {"rawText": "批量上传", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form", "Radio"]}}, "k_167im9c": {"rawText": "手动输入", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form", "Radio"]}}, "k_003tnp0": {"rawText": "文件", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form"]}}, "k_0armcjx": {"rawText": "使用回车键换行，一行视为一个收件邮箱地址，最多可输入100个，共输入了<1><0></0></1>个邮箱地址。", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form", "div"]}}, "k_0hf7x3z": {"rawText": "收件邮箱地址示例：<EMAIL>", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form", "div"]}}, "k_0qpvlj5": {"rawText": "原文件：{{TotalCount}}个收件邮箱地址（不包含空行）", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form", "div", "div", "Text"]}}, "k_1dhmv6f": {"rawText": "实际上传：{{ValidCount}}个收件邮箱地址（存在“重复邮箱”、“邮箱地址格式不对”，已剔除）", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form", "div", "div", "Text"]}}, "k_11nii1b": {"rawText": "单次支持上传50万个邮箱，请上传csv格式文件。请根据模板要求填入收件邮箱地址<1>下载标准模板</1>", "staticAnalysis": {"componentStack": ["CreateCustomBlockDialog", "Modal", "Form", "div"]}}, "k_19ru9zk": {"rawText": "是否永久有效", "staticAnalysis": {"componentStack": ["EditCustomBlockDialog", "Modal", "Form"]}}, "k_0003yx6": {"rawText": "是", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "Modal", "FinalForm", "form", "Form", "Field", "RadioGroup", "Radio"]}}, "k_00041oj": {"rawText": "否", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "Modal", "FinalForm", "form", "Form", "Field", "RadioGroup", "Radio"]}}, "k_1330o12": {"rawText": "失效日期", "staticAnalysis": {"componentStack": ["EditCustomBlockDialog", "Modal", "Form"]}}, "k_002r79h": {"rawText": "全部", "staticAnalysis": {"componentStack": ["IPTable", "Table"]}}, "k_03be766": {"rawText": "已失效", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "Table", "Text"]}}, "k_03i8dlr": {"rawText": "生效中", "staticAnalysis": {"componentStack": ["domainStatusOpts"]}}, "k_1xofdc7": {"rawText": "确认删除{{email}}", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "Table", "<PERSON><PERSON>"]}}, "k_0k6jmon": {"rawText": "删除后，该收件邮箱地址将不会被拦截，可接收邮件", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "Table", "<PERSON><PERSON>"]}}, "k_15wrir2": {"rawText": "删除成功", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "onDelete"]}}, "k_1l8lm3v": {"rawText": "自动拦截-收件箱空间满已经开启！", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "switchAutoRejectRule"]}}, "k_0ebfqk5": {"rawText": "自动拦截-收件箱空间满已关闭！", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "switchAutoRejectRule"]}}, "k_1sq8802": {"rawText": "操作失败。", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "switchAutoRejectRule"]}}, "k_0m3achi": {"rawText": "自动拦截规则", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card"]}}, "k_0x4ttiv": {"rawText": "收件箱空间满拦截", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "div", "div"]}}, "k_1oe15rc": {"rawText": "建议开启，长期发送到“满箱邮箱”可能被认为是“骚扰性发送”", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "div", "div", "div"]}}, "k_0fw2d12": {"rawText": "拦截列表", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card"]}}, "k_003s65n": {"rawText": "新增", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "div", "div", "div", "<PERSON><PERSON>"]}}, "k_0irme7v": {"rawText": "<EMAIL>", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "div", "div", "div", "FinalForm", "form", "Form", "Field", "SearchBox"]}}, "k_15iko49": {"rawText": "拦截类型", "staticAnalysis": {"componentStack": ["CustomizeBlockList", "Card", "Table"]}}, "k_003m67m": {"rawText": "修改", "staticAnalysis": {"componentStack": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "Table", "<PERSON><PERSON>"]}}, "k_0jtq3b2": {"rawText": "修改{{email}}", "staticAnalysis": {"componentStack": ["EditCustomBlockDialog", "Modal"]}}, "k_0g3s0ym": {"rawText": "确认添加邮箱地址 {{email}} 至白名单？", "staticAnalysis": {"componentStack": ["OtherAddModal", "Modal"]}}, "k_1erx7jm": {"rawText": "添加至白名单后，平台将恢复向该邮箱发送邮件。请务必确认邮箱有效，避免因无效地址增多导致发信退信率上升，影响投递率和域名信誉。请谨慎操作！", "staticAnalysis": {"componentStack": ["OtherAddModal", "Modal", "Text"]}}, "k_003qdlq": {"rawText": "添加", "staticAnalysis": {"componentStack": ["AddIPDialog", "Modal", "<PERSON><PERSON>"]}}, "k_149jyfo": {"rawText": "收件人拒收或者将发件人拉黑，平台将不会继续往拒收邮箱中发信。如有疑问，请", "staticAnalysis": {"componentStack": ["RejectBlockList", "div.reject-block-list", "<PERSON><PERSON>"]}}, "k_0ti5yya": {"rawText": "联系在线客服", "staticAnalysis": {"componentStack": ["RejectBlockList", "div.reject-block-list", "<PERSON><PERSON>", "ExternalLink"]}}, "k_15t92ap": {"rawText": "提交工单", "staticAnalysis": {"componentStack": ["RejectBlockList", "div.reject-block-list", "<PERSON><PERSON>", "ExternalLink"]}}, "k_0003jjb": {"rawText": "。", "staticAnalysis": {"componentStack": ["RejectBlockList", "div.reject-block-list", "<PERSON><PERSON>"]}}, "k_1udcp0s": {"rawText": "被拒收的发信域名", "staticAnalysis": {"componentStack": ["RejectBlockList", "div.reject-block-list", "Card", "Table"]}}, "k_0bybl43": {"rawText": "拒收时间", "staticAnalysis": {"componentStack": ["RejectBlockList", "div.reject-block-list", "Card", "Table"]}}, "k_1nfdppo": {"rawText": "确认移除邮箱地址 {{email}} ?", "staticAnalysis": {"componentStack": ["SelfDeleteModal"]}}, "k_0o67qgg": {"rawText": "确认移除已选的 {{nums}} 个邮箱地址？", "staticAnalysis": {"componentStack": ["SelfDeleteModal"]}}, "k_1iys74w": {"rawText": "移除后，平台将恢复向该邮箱发送邮件。请务必确认邮箱有效，避免因无效地址增多导致发信退信率上升，影响投递率和域名信誉。请谨慎操作！", "staticAnalysis": {"componentStack": ["SelfDeleteModal", "Modal", "Text"]}}, "k_1414i2p": {"rawText": "导入文件大小超过限制", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_0mm9kvs": {"rawText": "仅支持导入.csv格式的文件，请重试。", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_0k4y0s3": {"rawText": "导入文件无有效邮箱", "staticAnalysis": {"componentStack": ["SingleFileUpload", "beforeUpload"]}}, "k_1vd8lor": {"rawText": "导入的邮箱数量不能超过50万条。", "staticAnalysis": {"componentStack": ["SingleFileUpload", "beforeUpload"]}}, "k_18o0omp": {"rawText": "取消上传", "staticAnalysis": {"componentStack": ["TemplateFileUpload", "Upload", "<PERSON><PERSON>"]}}, "k_18o1b9u": {"rawText": "重新上传", "staticAnalysis": {"componentStack": ["TemplateFileUpload", "Upload", "<PERSON><PERSON>"]}}, "k_18o9o0d": {"rawText": "点击上传", "staticAnalysis": {"componentStack": ["TemplateFileUpload", "Upload", "a"]}}, "k_1ff0y1c": {"rawText": "/拖拽到此区域", "staticAnalysis": {"componentStack": ["SingleFileUpload", "Upload", "Text"]}}, "k_03ia4nz": {"rawText": "收件人", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form", "SesFormItem"]}}, "k_03fkcas": {"rawText": "请输入", "staticAnalysis": {"componentStack": ["fieldList"]}}, "k_0vi0a5d": {"rawText": "收件人邮箱地址不能为空！", "staticAnalysis": {"componentStack": ["fieldList"]}}, "k_0yzf3p4": {"rawText": "邮箱地址格式输入错误！", "staticAnalysis": {"componentStack": ["fieldList"]}}, "k_1tn4vw6": {"rawText": "变量不能为空，请输入json格式的变量！", "staticAnalysis": {"componentStack": ["fieldList"]}}, "k_0cnwdpu": {"rawText": "变量长度不能超过1000个字符，请重新输入！", "staticAnalysis": {"componentStack": ["fieldList"]}}, "k_01z222r": {"rawText": "JSON格式错误，请按照如上示例格式进行填写！", "staticAnalysis": {"componentStack": ["fieldList"]}}, "k_002rkhe": {"rawText": "变量", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "ProTable"]}}, "k_1o4jwea": {"rawText": "需在此处填写JSON格式的变量{\"变量名1\":\"变量值\",\"变量名2\":\"变量值\",……}", "staticAnalysis": {"componentStack": ["AddImportModal", "Modal", "DynamicForm", "p"]}}, "k_0esaq13": {"rawText": "示例：{\"Username\":\"张三\",\"City\":\"北京\"}", "staticAnalysis": {"componentStack": ["AddImportModal", "Modal", "DynamicForm", "p"]}}, "k_0ye5gww": {"rawText": "{\"请输入变量名1\":\"请输入变量值\", \"请输入变量名2\":\"请输入变量值\"， ……}", "staticAnalysis": {"componentStack": ["fieldList"]}}, "k_1jd15tw": {"rawText": "添加收件人", "staticAnalysis": {"componentStack": ["AddImportModal", "Modal"]}}, "k_0j2buvy": {"rawText": "修改{{<PERSON><PERSON>}}", "staticAnalysis": {"componentStack": ["EditImportModal", "Modal"]}}, "k_1h1y0l2": {"rawText": "仅支持中文字符、大小写字母（a-z、A-Z）、数字（0-9）和下划线", "staticAnalysis": {"componentStack": ["NewModal"]}}, "k_0kgls7e": {"rawText": "新建收件人列表", "staticAnalysis": {"componentStack": ["ContactPage", "Layout", "Body", "Content", "Justify", "<PERSON><PERSON>"]}}, "k_11l8yh3": {"rawText": "列表名称", "staticAnalysis": {"componentStack": ["ContactPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0yf0hf3": {"rawText": "不能重复，长度为1-200个字符", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-contact-modal", "form", "Form", "Input"]}}, "k_002vt4q": {"rawText": "描述", "staticAnalysis": {"componentStack": ["ContactPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0ndboqh": {"rawText": "导入文件存在乱码，请确定使用的是UTF-8编码。", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_1w6ovdn": {"rawText": "请删除“重要说明”的全部内容。", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_1xf91vv": {"rawText": "“收件人列表”为空，请检查后重试。", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_10a4phq": {"rawText": "导入的收件人数量不能超过50万条。", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_1pnfclb": {"rawText": "“收件人列表”中的变量值存在空值，请检查后重试。", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_1oenozu": {"rawText": "“收件人列表”中的变量名填写不正确，请检查后重试。", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_0ux636k": {"rawText": "“收件人列表”中存在相同的变量名，请检查后重试。", "staticAnalysis": {"componentStack": ["beforeUploadCheck"]}}, "k_0737skn": {"rawText": "上传文件要求：", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_1wp19un": {"rawText": "支持 CSV 格式文件 (<1>点击下载模板文件</1>)；每个收件人列表最多支持上传50万个收件人地址。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_0g99xvs": {"rawText": "当文件中存在“重复收件人”、“收件人为空”、“整个变量json长度超过1000字符”，上传时会直接剔除。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_1qb2isu": {"rawText": "每个收件人列表最多支持上传50万个收件人地址，大小不超过100Mb。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_1iypwwr": {"rawText": "当收件人状态为“无效”时，<1>请修改或者删除“无效”收件人。</1>", "staticAnalysis": {"componentStack": ["reminderList"]}}, "k_1k6flox": {"rawText": "（存在“重复收件人”、“收件人为空”、“变量数据超过1000字符”，已直接剔除）", "staticAnalysis": {"componentStack": ["getActualMsg"]}}, "k_1ikwphs": {"rawText": "（存在“重复收件人”，已直接剔除）", "staticAnalysis": {"componentStack": ["getActualMsg"]}}, "k_1ja9fm5": {"rawText": "原文件：<1>{{totalCount}}个</1>收件人（不包含空行）<3></3>实际上传：<5>{{validCount}}个</5>收件人<7>{{actualMsg}}</7>", "staticAnalysis": {"componentStack": ["showModal", "modal"]}}, "k_003n1ik": {"rawText": "所有", "staticAnalysis": {"componentStack": ["StatsSearchQuery"]}}, "k_003lzes": {"rawText": "有效", "staticAnalysis": {"componentStack": ["statusOptions"]}}, "k_003lzsd": {"rawText": "无效", "staticAnalysis": {"componentStack": ["statusOptions"]}}, "k_16d1saj": {"rawText": "请确认批量删除操作", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "Justify", "<PERSON><PERSON>", "yes"]}}, "k_0jy58bx": {"rawText": "请确认删除操作", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "Justify", "<PERSON><PERSON>", "yes"]}}, "k_1kdb0kv": {"rawText": "删除后，这些收件人会从收件人列表移除。确定删除吗？", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "Justify", "<PERSON><PERSON>", "yes"]}}, "k_101kwfv": {"rawText": "删除后，{{Email}}会从收件人列表移除。确定删除吗？", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "Justify", "<PERSON><PERSON>", "yes"]}}, "k_0xf9ztc": {"rawText": "导入中，请勿离开当前界面...", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "div", "Upload"]}}, "k_13wa1rs": {"rawText": "上传失败，请重试", "staticAnalysis": {"componentStack": ["ContactImport", "handleUpload"]}}, "k_15wdq5o": {"rawText": "导出成功", "staticAnalysis": {"componentStack": ["ContactImport", "downloadDetail"]}}, "k_14rb87s": {"rawText": "收件人编辑", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content"]}}, "k_0i8u1uy": {"rawText": "注意事项：", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "<PERSON><PERSON>", "H5"]}}, "k_003pnp8": {"rawText": "导入", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "div", "Upload", "<PERSON><PERSON>"]}}, "k_003polf": {"rawText": "导出", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content", "div", "<PERSON><PERSON>"]}}, "k_002wh4y": {"rawText": "查询", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form.stats-search-query", "Justify", "div.right-search-btn-field", "<PERSON><PERSON>"]}}, "k_0mfjq2c": {"rawText": "总：<1>{{TotalCount}} </1>个，有效：<3>{{ValidCount}} </3>个，无效：<5>{{InvalidCount}} </5>个收件人", "staticAnalysis": {"componentStack": ["ContactImport", "Layout", "Body", "Content"]}}, "k_03ej395": {"rawText": "待导入", "staticAnalysis": {"componentStack": ["searchFields"]}}, "k_03ibj2i": {"rawText": "处理中", "staticAnalysis": {"componentStack": ["searchFields"]}}, "k_18dm8wc": {"rawText": "导入完成-全部有效", "staticAnalysis": {"componentStack": ["searchFields"]}}, "k_11gjjmq": {"rawText": "导入完成-部分有效", "staticAnalysis": {"componentStack": ["searchFields"]}}, "k_0o6s2aw": {"rawText": "有{{count}}个“无效”收件人，请点击编辑“修改”或者“删除”", "staticAnalysis": {"componentStack": ["importStatusOptions"]}}, "k_14nl8n7": {"rawText": "列表名称和描述将用来调用列表，因此不可重复。", "staticAnalysis": {"componentStack": ["introductionList"]}}, "k_13w7mgb": {"rawText": "导入状态", "staticAnalysis": {"componentStack": ["ContactPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0mxnvn3": {"rawText": "确认删除当前收件人列表？", "staticAnalysis": {"componentStack": ["ContactPage", "tableColumns", "<PERSON><PERSON>", "yes"]}}, "k_13wlv6k": {"rawText": "删除后，不能再用该收件人列表发送邮件。", "staticAnalysis": {"componentStack": ["ContactPage", "tableColumns", "<PERSON><PERSON>", "yes"]}}, "k_0yivubb": {"rawText": "列表ID", "staticAnalysis": {"componentStack": ["ContactPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_003lzri": {"rawText": "总数", "staticAnalysis": {"componentStack": ["ContactPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_1boy8af": {"rawText": "指的是导入有效的收件人个数。", "staticAnalysis": {"componentStack": ["ContactPage", "Layout", "Body", "Content", "Card", "Table", "Bubble"]}}, "k_002vxya": {"rawText": "编辑", "staticAnalysis": {"componentStack": ["DomainSettingTable", "Card", "Table", "<PERSON><PERSON>"]}}, "k_19bsc4b": {"rawText": "120美元", "staticAnalysis": {"componentStack": ["price"]}}, "k_15p1s0v": {"rawText": "900元", "staticAnalysis": {"componentStack": ["price"]}}, "k_15gdrvi": {"rawText": "明细账单", "staticAnalysis": {"componentStack": ["StopServiceDialog", "BillLink", "<PERSON><PERSON>"]}}, "k_11y596c": {"rawText": "收支明细", "staticAnalysis": {"componentStack": ["StopServiceDialog", "BillLink", "<PERSON><PERSON>"]}}, "k_13h5h41": {"rawText": "【独立IP增值服务】开通成功", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "handlerSubmit"]}}, "k_1dxy4ml": {"rawText": "已成功开通【独立IP增值服务】，并冻结费用{{price}}，点击控制台>费用中心><3></3>查看详情。", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "alertSuccess"]}}, "k_03j88xh": {"rawText": "知道了", "staticAnalysis": {"componentStack": ["TemplatePage", "onCreateSubmit"]}}, "k_0sl30z4": {"rawText": "当前账户余额不足", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "handlerSubmit"]}}, "k_0q3xhma": {"rawText": "开通【独立IP增值服务】预计冻结费用{{price}}。当前您的账户余额不足，还请先进行充值。（代金券无法抵扣冻结费用）", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "handlerSubmit"]}}, "k_1po02h2": {"rawText": "充值冻结指引", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "handlerSubmit", "ExternalLink"]}}, "k_161gnjl": {"rawText": "前往充值", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "handlerSubmit", "<PERSON><PERSON>"]}}, "k_1x84ne4": {"rawText": "确认开通【独立IP增值服务】吗？", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "Modal"]}}, "k_1tljcg6": {"rawText": "为保证资源被合理使用，要开通【独立IP增值服务】，平台预计冻结费用{{price}}，实际冻结金额以账单为准。", "staticAnalysis": {"componentStack": ["OpenConfirmDialog", "Modal", "Text"]}}, "k_1wy208t": {"rawText": "获取域名列表失败，请稍后重试", "staticAnalysis": {"componentStack": ["ApplyIPDialog"]}}, "k_1njhumr": {"rawText": "添加发信域名成功", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleAddDomainConfirm"]}}, "k_0za4i8a": {"rawText": "添加发信域名失败，请稍后重试。", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleAddDomainConfirm"]}}, "k_0tdukmp": {"rawText": "暂无可添加发信域名", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "Modal", "Table", "SelectMultiple"]}}, "k_180nsos": {"rawText": "添加发信域名", "staticAnalysis": {"componentStack": ["IPTable", "Table", "<PERSON><PERSON>"]}}, "k_0v8o8jm": {"rawText": "找不到发信域名？<1>刷新</1>或<3>新建发信域名</3>", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "Modal", "Table", "SelectMultiple", "div"]}}, "k_0w77lrk": {"rawText": "请选择发信域名", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "Modal", "Table", "SelectMultiple"]}}, "k_1g2mytm": {"rawText": "确认申请独立IP？", "staticAnalysis": {"componentStack": ["ApplyConfirmDialog", "Modal"]}}, "k_1cocynt": {"rawText": "独立IP一旦申请就开始计费，当月不支持停用。", "staticAnalysis": {"componentStack": ["ApplyConfirmDialog", "Modal", "<PERSON><PERSON>"]}}, "k_1h0rkzo": {"rawText": "独立IP数量", "staticAnalysis": {"componentStack": ["ApplyConfirmDialog", "Modal", "div", "div", "Text", "span"]}}, "k_00046tr": {"rawText": "个", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "Dropdown", "Text"]}}, "k_18uv0lw": {"rawText": "开始计费时间", "staticAnalysis": {"componentStack": ["IPTable", "Table"]}}, "k_13u5j9i": {"rawText": "我已阅读并同意<1>《计费规则》</1>", "staticAnalysis": {"componentStack": ["ApplyConfirmDialog", "Modal", "Checkbox"]}}, "k_0gws9kb": {"rawText": "确认申请", "staticAnalysis": {"componentStack": ["ApplyConfirmDialog", "Modal", "<PERSON><PERSON>"]}}, "k_07k2v1h": {"rawText": "独立IP{{index}}", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "handleDeleteIP"]}}, "k_12d8i0r": {"rawText": "最多只能申请{{count}}个独立IP", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "handleAddIP"]}}, "k_1rsfidt": {"rawText": "请为每个独立IP选择发信域名", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "Modal", "Bubble"]}}, "k_14jg966": {"rawText": "独立IP已下发并申请成功", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "handleConfirm"]}}, "k_1l3buq3": {"rawText": "独立IP申请失败", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "handleConfirm"]}}, "k_1x5dqdb": {"rawText": "申请独立IP", "staticAnalysis": {"componentStack": ["OperationBar", "Justify", "<PERSON><PERSON>"]}}, "k_1g0eiio": {"rawText": "独立IP一旦配置就开始计费，每月3日前会根据上月您账号下配置启用的独立IP个数<1>自动扣除上月费用</1>，详见<3>计费概述</3>。", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "Modal", "<PERSON><PERSON>", "List"]}}, "k_11qfqwz": {"rawText": "独立IP是研发团队提前预热准备好的资源。为保证资源能被合理有效使用，<1>每月最多支持申请3个独立 IP</1>。了解<3>申请建议</3>。", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "Modal", "<PERSON><PERSON>", "List"]}}, "k_15e5z5s": {"rawText": "收信域名", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form.stats-search-query", "Justify", "Form"]}}, "k_0e5eivz": {"rawText": "每月最多支持申请{{MAX_IP_COUNT}}个独立IP，如有疑问请<3>联系腾讯云在线客服</3>", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "Modal", "Bubble.app-ses-alert"]}}, "k_1x5rjuo": {"rawText": "添加独立IP", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "Bubble", "<PERSON><PERSON>"]}}, "k_002wmu9": {"rawText": "申请", "staticAnalysis": {"componentStack": ["ApplyIPDialog", "Modal", "Bubble", "<PERSON><PERSON>"]}}, "k_0zo804b": {"rawText": "删除后，该配置将无法恢复。", "staticAnalysis": {"componentStack": ["DeleteDomainDialog", "renderContent", "Text"]}}, "k_01frutw": {"rawText": "1.删除后独立IP{{ip}}将没有任何发信域名配置。", "staticAnalysis": {"componentStack": ["DeleteDomainDialog", "renderContent", "Text"]}}, "k_0p4tuwv": {"rawText": "2.若还需要独立IP继续帮助您发信提高到达率，请及时为其添加发信域名。", "staticAnalysis": {"componentStack": ["StopDomainDialog", "renderContent", "Text"]}}, "k_1o0af49": {"rawText": "3.若您不想继续使用独立IP{{ip}}服务，请及时停用独立IP，避免持续产生费用。", "staticAnalysis": {"componentStack": ["StopDomainDialog", "renderContent", "Text"]}}, "k_16ang1t": {"rawText": "确认删除发信域名{{domain}}吗？", "staticAnalysis": {"componentStack": ["DeleteDomainDialog", "Modal"]}}, "k_0cjfhqj": {"rawText": "确认删除", "staticAnalysis": {"componentStack": ["DeleteDomainDialog", "Modal", "<PERSON><PERSON>"]}}, "k_11mzt9a": {"rawText": "状态为\"使用中\"的独立IP每月3日前会自动扣除上月费用。若您不再继续使用，请及时停用独立IP，避免持续产生费用。", "staticAnalysis": {"componentStack": ["InfoAlert", "<PERSON><PERSON>", "List"]}}, "k_11zxz9r": {"rawText": "停用所有独立IP且以后不再继续使用请点击\"关闭服务\"；停用单个独立IP请点击操作列\"停用\"按钮。", "staticAnalysis": {"componentStack": ["InfoAlert", "<PERSON><PERSON>", "List", "Text"]}}, "k_1nj5ddi": {"rawText": "关闭发信域名成功", "staticAnalysis": {"componentStack": ["IPTable", "Table", "Table", "<PERSON><PERSON>"]}}, "k_1njgn47": {"rawText": "开启发信域名成功", "staticAnalysis": {"componentStack": ["IPTable", "Table", "Table", "<PERSON><PERSON>"]}}, "k_0z95hrj": {"rawText": "关闭发信域名失败，请稍后重试。", "staticAnalysis": {"componentStack": ["IPTable", "Table", "Table", "<PERSON><PERSON>"]}}, "k_0za3sv2": {"rawText": "开启发信域名失败，请稍后重试。", "staticAnalysis": {"componentStack": ["IPTable", "Table", "Table", "<PERSON><PERSON>"]}}, "k_1nj4qto": {"rawText": "删除发信域名成功", "staticAnalysis": {"componentStack": ["IPTable", "handleDeleteDomain"]}}, "k_0z94o9h": {"rawText": "删除发信域名失败，请稍后重试。", "staticAnalysis": {"componentStack": ["IPTable", "handleDeleteDomain"]}}, "k_0bye8ab": {"rawText": "停用时间", "staticAnalysis": {"componentStack": ["IPTable", "Table"]}}, "k_1dagy9p": {"rawText": "当月新增的独立IP不支持停用", "staticAnalysis": {"componentStack": ["IPTable", "Table", "Bubble"]}}, "k_003j4vl": {"rawText": "停用", "staticAnalysis": {"componentStack": ["IPTable", "Table", "Bubble", "<PERSON><PERSON>"]}}, "k_0bybdc3": {"rawText": "更新时间", "staticAnalysis": {"componentStack": ["IPTable", "Table", "Table"]}}, "k_003pvbe": {"rawText": "开启", "staticAnalysis": {"componentStack": ["IPTable", "Table", "Table", "<PERSON><PERSON>"]}}, "k_03iawv3": {"rawText": "使用中", "staticAnalysis": {"componentStack": ["dedicatedIPStatusOpts"]}}, "k_038j3oj": {"rawText": "已停用", "staticAnalysis": {"componentStack": ["dedicatedIPStatusOpts"]}}, "k_0d458ip": {"rawText": "当前包含当月新增的独立IP，因此不支持关闭服务。", "staticAnalysis": {"componentStack": ["OperationBar", "Justify", "Bubble"]}}, "k_15thjg7": {"rawText": "关闭服务", "staticAnalysis": {"componentStack": ["OperationBar", "Justify", "Bubble", "<PERSON><PERSON>"]}}, "k_0glj5qy": {"rawText": "成功停用【独立IP增值服务】且次月1日完成扣费后，才能进行“解冻资金”操作。", "staticAnalysis": {"componentStack": ["OperationBar", "Justify", "Bubble"]}}, "k_0bw1rbc": {"rawText": "解冻资金", "staticAnalysis": {"componentStack": ["OperationBar", "Justify", "Bubble", "<PERSON><PERSON>"]}}, "k_1k29xaf": {"rawText": "请输入独立IP、发信域名搜索", "staticAnalysis": {"componentStack": ["OperationBar", "Justify", "SearchBox"]}}, "k_1m82lsq": {"rawText": "1.关闭后独立IP{{ip}}将没有任何生效发信域名。", "staticAnalysis": {"componentStack": ["StopDomainDialog", "renderContent", "Text"]}}, "k_0w2tyt8": {"rawText": "关闭后，后续可再次启用。", "staticAnalysis": {"componentStack": ["StopDomainDialog", "renderContent", "Text"]}}, "k_0gt7qpi": {"rawText": "关闭后，该发信域名绑定的独立IP将清零，系统将默认采用共享IP发信。", "staticAnalysis": {"componentStack": ["StopDomainDialog", "renderContent", "Text"]}}, "k_16katx7": {"rawText": "确认关闭发信域名{{domain}}吗？", "staticAnalysis": {"componentStack": ["StopDomainDialog", "Modal"]}}, "k_0c5scq9": {"rawText": "确认关闭", "staticAnalysis": {"componentStack": ["StopServiceDialog", "Modal", "<PERSON><PERSON>"]}}, "k_0xacaac": {"rawText": "确认停用独立IP{{ip}}吗？", "staticAnalysis": {"componentStack": ["StopIPDialog", "Modal"]}}, "k_09uutmb": {"rawText": "1.停用后，独立IP{{ip}}相关的所有发信域名配置将被删除，平台将会采用共享IP发信，效果可能会受影响。", "staticAnalysis": {"componentStack": ["StopIPDialog", "Modal", "Text"]}}, "k_0iy7jhx": {"rawText": "2.停用当月，仍会按照实际使用天数折算停用当月费用并于次月1日体现在账单中。", "staticAnalysis": {"componentStack": ["StopIPDialog", "Modal", "Text"]}}, "k_0zs56t7": {"rawText": "确认停用", "staticAnalysis": {"componentStack": ["StopIPDialog", "Modal", "<PERSON><PERSON>"]}}, "k_0wg1jp4": {"rawText": "停用所有使用中的独立IP", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "Stepper"]}}, "k_0p7ecrc": {"rawText": "1.停用IP增值服务前需先停用使用中的独立IP：{{ips}}", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "Stepper", "Text"]}}, "k_1nkrqi2": {"rawText": "2.停用后，独立IP相关的所有发信域名将被删除，平台将会采用共享IP发信，效果可能会受影响。", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "Stepper", "Text"]}}, "k_0iy7jhw": {"rawText": "3.停用当月，仍会按照实际使用天数折算停用当月费用并于次月1日体现在账单中。", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "Stepper", "Text"]}}, "k_1ega5u3": {"rawText": "关闭独立IP增值服务", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "Stepper"]}}, "k_1lvt8sj": {"rawText": "1.关闭服务后，再次使用时需要重新开通。", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "Text"]}}, "k_0p2bnoz": {"rawText": "2.关闭服务后，待次月3日前扣完当月独立IP使用费用，开通时预冻结的资金会自动返还账户，到时可点击控制台>费用中心><1></1>查看详情。", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "steps", "Text"]}}, "k_1ykndoj": {"rawText": "关闭独立IP增值服务将同时停用所有正在使用中的独立IP。", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "<PERSON><PERSON>", "Text"]}}, "k_0lyku9m": {"rawText": "2.关闭服务后，待次月3日前扣完当月费用，开通时预冻结的资金会自动返还账户，到时可点击控制台>费用中心><1></1>查看详情。", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "Text"]}}, "k_0tr8izd": {"rawText": "2.关闭服务后，开通时预冻结的资金会自动返还账户，请稍后点击控制台>费用中心><1></1>查看详情。", "staticAnalysis": {"componentStack": ["StopServiceDialog", "renderContent", "Text"]}}, "k_1wlf9mw": {"rawText": "确认关闭【独立IP增值服务】吗？", "staticAnalysis": {"componentStack": ["StopServiceDialog", "Modal"]}}, "k_1mcume1": {"rawText": "我已阅读并知晓以上信息", "staticAnalysis": {"componentStack": ["StopServiceDialog", "Modal", "div", "Checkbox", "Text"]}}, "k_02iq161": {"rawText": "已关闭", "staticAnalysis": {"componentStack": ["switchOptions"]}}, "k_0tcqidc": {"rawText": "停用独立IP成功", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleStopIP"]}}, "k_0hquty1": {"rawText": "停用独立IP失败，请稍后重试。", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleStopIP"]}}, "k_1l4onl0": {"rawText": "关闭独立IP增值服务成功", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleStopService"]}}, "k_0hen4nz": {"rawText": "关闭独立IP增值服务失败，请稍后重试", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleStopService"]}}, "k_0q6iu90": {"rawText": "抱歉，当前独立IP库存不足", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleApplyIPDialogOpen"]}}, "k_1ojhv7d": {"rawText": "系统已经接收到您的诉求且已开始预热准备，请耐心等待，2-4周后再进行添加", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleApplyIPDialogOpen"]}}, "k_12kunhn": {"rawText": "申请独立IP失败，请稍后重试", "staticAnalysis": {"componentStack": ["DedicatedIPManage"]}}, "k_1tdyiuz": {"rawText": "如未解冻成功，请确保已完成次月1日扣费", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleUnfrozenFunds"]}}, "k_0qyfwtl": {"rawText": "解冻失败，请稍后重试", "staticAnalysis": {"componentStack": ["DedicatedIPManage", "handleUnfrozenFunds"]}}, "k_1t0ektx": {"rawText": "独立IP增值服务", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content", "Blank"]}}, "k_0c6jivb": {"rawText": "立即开通", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content", "Blank", "<PERSON><PERSON>"]}}, "k_1rir0ao": {"rawText": "不受其他用户发信干扰，可以保证发信域名和IP的信誉度，提高邮件到达率", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content", "Blank", "Text"]}}, "k_0l9qc0l": {"rawText": "独立IP是基于发信域名维度配置的，请先完成<1><0><0></0></0></1>。", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content", "Blank", "<PERSON><PERSON>", "List"]}}, "k_11lfais": {"rawText": "发信域名配置", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret"]}}, "k_1sx77qi": {"rawText": "独立IP采用月结后付费方式，每月3日前会自动扣除上月费用。若您不再继续使用，请及时停用独立IP或者关闭服务。独立IP添加当月不允许停用。", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content", "Blank", "<PERSON><PERSON>", "List"]}}, "k_05wg7vc": {"rawText": "为保证资源能被合理使用，开通需预先冻结费用<1></1>，了解<3><0>冻结说明</0></3>。请保证余额充足，前往<5><0><0></0></0></5>。", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content", "Blank", "<PERSON><PERSON>", "List"]}}, "k_003qjuk": {"rawText": "充值", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content", "Blank", "<PERSON><PERSON>", "List", "<PERSON><PERSON>", "Text", "Slot"]}}, "k_1txqw4q": {"rawText": "我已阅读并同意<1>《腾讯云邮件推送服务条款》</1><2>《计费规则》</2>", "staticAnalysis": {"componentStack": ["DedicatedIPOpen", "Layout", "Body", "Content", "Blank", "div", "Checkbox"]}}, "k_09og6dy": {"rawText": "获取IP列表失败，请稍后重试", "staticAnalysis": {"componentStack": ["AddIPDialog"]}}, "k_0tcjlin": {"rawText": "添加独立IP成功", "staticAnalysis": {"componentStack": ["DomainPage", "handleAddIPConfirm"]}}, "k_0hr0ily": {"rawText": "添加独立IP失败，请稍后重试。", "staticAnalysis": {"componentStack": ["DomainPage", "handleAddIPConfirm"]}}, "k_1116hfx": {"rawText": "暂无可添加独立IP", "staticAnalysis": {"componentStack": ["AddIPDialog", "Modal", "div", "SelectMultiple"]}}, "k_1x08p4z": {"rawText": "找不到独立IP？<1>刷新</1>或<3>申请独立IP<1></1></3>", "staticAnalysis": {"componentStack": ["AddIPDialog", "Modal", "div", "SelectMultiple", "div"]}}, "k_09c9c98": {"rawText": "请选择独立IP", "staticAnalysis": {"componentStack": ["AddIPDialog", "Modal", "div", "SelectMultiple"]}}, "k_0g9rf92": {"rawText": "请输入域名", "staticAnalysis": {"componentStack": ["get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "k_1mvtsae": {"rawText": "域名太短了", "staticAnalysis": {"componentStack": ["get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "k_061azvv": {"rawText": "名称不符合格式要求", "staticAnalysis": {"componentStack": ["get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "k_180n6p9": {"rawText": "新建发信域名", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Justify", "<PERSON><PERSON>"]}}, "k_003psvr": {"rawText": "域名", "staticAnalysis": {"componentStack": ["NewDomainModal", "Modal.edit-domain-modal", "form", "Form", "SesFormItem"]}}, "k_19j1ta6": {"rawText": "不可使用企业邮箱域名，以免产生SPF、MX记录的冲突，可以在已有企业邮箱域名的情况下创建并使用二级域名。", "staticAnalysis": {"componentStack": ["NewDomainModal", "Modal.edit-domain-modal", "form", "Form", "SesFormItem"]}}, "k_02o0jjh": {"rawText": "待验证", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "span"]}}, "k_02o0j2y": {"rawText": "已验证", "staticAnalysis": {"componentStack": ["VerifyModal", "handleSubmit", "attrs"]}}, "k_0f8cwpo": {"rawText": "验证失败", "staticAnalysis": {"componentStack": ["VerifyModal", "handleSubmit", "attrs"]}}, "k_053u9rz": {"rawText": "验证未通过", "staticAnalysis": {"componentStack": ["VerifyModal", "handleSubmit"]}}, "k_150zc86": {"rawText": "帮助文档", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "span", "a"]}}, "k_168ia83": {"rawText": "如果域名本身提供了邮件服务(已存在MX记录)，无需配置“mxbiz1.qq.com”，详情请参照“帮助文档”。", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "div.domain-info-cards"]}}, "k_0tekbse": {"rawText": "该记录值末尾需要包含“.”。", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "div.domain-info-cards"]}}, "k_1obley8": {"rawText": "{{type}}验证", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "div.domain-info-cards", "Card", "p"]}}, "k_002sqcb": {"rawText": "必须", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "div.domain-info-cards", "Card", "p"]}}, "k_002r2rn": {"rawText": "可选", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "div.domain-info-cards", "Card", "p"]}}, "k_003py1h": {"rawText": "类型", "staticAnalysis": {"componentStack": ["StatusTable", "div", "Card", "BasicLine"]}}, "k_176lfup": {"rawText": "主机记录", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "div.domain-info-cards", "Card", "Table"]}}, "k_03fb658": {"rawText": "记录值", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "div.domain-info-cards", "Card", "Table"]}}, "k_03fcsh3": {"rawText": "当前值", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "div.domain-info-cards", "Card", "Table"]}}, "k_0gzgqsc": {"rawText": "提交验证", "staticAnalysis": {"componentStack": ["VerifyModal", "Modal.verify-domain-modal", "<PERSON><PERSON>"]}}, "k_1q8si1v": {"rawText": "发信域名配置完成并验证通过后，请一直保持MX、SPF、DKIM、DMARC的正确配置，否则会导致发信异常。", "staticAnalysis": {"componentStack": ["getIntroductionList"]}}, "k_04qtcle": {"rawText": "不可使用企业邮箱域名，以免产生配置信息的冲突。", "staticAnalysis": {"componentStack": ["getIntroductionList"]}}, "k_0mz86q4": {"rawText": "域名配置后，可能需要5分钟-2小时的时间同步，如果不能立即验证通过，请耐心等待。", "staticAnalysis": {"componentStack": ["getIntroductionList"]}}, "k_0ak9hks": {"rawText": "每个腾讯云账户可配置最多10个域名。", "staticAnalysis": {"componentStack": ["getIntroductionList"]}}, "k_1v7ak6e": {"rawText": "如果您的域名托管在腾讯云，请进入<1>DNSPod控制台</1>配置域名验证信息；如果您的域名托管在其它域名服务商，请按照域名配置详情信息自行配置。", "staticAnalysis": {"componentStack": ["getIntroductionList", "cnExtraPos2"]}}, "k_1bondbq": {"rawText": "请在您的域名托管服务商提供的配置界面中，按照域名配置详情信息配置域名验证信息。", "staticAnalysis": {"componentStack": ["getIntroductionList"]}}, "k_0i8nsi9": {"rawText": "确认删除当前所选域名？", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_0m02zrf": {"rawText": "删除后，不能再用该域名发送邮件。", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_1tb02j0": {"rawText": "您未开通【独立IP增值服务】", "staticAnalysis": {"componentStack": ["DomainPage", "handleAddIp"]}}, "k_0au6k9k": {"rawText": "暂不可添加独立IP，请先开通【独立IP增值服务】", "staticAnalysis": {"componentStack": ["DomainPage", "handleAddIp"]}}, "k_0c6jvde": {"rawText": "前往开通", "staticAnalysis": {"componentStack": ["DomainPage", "handleAddIp"]}}, "k_003l8z3": {"rawText": "提示", "staticAnalysis": {"componentStack": ["ConfirmDialog"]}}, "k_1soc5qp": {"rawText": "如已开通【独立IP增值服务】，请您刷新页面后再尝试添加独立IP。", "staticAnalysis": {"componentStack": ["DomainPage", "handleAddIp"]}}, "k_0bhs7u7": {"rawText": "获取账户信息失败，请稍后重试", "staticAnalysis": {"componentStack": ["DomainPage", "handleAddIp"]}}, "k_0mvti54": {"rawText": "请输入发信域名搜索", "staticAnalysis": {"componentStack": ["SearchQuery", "form", "Form", "SearchBox"]}}, "k_0nbv13s": {"rawText": "域名状态，验证通过才可以通过此域名发送邮件", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "Bubble"]}}, "k_0f7ufx1": {"rawText": "验证通过", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "span"]}}, "k_11y1ewz": {"rawText": "信誉等级", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_1xqkn8y": {"rawText": "单日最高发信量", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "p", "span"]}}, "k_15i847j": {"rawText": "提升规则", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "p", "Bubble.app-ses-alert", "ExternalLink"]}}, "k_0ykdkek": {"rawText": "发信IP", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_0yke4yu": {"rawText": "共享IP", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "Text"]}}, "k_002wqjs": {"rawText": "验证", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>"]}}, "k_003mow6": {"rawText": "详情", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>"]}}, "k_0w4n1i6": {"rawText": "验证通过的发信域名才能配置独立IP", "staticAnalysis": {"componentStack": ["DomainPage", "Layout", "Body", "Content", "Card", "Table", "Bubble"]}}, "k_0jz7u56": {"rawText": "下载中，请耐心等待", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable", "<PERSON><PERSON>"]}}, "k_18e75yn": {"rawText": "邮件发送时间", "staticAnalysis": {"componentStack": ["SearchQuery", "form", "Justify", "Form"]}}, "k_002vqvn": {"rawText": "下载", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable", "<PERSON><PERSON>"]}}, "k_09ul1wh": {"rawText": "下载内容使用CSV格式，使用Excel等工具查看时，可能会存在数字以科学记数等形式显示。", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "<PERSON><PERSON>", "p"]}}, "k_1appu53": {"rawText": "下载文件中时间使用毫秒时间戳的形式，可以通过Excel的公式转为可读时间。例：=TEXT(C2/86400000+DATE(1970,1,1), \"YYYY-MM-DD HH:mm:ss\") 其中C2为时间戳所在的单元格，输出的时间为UTC时间。", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "<PERSON><PERSON>", "p"]}}, "k_132sw7j": {"rawText": "发信日期", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_003lxlg": {"rawText": "次数", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_0bjr4fa": {"rawText": "注：收件邮箱地址区分大小写，请填入正确的收件邮箱地址", "staticAnalysis": {"componentStack": ["SearchQuery", "form.stats-search-query", "Justify", "Form", "Text"]}}, "k_1ke0v8r": {"rawText": "<EMAIL>", "staticAnalysis": {"componentStack": ["BlackListSearch<PERSON><PERSON><PERSON>", "form.stats-search-query", "Justify", "Form", "SearchBox.email-address-search-box"]}}, "k_003tw6w": {"rawText": "事件", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_13no498": {"rawText": "递送说明", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_13w34zu": {"rawText": "邮件状态", "staticAnalysis": {"componentStack": ["EmailStatusPage", "Layout", "Body", "Content"]}}, "k_1u86ylm": {"rawText": "被退订的发信域名", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_0f8drqm": {"rawText": "移除失败", "staticAnalysis": {"componentStack": ["EmailStatusTable", "onConfirm"]}}, "k_0bz6p9h": {"rawText": "退订时间", "staticAnalysis": {"componentStack": ["EmailStatusTable", "div.black-list-table", "Card", "Table"]}}, "k_06ptx4c": {"rawText": "确认移除收件邮箱地址 {{email}} ?", "staticAnalysis": {"componentStack": ["UnsubscribeModal", "Modal"]}}, "k_0dp6s9e": {"rawText": "移除后，平台将恢复向该邮箱发送邮件。请谨慎操作！", "staticAnalysis": {"componentStack": ["UnsubscribeModal", "Modal", "Text"]}}, "k_1e0lhgq": {"rawText": "该模板尚未通过审核", "staticAnalysis": {"componentStack": ["useFetchTemplates", "fetchTemplates", "newList"]}}, "k_1715qsq": {"rawText": "尚未创建模板", "staticAnalysis": {"componentStack": ["useFetchTemplates", "fetchTemplates"]}}, "k_0ckcv4a": {"rawText": "发信数量", "staticAnalysis": {"componentStack": ["StatusTable", "div.black-list-table", "Card", "Table"]}}, "k_0claj39": {"rawText": "送达数量", "staticAnalysis": {"componentStack": ["StatsPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_0ckd8bf": {"rawText": "退信数量", "staticAnalysis": {"componentStack": ["StatusTable", "div", "Card", "Table"]}}, "k_1xslosl": {"rawText": "拒绝发送数量", "staticAnalysis": {"componentStack": ["StatsPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_1kjrza1": {"rawText": "队列实时状态仅展示最近15分钟的状态，每10秒钟聚合一次。", "staticAnalysis": {"componentStack": ["StatusTable", "div.black-list-table", "<PERSON><PERSON>", "p"]}}, "k_002qrgn": {"rawText": "时间", "staticAnalysis": {"componentStack": ["StatusTable", "div.black-list-table", "Card", "Table"]}}, "k_1ch8o42": {"rawText": "队列实时状态", "staticAnalysis": {"componentStack": ["QueueStatusPage", "Layout", "Body", "Content"]}}, "k_002r1jz": {"rawText": "开通", "staticAnalysis": {"componentStack": ["DedicatedIPCard", "Col", "Card", "<PERSON><PERSON>"]}}, "k_0askmaa": {"rawText": "独立IP不受其他用户发信干扰，可以保证发信域名和IP的信誉度，提高邮件到达率，点击了解<1>价格详情</1>", "staticAnalysis": {"componentStack": ["DedicatedIPCard", "Col", "Card", "div"]}}, "k_0qnavzs": {"rawText": "前置条件：已配置至少一个发信域名和一个发信地址，点击<1><0></0></1>", "staticAnalysis": {"componentStack": ["DedicatedIPCard", "Col", "Card", "div"]}}, "k_180u7no": {"rawText": "配置发信域名", "staticAnalysis": {"componentStack": ["DedicatedIPCard", "Col", "Card", "div", "<PERSON><PERSON>", "Slot"]}}, "k_003kivf": {"rawText": "查看", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card", "<PERSON><PERSON>"]}}, "k_0lve5mu": {"rawText": "当前共有{{count}}个独立IP在使用中", "staticAnalysis": {"componentStack": ["DedicatedIPCard", "Col", "Card", "div", "div"]}}, "k_0nnwgjs": {"rawText": "注意：每月最多支持申请3个独立 IP，点击查看", "staticAnalysis": {"componentStack": ["DedicatedIPCard", "Col", "Card", "div", "div"]}}, "k_0fux5it": {"rawText": "申请建议", "staticAnalysis": {"componentStack": ["DedicatedIPCard", "Col", "Card", "div", "div", "<PERSON><PERSON>"]}}, "k_0jjl6fb": {"rawText": "设置自定义域名，域名管理员配置DNS。", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret", "div"]}}, "k_16fy8s6": {"rawText": "立即开始", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret", "div", "Link"]}}, "k_11m83ba": {"rawText": "发信地址配置", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret"]}}, "k_18z43gz": {"rawText": "自定义您需要的发信地址。", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret", "div"]}}, "k_0wlgs9r": {"rawText": "发信模版配置", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret"]}}, "k_0e2br0o": {"rawText": "根据您的需求配置模版内容。", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret", "div"]}}, "k_18q4pq6": {"rawText": "开始发信", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret"]}}, "k_0ckll36": {"rawText": "配置完成后，使用普通发送功能发送您的第一封邮件。", "staticAnalysis": {"componentStack": ["FastKnowStep", "steps", "ret", "div"]}}, "k_0c4p3to": {"rawText": "快速入门", "staticAnalysis": {"componentStack": ["FastKnowStep", "div.fast-know-step-component", "Row", "Col", "H3.my-card__title"]}}, "k_002vfrj": {"rawText": "概览", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content"]}}, "k_0g16c4b": {"rawText": "按量计费", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card"]}}, "k_1eg8zkk": {"rawText": "1000封免费测试，超过免费额度的发送将会根据实际使用量计费，查看<1>价格说明</1>。", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card"]}}, "k_08ghn1k": {"rawText": "当前共有 {{count}} 个发信域名", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card", "p"]}}, "k_05qpion": {"rawText": "每日发送限制：{{formattedCount}}封", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card", "p"]}}, "k_01wqny9": {"rawText": "单域名最高信誉等级是 {{levelCount}} 级", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card", "p"]}}, "k_0udb8jl": {"rawText": "单域名单日最高发信量为 {{emailCount}} 封", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card", "p"]}}, "k_0otohur": {"rawText": "点击查看提升规则", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card", "p", "a"]}}, "k_1fkkfu2": {"rawText": "当前共有 {{count}} 个发信地址", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card"]}}, "k_131j5nv": {"rawText": "发信模板", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content"]}}, "k_164wmg4": {"rawText": "当前共有 {{count}} 个发信模板", "staticAnalysis": {"componentStack": ["SesIndex", "Layout", "Body", "Content", "Row", "Col", "Card"]}}, "k_15lhn7s": {"rawText": "回调地址", "staticAnalysis": {"componentStack": ["SettingPage", "Layout", "Body", "Content"]}}, "k_1uqzsw6": {"rawText": "同1个发信地址只能创建1个“发信地址级”回调。", "staticAnalysis": {"componentStack": ["NewModal", "Modal.edit-domain-modal", "form", "Form", "SesFormItem"]}}, "k_0hsq27o": {"rawText": "确认删除？", "staticAnalysis": {"componentStack": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "Table", "<PERSON><PERSON>"]}}, "k_1ftienj": {"rawText": "发送地址级回调地址", "staticAnalysis": {"componentStack": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "Justify", "H3"]}}, "k_1kq755l": {"rawText": "邮件延迟发送回调", "staticAnalysis": {"componentStack": ["callbackEvent"]}}, "k_1d9635r": {"rawText": "邮件成功发送回调", "staticAnalysis": {"componentStack": ["callbackEvent"]}}, "k_1pi6v4x": {"rawText": "邮件丢弃回调", "staticAnalysis": {"componentStack": ["callbackEvent"]}}, "k_1pcz27n": {"rawText": "邮件打开回调", "staticAnalysis": {"componentStack": ["callbackEvent"]}}, "k_1qw2hky": {"rawText": "邮件点击回调", "staticAnalysis": {"componentStack": ["callbackEvent"]}}, "k_000omo1": {"rawText": "收件服务商拒信回调", "staticAnalysis": {"componentStack": ["callbackEvent"]}}, "k_0nktopl": {"rawText": "收件人举报回调", "staticAnalysis": {"componentStack": ["callbackEvent"]}}, "k_0u04exs": {"rawText": "收件人退订回调", "staticAnalysis": {"componentStack": ["callbackEvent"]}}, "k_0ppk0bq": {"rawText": "回调事件过滤", "staticAnalysis": {"componentStack": ["EventFilterSetting", "Card", "Justify", "H3"]}}, "k_15vp5xk": {"rawText": "回调指南", "staticAnalysis": {"componentStack": ["EventFilterSetting", "Card", "Justify", "ExternalLink"]}}, "k_003m6xh": {"rawText": "设置", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "Modal", "FinalForm", "form", "<PERSON><PERSON>"]}}, "k_077xr6j": {"rawText": "https://abc.def.ghi/jk", "staticAnalysis": {"componentStack": ["SettingForm", "form.send-email-form ", "Form", "SesFormItem", "Input"]}}, "k_0003y9x": {"rawText": "无", "staticAnalysis": {"componentStack": ["SettingForm", "form.send-email-form ", "Form", "SesFormItem", "div.editable-field", "div.editable-field__text"]}}, "k_003rk1s": {"rawText": "保存", "staticAnalysis": {"componentStack": ["SettingForm", "form.send-email-form ", "Button.send-submit-btn"]}}, "k_160hitd": {"rawText": "设置一个回调地址，腾讯云将在产生递送成功、腾讯云拒信、ESP退信、用户打开邮件、点击链接、退订等事件后通知到回调地址。该地址默认为空，不通知。", "staticAnalysis": {"componentStack": ["AttentionTips", "<PERSON><PERSON>"]}}, "k_1o6zqsv": {"rawText": "“发信地址级”回调优先级高于“账户级”回调：", "staticAnalysis": {"componentStack": ["AttentionTips", "<PERSON><PERSON>"]}}, "k_19iazzq": {"rawText": "只能创建1个“账户级”回调；1个发信地址只能创建1个“发信地址级”回调。", "staticAnalysis": {"componentStack": ["AttentionTips", "<PERSON><PERSON>"]}}, "k_1fo9e9f": {"rawText": "既设置“账户级”回调又设置“发信地址级”回调，则“发信地址级”设置仅对该发信地址生效，而“账户级”设置对该发信地址以外的其他发信地址生效；", "staticAnalysis": {"componentStack": ["AttentionTips", "<PERSON><PERSON>", "p"]}}, "k_10is6d7": {"rawText": "仅设置“账户级”回调则对当前账户下所有发信地址生效；", "staticAnalysis": {"componentStack": ["AttentionTips", "<PERSON><PERSON>", "p"]}}, "k_1uq81x7": {"rawText": "仅设置“发信地址级”回调则对该发信地址生效。", "staticAnalysis": {"componentStack": ["AttentionTips", "<PERSON><PERSON>", "p"]}}, "k_11n6sh7": {"rawText": "回调地址配置", "staticAnalysis": {"componentStack": ["SettingPage", "Layout", "Body", "Content", "Tabs"]}}, "k_0xhoyqp": {"rawText": "回调基础配置", "staticAnalysis": {"componentStack": ["SettingPage", "Layout", "Body", "Content", "Tabs"]}}, "k_15wcvlb": {"rawText": "保存成功", "staticAnalysis": {"componentStack": ["SettingPage", "onCreateSubmit"]}}, "k_0odpbha": {"rawText": "账户级回调地址", "staticAnalysis": {"componentStack": ["SettingPage", "Layout", "Body", "Content", "Tabs", "TabPanel", "Card", "h3"]}}, "k_0c7x74q": {"rawText": "成功发送", "staticAnalysis": {"componentStack": ["sceneList"]}}, "k_03edeml": {"rawText": "软弹回", "staticAnalysis": {"componentStack": ["sceneList"]}}, "k_03ee05q": {"rawText": "硬弹回", "staticAnalysis": {"componentStack": ["sceneList"]}}, "k_0s3u5wk": {"rawText": "全局黑名单", "staticAnalysis": {"componentStack": ["sceneList"]}}, "k_13jcj72": {"rawText": "场景模拟", "staticAnalysis": {"componentStack": ["EmailSimulatorForm", "form.send-email-form ", "Form"]}}, "k_0n68af6": {"rawText": "接受者地址", "staticAnalysis": {"componentStack": ["EmailSimulatorForm", "form.send-email-form ", "Form"]}}, "k_15wbkfo": {"rawText": "模拟成功", "staticAnalysis": {"componentStack": ["EmailSimulatorPage", "onCreateSubmit"]}}, "k_0gj5oej": {"rawText": "邮件模拟器", "staticAnalysis": {"componentStack": ["EmailSimulatorPage", "Layout", "Body", "Content"]}}, "k_112qng2": {"rawText": "代码及描述", "staticAnalysis": {"componentStack": ["EmailSimulatorPage", "Layout", "Body", "Content", "Card.send-form-page-card", "Form"]}}, "k_1pbrkyz": {"rawText": "此处收件地址填写上限为20个，不同收件地址请换行", "staticAnalysis": {"componentStack": ["SendEmailForm", "form.send-email-form ", "Form", "SesFormItem"]}}, "k_16lpoxo": {"rawText": "文件大小", "staticAnalysis": {"componentStack": ["TemplateFileUpload", "Upload", "p"]}}, "k_13yz33q": {"rawText": "释放鼠标", "staticAnalysis": {"componentStack": ["TemplateFileUpload", "Upload"]}}, "k_0a61lzz": {"rawText": "拖拽到此区域", "staticAnalysis": {"componentStack": ["TemplateFileUpload", "Upload", "Text"]}}, "k_15wpiai": {"rawText": "发送成功", "staticAnalysis": {"componentStack": ["SingleSendPage", "onCreateSubmit"]}}, "k_0c7ufsd": {"rawText": "邮件发送", "staticAnalysis": {"componentStack": ["SingleSendPage", "Layout", "Body", "Content"]}}, "k_1s4j10w": {"rawText": "开启打开邮件用户数统计", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "getModalTitle"]}}, "k_09zw3kv": {"rawText": "不再统计打开邮件用户数", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "getModalTitle"]}}, "k_0qjk7uz": {"rawText": "开启后，您可在<1>邮件跟踪数据</1>页面查看本腾讯云账号下所有发信域名发送的邮件的打开用户数", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "getModalTitle", "p"]}}, "k_1kbidrz": {"rawText": "关闭后，将不会统计本腾讯云账号下所有发信域名发送的邮件的打开用户数", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "getModalTitle"]}}, "k_0rxmyoy": {"rawText": "开启链接点击用户数统计", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "getModalTitle"]}}, "k_0q11971": {"rawText": "不再统计链接点击用户数", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "getModalTitle"]}}, "k_0laksld": {"rawText": "开启后，您可在<1>邮件跟踪数据</1>页面查看本腾讯云账号下所有发信域名发送的邮件的链接点击用户数", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "getModalTitle", "p"]}}, "k_0os6ph1": {"rawText": "关闭后，将不会统计本腾讯云账号下所有发信域名发送的邮件的链接点击用户数", "staticAnalysis": {"componentStack": ["AccountConfirmDialog", "getModalTitle"]}}, "k_18qrgl2": {"rawText": "账号级跟踪数据统计设置", "staticAnalysis": {"componentStack": ["AccountSettingCard", "Card", "H3"]}}, "k_0cwp8fz": {"rawText": "打开邮件用户数统计", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "Modal", "FinalForm", "form", "Form", "Field"]}}, "k_09o7u99": {"rawText": "点击链接用户数统计", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "Modal", "FinalForm", "form", "Form", "Field"]}}, "k_03eq60o": {"rawText": "已开启", "staticAnalysis": {"componentStack": ["switchOptions"]}}, "k_1yyvzqd": {"rawText": "发信域名级跟踪数据统计设置", "staticAnalysis": {"componentStack": ["DomainSettingTable", "Card", "H3"]}}, "k_12cnryb": {"rawText": "批量设置", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "Modal", "FinalForm", "form", "<PERSON><PERSON>"]}}, "k_180o0c1": {"rawText": "输入发信域名", "staticAnalysis": {"componentStack": ["DomainSettingTable", "Card", "Justify", "SearchBox"]}}, "k_1uec10n": {"rawText": "本次选择的{{count}}个发信域名", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "handlerSubmit"]}}, "k_112oonx": {"rawText": "发信域名：{{domain}}", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "Modal"]}}, "k_0zzl751": {"rawText": "<0><0></0></0><1><0></0></1>", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "handlerSubmit", "div"]}}, "k_1kyoydg": {"rawText": "已开启打开邮件用户数统计能力", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "handlerSubmit", "div", "p", "Slot"]}}, "k_1l9p5g5": {"rawText": "已关闭打开邮件用户数统计能力", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "handlerSubmit", "div", "p", "Slot"]}}, "k_0vgbj2u": {"rawText": "已开启点击链接用户数统计能力", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "handlerSubmit", "div", "p", "Slot"]}}, "k_0ukkv0n": {"rawText": "已关闭点击链接用户数统计能力", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "handlerSubmit", "div", "p", "Slot"]}}, "k_1dqdlmk": {"rawText": "已选择{{count}}个发信域名", "staticAnalysis": {"componentStack": ["EditDomainSettingDialog", "Modal"]}}, "k_003m98p": {"rawText": "按日", "staticAnalysis": {"componentStack": ["timePickerOptions"]}}, "k_003n1ok": {"rawText": "按月", "staticAnalysis": {"componentStack": ["timePickerOptions"]}}, "k_0ld10w7": {"rawText": "请选择时间范围", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable", "<PERSON><PERSON>"]}}, "k_15ibvd9": {"rawText": "邮件类型", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form", "Justify", "Form"]}}, "k_002rhlf": {"rawText": "批量", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form", "Justify", "Form", "Select"]}}, "k_003ps0i": {"rawText": "触发", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form", "Justify", "Form", "Select"]}}, "k_0003zby": {"rawText": "总", "staticAnalysis": {"componentStack": ["StatusTable", "totalData"]}}, "k_003n1un": {"rawText": "日期", "staticAnalysis": {"componentStack": ["StatusTable", "div", "Card", "Table"]}}, "k_0396nh9": {"rawText": "送达率", "staticAnalysis": {"componentStack": ["StatusTable", "div", "Card", "Table"]}}, "k_1xuotg4": {"rawText": "数据统计存在一定时间延迟，实时数据仅供参考使用。", "staticAnalysis": {"componentStack": ["StatusTable", "div", "<PERSON><PERSON>"]}}, "k_1pkkoav": {"rawText": "邮件跟踪数据统计，支持对账号级及域名级自助设置 是否统计邮件打开用户及点击用户数，<1>去设置</1>。", "staticAnalysis": {"componentStack": ["StatusTable", "div", "<PERSON><PERSON>", "List"]}}, "k_0hts7rv": {"rawText": "如设置关闭邮件的跟踪数据统计能力，则对应账号或发信域名的邮件打开用户数或点击链接用户数不会被统计。", "staticAnalysis": {"componentStack": ["StatusTable", "div", "<PERSON><PERSON>", "List"]}}, "k_00042hl": {"rawText": "值", "staticAnalysis": {"componentStack": ["StatusTable", "div", "Card", "BasicLine"]}}, "k_17xmxqu": {"rawText": "输入收信域名", "staticAnalysis": {"componentStack": ["StatsSearchQuery", "form.stats-search-query", "Justify", "Form", "Input"]}}, "k_0cl5bxm": {"rawText": "发送数量", "staticAnalysis": {"componentStack": ["StatsPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_03vsgdv": {"rawText": "点击链接用户数", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_1xf76jl": {"rawText": "打开邮件用户数", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_0ck18xd": {"rawText": "调用数量", "staticAnalysis": {"componentStack": ["StatusTable", "div", "Card", "Table"]}}, "k_0clb83c": {"rawText": "退订数量", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_0iq3dh9": {"rawText": "打开邮件的用户数量，根据收件人去重", "staticAnalysis": {"componentStack": ["StatusTable", "div", "Card", "Table", "Bubble"]}}, "k_02s10wr": {"rawText": "垃圾投诉数量", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_1fxv0c6": {"rawText": "邮件跟踪数据", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content"]}}, "k_1vx0d6p": {"rawText": "打开邮件用户数<1><0></0></1>", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_1dk7itf": {"rawText": "仅开启了“打开邮件用户数”统计能力才会被统计，可在<1>统计设置页</1>查看是否开启。", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable", "Bubble"]}}, "k_04hvg7n": {"rawText": "点击链接用户数<1><0></0></1>", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_07jlccx": {"rawText": "仅开启了“点击链接用户数”统计能力才会被统计，可在<1>统计设置页</1>查看是否开启。", "staticAnalysis": {"componentStack": ["MailTrackingPage", "Layout", "Body", "Content", "NewStatusTable", "Bubble"]}}, "k_12clex7": {"rawText": "统计设置", "staticAnalysis": {"componentStack": ["StatisticsSettingPage", "Layout", "Body", "Content"]}}, "k_1a1iblm": {"rawText": "计费邮件数量", "staticAnalysis": {"componentStack": ["StatsPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_1655hqa": {"rawText": "无效地址数量", "staticAnalysis": {"componentStack": ["StatsPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_0dewb0e": {"rawText": "垃圾邮件退信数量", "staticAnalysis": {"componentStack": ["StatsPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_0bbrtms": {"rawText": "其他原因退信数量", "staticAnalysis": {"componentStack": ["StatsPage", "Layout", "Body", "Content", "NewStatusTable"]}}, "k_13qdg7f": {"rawText": "发信数据", "staticAnalysis": {"componentStack": ["StatsPage", "Layout", "Body", "Content"]}}, "k_0nk5rm6": {"rawText": "提交失败，可能由于模板文件过大，请使用API调用的方式上传。", "staticAnalysis": {"componentStack": ["addTemplate"]}}, "k_009fkbr": {"rawText": "请输入模板名称", "staticAnalysis": {"componentStack": ["NewTemplateModal"]}}, "k_0zri0bi": {"rawText": "模板名称过长", "staticAnalysis": {"componentStack": ["NewTemplateModal"]}}, "k_1gt20x5": {"rawText": "请上传文件", "staticAnalysis": {"componentStack": ["NewTemplateModal"]}}, "k_02y5za0": {"rawText": "请输入邮件正文", "staticAnalysis": {"componentStack": ["NewTemplateModal"]}}, "k_1d252g7": {"rawText": "已上传的模板", "staticAnalysis": {"componentStack": ["NewTemplateModal"]}}, "k_1r76n8v": {"rawText": "邮件模板预览", "staticAnalysis": {"componentStack": ["NewTemplateModal", "previewHtmlTemplate", "modal"]}}, "k_150dmu8": {"rawText": "邮件模板详情", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal"]}}, "k_15bv0kp": {"rawText": "新建邮件模板", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal"]}}, "k_11k3gau": {"rawText": "模板名称", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_15ijusr": {"rawText": "模板类型", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal", "form", "Form"]}}, "k_0k8uwsf": {"rawText": "HTML富文本", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal", "form", "Form", "Segment"]}}, "k_03c475t": {"rawText": "纯文本", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal", "form", "Form", "Segment"]}}, "k_12g1oux": {"rawText": "邮件正文", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal", "form", "Form", "SesFormItem"]}}, "k_0fvugro": {"rawText": "邮件摘要", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal", "form", "Form"]}}, "k_1atf9he": {"rawText": "邮件内容中的变量使用{{变量名}}表示，如：尊敬的{{name}}。变量名仅支持大小写字母（a-z、A-Z）、数字（0-9）和下划线。", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal", "form", "Form"]}}, "k_002uzrd": {"rawText": "预览", "staticAnalysis": {"componentStack": ["NewTemplateModal", "Modal.edit-template-modal", "form", "<PERSON><PERSON>"]}}, "k_1653zpr": {"rawText": "请上传HTML文件。HTML文件仅支持UTF-8编码，否则会乱码。", "staticAnalysis": {"componentStack": ["TemplateFileUpload"]}}, "k_00rdj04": {"rawText": "文件大小不超过{{size}}。", "staticAnalysis": {"componentStack": ["TemplateFileUpload"]}}, "k_131lpy2": {"rawText": "创建模板", "staticAnalysis": {"componentStack": ["TemplatePage", "onCreateSubmit"]}}, "k_0s6pypu": {"rawText": "您的模板申请已提交成功，预计在一个工作日内完成审核（周末、节假日顺延），感谢您的耐心等待。", "staticAnalysis": {"componentStack": ["TemplatePage", "onCreateSubmit"]}}, "k_0yslfz1": {"rawText": "确认删除当前所选模板？", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_1y3su2v": {"rawText": "删除后，不能再用该模板发送邮件。", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "<PERSON><PERSON>", "yes"]}}, "k_0yld1cr": {"rawText": "模板提交后，预计1个工作日内完成审核（周末、节假日顺延）；", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "<PERSON><PERSON>", "List"]}}, "k_010h52g": {"rawText": "为了保证模板通过率，模板内容必须体现实际业务且需要遵循<1>邮件模板内容规范</1>。", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "<PERSON><PERSON>", "List"]}}, "k_0yj8k52": {"rawText": "模板id", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_13w75f0": {"rawText": "当前状态", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table"]}}, "k_1bz0q12": {"rawText": "模板状态，审核通过才可以使用此模板发送邮件", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "Bubble"]}}, "k_0f7zpcx": {"rawText": "审核通过", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "span"]}}, "k_03i7qq9": {"rawText": "审核中", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "Text"]}}, "k_065n8bk": {"rawText": "模板提交后，预计1个工作日内完成审核（周末、节假日顺延），感谢您的耐心等待。", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "Text", "Bubble"]}}, "k_054aa2z": {"rawText": "审核未通过", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "span.template-status template-status__forbidden"]}}, "k_13we9fh": {"rawText": "未知状态", "staticAnalysis": {"componentStack": ["TemplatePage", "Layout", "Body", "Content", "Card", "Table", "span"]}}, "k_1cyubfw": {"rawText": "文件读取失败，请重试。", "staticAnalysis": {"componentStack": ["readFileAsync"]}}, "k_0xfiovd": {"rawText": "收件邮箱地址为必填条件", "staticAnalysis": {"componentStack": ["validateEmail"]}}, "k_1du7ojz": {"rawText": "邮箱地址格式错误", "staticAnalysis": {"componentStack": ["validateEmail"]}}, "k_0extds1": {"rawText": "第{{unvalidIndex}}行格式错误，请重新输入。", "staticAnalysis": {"componentStack": ["validateDestination"]}}}