/**
 * Tea 项目配置
 * @type {import("@tencent/tea-types/config").Configuration}
 */
module.exports = {
  dict: {
    token: 'B0yZRpEt_QCugoFqOMgM6PjFnsfRFKa6fn6GVeEI',
  },
  buffet: {
    // 要自动化提单的产品 ID，请在 http://yehe.isd.com/buffet/product 中查询确认
    productId: 1364,
    zh: [
      { site: 1, route: '/ses' },
      { site: 2, route: '/ses' },
    ],
    en: [{ site: 2, route: '/ses' }],
    // 要自动化提单到的路由
    // changes: (js, css) => {
    //   console.log("111", js, css);
    //   return [];
    // },
  },
};
