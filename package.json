{"name": "@tencent/tea-app-ses", "version": "1.0.0", "description": "The ses tea app for qcloud console", "main": "src/app.js", "resolutions": {"glob-parent": "^5.1.2"}, "scripts": {"preinstall": "npx npm-force-resolutions", "test": "echo \"Error: no test specified\" && exit 1", "dev": "tea dev --port 8087", "dev-en": "tea dev  --port 8087 -l en", "scan": "tea scan", "build": "tea build", "deploy": "cross-env NODE_OPTIONS=--openssl-legacy-provider NODE_ENV=production tea commit -l zh,en -m 'feat 体验链接，不发布'", "buildUntranslatedFile": "node ./slot_modules/t.js", "deploy:zh": "cross-env NODE_OPTIONS=--openssl-legacy-provider NODE_ENV=production tea commit -l zh -m 'feat 独立IP体验链接'", "update": "tea update"}, "keywords": ["tea", "app", "ses"], "engines": {"typescript": ">3.3"}, "license": "UNLICENSED", "dependencies": {"@loadable/component": "^5.16.7", "@tencent/tea-app": "^2.1.15", "@tencent/tea-chart": "^2.4.7", "@tencent/tea-component": "^2.8.5", "@tencent/tea-icons-react": "^1.0.51", "@tencent/tea-material-pro-form": "^0.2.2", "@tencent/tea-material-pro-table": "^1.0.0", "cos-js-sdk-v5": "^1.8.4", "crypto-js": "^4.0.0", "final-form": "^4.20.1", "final-form-arrays": "^3.1.0", "i18next": "^20.3.1", "lodash": "^4.17.21", "moment": "^2.29.2", "react": "^16.8.3", "react-dom": "^16.8.3", "react-final-form": "^6.5.9", "react-final-form-arrays": "^3.1.4", "react-final-form-hooks": "^2.0.2", "react-redux": "^7.2.1", "react-router-dom": "^5.2.0", "react-use": "^17.5.1", "redux-thunk": "^2.3.0"}, "devDependencies": {"@tencent/eslint-config-tencent": "^0.16.0", "@tencent/eslint-plugin-tea-i18n": "^0.1.16", "@tencent/tea-scripts": "^2.1.24", "@tencent/tea-types": "^0.1.15", "@types/react": "^16.8.4", "@types/react-dom": "^16.8.2", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "eslint": "^7.31.0", "eslint-config-prettier": "^8.3.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.8.2", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "prettier": "^2.7.1", "typescript": "^4.7.4"}}